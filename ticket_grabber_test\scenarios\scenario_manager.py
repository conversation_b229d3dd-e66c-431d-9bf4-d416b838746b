"""
场景管理器

负责场景的加载、执行、监控和结果管理
"""

import json
import time
import threading
import asyncio
from typing import Dict, List, Any, Optional, Callable, Union
from pathlib import Path
from concurrent.futures import ThreadPoolExecutor, as_completed
import uuid
from datetime import datetime

from .base_scenario import (
    BaseScenario, ScenarioConfig, AttackType, ScenarioStatus, 
    ScenarioResult, ScenarioMetrics
)
from .attack_scenarios import (
    BasicAttackScenario, AdvancedAttackScenario, 
    DistributedAttackScenario, AdversarialAttackScenario
)

try:
    from ..core.logger import get_logger
    from ..config.settings import config_manager
except ImportError:
    import logging
    def get_logger(name):
        return logging.getLogger(name)
    
    class SimpleConfig:
        def get(self, key, default=None):
            return default
    
    config_manager = SimpleConfig()


class ScenarioManager:
    """
    场景管理器
    
    负责场景的创建、执行、监控和结果管理
    """
    
    def __init__(self, config_file: str = None):
        """
        初始化场景管理器
        
        Args:
            config_file: 配置文件路径
        """
        self.logger = get_logger("ScenarioManager")
        
        # 配置管理
        self.config_file = config_file or "config/test_scenarios.json"
        self.config = self._load_config()
        
        # 场景注册表
        self.scenario_classes = {
            AttackType.BASIC: BasicAttackScenario,
            AttackType.ADVANCED: AdvancedAttackScenario,
            AttackType.DISTRIBUTED: DistributedAttackScenario,
            AttackType.ADVERSARIAL: AdversarialAttackScenario,
            AttackType.BEHAVIORAL: AdvancedAttackScenario,  # 使用高级场景
            AttackType.HYBRID: AdvancedAttackScenario  # 使用高级场景
        }
        
        # 运行时状态
        self.active_scenarios: Dict[str, BaseScenario] = {}
        self.completed_scenarios: Dict[str, BaseScenario] = {}
        self.execution_history: List[Dict[str, Any]] = []
        
        # 执行控制
        self.max_concurrent = self.config.get('execution_settings', {}).get('max_concurrent_scenarios', 3)
        self.executor = ThreadPoolExecutor(max_workers=self.max_concurrent)
        self.execution_lock = threading.Lock()
        
        # 回调函数
        self.on_scenario_start: Optional[Callable] = None
        self.on_scenario_complete: Optional[Callable] = None
        self.on_scenario_error: Optional[Callable] = None
        self.on_batch_complete: Optional[Callable] = None
        
        self.logger.info("场景管理器初始化完成")
    
    def _load_config(self) -> Dict[str, Any]:
        """加载配置文件"""
        try:
            config_path = Path(self.config_file)
            if not config_path.exists():
                self.logger.warning(f"配置文件不存在: {self.config_file}")
                return self._get_default_config()
            
            with open(config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            self.logger.info(f"配置文件加载成功: {self.config_file}")
            return config
            
        except Exception as e:
            self.logger.error(f"配置文件加载失败: {e}")
            return self._get_default_config()
    
    def _get_default_config(self) -> Dict[str, Any]:
        """获取默认配置"""
        return {
            "scenarios": {},
            "scenario_groups": {},
            "default_config": {
                "target_url": "http://localhost:8080/api/tickets",
                "max_requests": 50,
                "concurrent_users": 1,
                "request_interval": 1.0,
                "timeout": 10.0,
                "use_proxy": False,
                "use_captcha_solver": False,
                "use_behavior_sim": False,
                "use_anti_detection": True
            },
            "execution_settings": {
                "max_concurrent_scenarios": 3,
                "scenario_timeout": 300,
                "cleanup_on_failure": True,
                "export_results": True
            }
        }
    
    def list_scenarios(self) -> List[str]:
        """列出所有可用场景"""
        return list(self.config.get('scenarios', {}).keys())
    
    def list_scenario_groups(self) -> List[str]:
        """列出所有场景组"""
        return list(self.config.get('scenario_groups', {}).keys())
    
    def get_scenario_config(self, scenario_name: str) -> Optional[Dict[str, Any]]:
        """获取场景配置"""
        scenarios = self.config.get('scenarios', {})
        return scenarios.get(scenario_name)
    
    def get_scenario_group(self, group_name: str) -> Optional[List[str]]:
        """获取场景组"""
        groups = self.config.get('scenario_groups', {})
        return groups.get(group_name)
    
    def create_scenario(self, scenario_name: str, custom_config: Dict[str, Any] = None) -> Optional[BaseScenario]:
        """
        创建场景实例
        
        Args:
            scenario_name: 场景名称
            custom_config: 自定义配置（覆盖默认配置）
            
        Returns:
            BaseScenario: 场景实例
        """
        try:
            # 获取场景配置
            scenario_config = self.get_scenario_config(scenario_name)
            if not scenario_config:
                self.logger.error(f"场景配置不存在: {scenario_name}")
                return None
            
            # 合并默认配置
            default_config = self.config.get('default_config', {})
            merged_config = {**default_config, **scenario_config}
            
            # 应用自定义配置
            if custom_config:
                merged_config.update(custom_config)
            
            # 创建配置对象
            config = ScenarioConfig(
                name=merged_config.get('name', scenario_name),
                description=merged_config.get('description', ''),
                attack_type=AttackType(merged_config.get('attack_type', 'basic')),
                target_url=merged_config.get('target_url'),
                max_requests=merged_config.get('max_requests', 50),
                concurrent_users=merged_config.get('concurrent_users', 1),
                request_interval=merged_config.get('request_interval', 1.0),
                timeout=merged_config.get('timeout', 10.0),
                use_proxy=merged_config.get('use_proxy', False),
                use_captcha_solver=merged_config.get('use_captcha_solver', False),
                use_behavior_sim=merged_config.get('use_behavior_sim', False),
                use_anti_detection=merged_config.get('use_anti_detection', True),
                custom_params=merged_config.get('custom_params', {}),
                success_criteria=merged_config.get('success_criteria', {}),
                failure_criteria=merged_config.get('failure_criteria', {})
            )
            
            # 获取场景类
            scenario_class = self.scenario_classes.get(config.attack_type)
            if not scenario_class:
                self.logger.error(f"不支持的攻击类型: {config.attack_type}")
                return None
            
            # 创建场景实例
            scenario = scenario_class(config)
            
            # 设置回调
            scenario.on_start = self._on_scenario_start
            scenario.on_complete = self._on_scenario_complete
            scenario.on_error = self._on_scenario_error
            
            self.logger.info(f"场景创建成功: {scenario_name}")
            return scenario
            
        except Exception as e:
            self.logger.error(f"场景创建失败: {scenario_name}, 错误: {e}")
            return None
    
    def execute_scenario(self, scenario: BaseScenario, blocking: bool = False) -> bool:
        """
        执行单个场景
        
        Args:
            scenario: 场景实例
            blocking: 是否阻塞执行
            
        Returns:
            bool: 是否成功启动
        """
        try:
            with self.execution_lock:
                if len(self.active_scenarios) >= self.max_concurrent:
                    self.logger.warning("达到最大并发场景数限制")
                    return False
                
                self.active_scenarios[scenario.scenario_id] = scenario
            
            # 启动场景
            success = scenario.execute(blocking=blocking)
            
            if not success:
                with self.execution_lock:
                    self.active_scenarios.pop(scenario.scenario_id, None)
            
            return success
            
        except Exception as e:
            self.logger.error(f"场景执行失败: {e}")
            with self.execution_lock:
                self.active_scenarios.pop(scenario.scenario_id, None)
            return False
    
    def execute_scenario_by_name(self, scenario_name: str, custom_config: Dict[str, Any] = None, blocking: bool = False) -> Optional[BaseScenario]:
        """
        根据名称执行场景
        
        Args:
            scenario_name: 场景名称
            custom_config: 自定义配置
            blocking: 是否阻塞执行
            
        Returns:
            BaseScenario: 场景实例（如果成功启动）
        """
        scenario = self.create_scenario(scenario_name, custom_config)
        if not scenario:
            return None
        
        success = self.execute_scenario(scenario, blocking)
        if success:
            return scenario
        else:
            return None
    
    def execute_scenario_group(self, group_name: str, custom_config: Dict[str, Any] = None, sequential: bool = False) -> List[BaseScenario]:
        """
        执行场景组
        
        Args:
            group_name: 场景组名称
            custom_config: 自定义配置
            sequential: 是否顺序执行
            
        Returns:
            List[BaseScenario]: 成功启动的场景列表
        """
        scenario_names = self.get_scenario_group(group_name)
        if not scenario_names:
            self.logger.error(f"场景组不存在: {group_name}")
            return []
        
        executed_scenarios = []
        
        for scenario_name in scenario_names:
            if sequential:
                # 顺序执行：等待前一个完成
                scenario = self.execute_scenario_by_name(scenario_name, custom_config, blocking=True)
            else:
                # 并发执行
                scenario = self.execute_scenario_by_name(scenario_name, custom_config, blocking=False)
            
            if scenario:
                executed_scenarios.append(scenario)
            else:
                self.logger.warning(f"场景执行失败: {scenario_name}")
        
        self.logger.info(f"场景组执行完成: {group_name}, 成功启动 {len(executed_scenarios)} 个场景")
        return executed_scenarios
    
    def execute_batch(self, scenario_configs: List[Dict[str, Any]], sequential: bool = False) -> List[BaseScenario]:
        """
        批量执行场景
        
        Args:
            scenario_configs: 场景配置列表，每个配置包含 'name' 和可选的 'config'
            sequential: 是否顺序执行
            
        Returns:
            List[BaseScenario]: 成功启动的场景列表
        """
        executed_scenarios = []
        
        for scenario_config in scenario_configs:
            scenario_name = scenario_config.get('name')
            custom_config = scenario_config.get('config', {})
            
            if not scenario_name:
                self.logger.warning("场景配置缺少名称，跳过")
                continue
            
            if sequential:
                scenario = self.execute_scenario_by_name(scenario_name, custom_config, blocking=True)
            else:
                scenario = self.execute_scenario_by_name(scenario_name, custom_config, blocking=False)
            
            if scenario:
                executed_scenarios.append(scenario)
        
        self.logger.info(f"批量执行完成，成功启动 {len(executed_scenarios)} 个场景")
        
        # 调用批量完成回调
        if self.on_batch_complete:
            self.on_batch_complete(executed_scenarios)
        
        return executed_scenarios
    
    def stop_scenario(self, scenario_id: str) -> bool:
        """停止场景"""
        scenario = self.active_scenarios.get(scenario_id)
        if scenario:
            scenario.stop()
            return True
        return False
    
    def pause_scenario(self, scenario_id: str) -> bool:
        """暂停场景"""
        scenario = self.active_scenarios.get(scenario_id)
        if scenario:
            scenario.pause()
            return True
        return False
    
    def resume_scenario(self, scenario_id: str) -> bool:
        """恢复场景"""
        scenario = self.active_scenarios.get(scenario_id)
        if scenario:
            scenario.resume()
            return True
        return False
    
    def get_scenario_status(self, scenario_id: str) -> Optional[Dict[str, Any]]:
        """获取场景状态"""
        scenario = self.active_scenarios.get(scenario_id) or self.completed_scenarios.get(scenario_id)
        if scenario:
            return scenario.get_progress()
        return None
    
    def list_active_scenarios(self) -> List[Dict[str, Any]]:
        """列出活动场景"""
        return [scenario.get_progress() for scenario in self.active_scenarios.values()]
    
    def list_completed_scenarios(self) -> List[Dict[str, Any]]:
        """列出已完成场景"""
        return [scenario.get_summary() for scenario in self.completed_scenarios.values()]
    
    def wait_for_completion(self, scenario_ids: List[str] = None, timeout: float = None) -> bool:
        """
        等待场景完成
        
        Args:
            scenario_ids: 要等待的场景ID列表，None表示等待所有活动场景
            timeout: 超时时间
            
        Returns:
            bool: 是否在超时前完成
        """
        if scenario_ids is None:
            scenarios_to_wait = list(self.active_scenarios.values())
        else:
            scenarios_to_wait = [self.active_scenarios[sid] for sid in scenario_ids if sid in self.active_scenarios]
        
        start_time = time.time()
        
        for scenario in scenarios_to_wait:
            remaining_timeout = None
            if timeout:
                elapsed = time.time() - start_time
                remaining_timeout = max(0, timeout - elapsed)
                if remaining_timeout <= 0:
                    return False
            
            if not scenario.wait_for_completion(remaining_timeout):
                return False
        
        return True
    
    def cleanup_completed_scenarios(self):
        """清理已完成的场景"""
        completed_ids = []
        
        for scenario_id, scenario in self.active_scenarios.items():
            if scenario.status in [ScenarioStatus.COMPLETED, ScenarioStatus.FAILED, ScenarioStatus.CANCELLED]:
                completed_ids.append(scenario_id)
        
        for scenario_id in completed_ids:
            scenario = self.active_scenarios.pop(scenario_id)
            self.completed_scenarios[scenario_id] = scenario
        
        self.logger.info(f"清理了 {len(completed_ids)} 个已完成场景")
    
    def export_results(self, output_dir: str = "results", format: str = "json"):
        """
        导出所有结果
        
        Args:
            output_dir: 输出目录
            format: 导出格式
        """
        try:
            output_path = Path(output_dir)
            output_path.mkdir(parents=True, exist_ok=True)
            
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            
            # 导出活动场景
            for scenario in self.active_scenarios.values():
                filename = f"{scenario.config.name}_{scenario.scenario_id[:8]}_{timestamp}.{format}"
                filepath = output_path / filename
                scenario.export_results(str(filepath))
            
            # 导出已完成场景
            for scenario in self.completed_scenarios.values():
                filename = f"{scenario.config.name}_{scenario.scenario_id[:8]}_{timestamp}.{format}"
                filepath = output_path / filename
                scenario.export_results(str(filepath))
            
            # 导出汇总报告
            summary_file = output_path / f"summary_{timestamp}.{format}"
            self._export_summary(str(summary_file))
            
            self.logger.info(f"结果导出完成: {output_dir}")
            
        except Exception as e:
            self.logger.error(f"结果导出失败: {e}")
    
    def _export_summary(self, filepath: str):
        """导出汇总报告"""
        try:
            summary = {
                'timestamp': datetime.now().isoformat(),
                'total_scenarios': len(self.active_scenarios) + len(self.completed_scenarios),
                'active_scenarios': len(self.active_scenarios),
                'completed_scenarios': len(self.completed_scenarios),
                'execution_history': self.execution_history,
                'scenarios': {
                    'active': [scenario.get_progress() for scenario in self.active_scenarios.values()],
                    'completed': [scenario.get_summary() for scenario in self.completed_scenarios.values()]
                }
            }
            
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(summary, f, indent=2, ensure_ascii=False)
            
        except Exception as e:
            self.logger.error(f"汇总报告导出失败: {e}")
    
    def _on_scenario_start(self, scenario: BaseScenario):
        """场景开始回调"""
        self.execution_history.append({
            'timestamp': time.time(),
            'event': 'scenario_start',
            'scenario_id': scenario.scenario_id,
            'scenario_name': scenario.config.name
        })
        
        if self.on_scenario_start:
            self.on_scenario_start(scenario)
    
    def _on_scenario_complete(self, scenario: BaseScenario):
        """场景完成回调"""
        self.execution_history.append({
            'timestamp': time.time(),
            'event': 'scenario_complete',
            'scenario_id': scenario.scenario_id,
            'scenario_name': scenario.config.name,
            'result': scenario.result.value if scenario.result else None,
            'duration': scenario.metrics.duration
        })
        
        # 移动到已完成列表
        with self.execution_lock:
            if scenario.scenario_id in self.active_scenarios:
                self.active_scenarios.pop(scenario.scenario_id)
                self.completed_scenarios[scenario.scenario_id] = scenario
        
        if self.on_scenario_complete:
            self.on_scenario_complete(scenario)
    
    def _on_scenario_error(self, scenario: BaseScenario, error_message: str):
        """场景错误回调"""
        self.execution_history.append({
            'timestamp': time.time(),
            'event': 'scenario_error',
            'scenario_id': scenario.scenario_id,
            'scenario_name': scenario.config.name,
            'error': error_message
        })
        
        if self.on_scenario_error:
            self.on_scenario_error(scenario, error_message)
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息"""
        total_scenarios = len(self.active_scenarios) + len(self.completed_scenarios)
        
        # 计算成功率
        completed_scenarios = list(self.completed_scenarios.values())
        successful_scenarios = sum(1 for s in completed_scenarios if s.result == ScenarioResult.SUCCESS)
        success_rate = successful_scenarios / len(completed_scenarios) if completed_scenarios else 0
        
        # 计算平均执行时间
        durations = [s.metrics.duration for s in completed_scenarios if s.metrics.end_time]
        avg_duration = sum(durations) / len(durations) if durations else 0
        
        # 统计攻击类型
        attack_type_stats = {}
        for scenario in list(self.active_scenarios.values()) + completed_scenarios:
            attack_type = scenario.config.attack_type.value
            attack_type_stats[attack_type] = attack_type_stats.get(attack_type, 0) + 1
        
        return {
            'total_scenarios': total_scenarios,
            'active_scenarios': len(self.active_scenarios),
            'completed_scenarios': len(self.completed_scenarios),
            'success_rate': success_rate,
            'average_duration': avg_duration,
            'attack_type_distribution': attack_type_stats,
            'execution_events': len(self.execution_history)
        }
    
    def shutdown(self):
        """关闭管理器"""
        self.logger.info("正在关闭场景管理器...")
        
        # 停止所有活动场景
        for scenario in list(self.active_scenarios.values()):
            scenario.stop()
        
        # 等待场景完成
        self.wait_for_completion(timeout=30)
        
        # 关闭线程池
        self.executor.shutdown(wait=True)
        
        # 导出结果
        if self.config.get('execution_settings', {}).get('export_results', True):
            self.export_results()
        
        self.logger.info("场景管理器已关闭")
    
    def __enter__(self):
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        self.shutdown()


# 便捷函数
def create_scenario_manager(config_file: str = None) -> ScenarioManager:
    """创建场景管理器"""
    return ScenarioManager(config_file)


def execute_single_scenario(scenario_name: str, config_file: str = None, custom_config: Dict[str, Any] = None) -> Optional[BaseScenario]:
    """执行单个场景的便捷函数"""
    with create_scenario_manager(config_file) as manager:
        return manager.execute_scenario_by_name(scenario_name, custom_config, blocking=True)


def execute_scenario_group(group_name: str, config_file: str = None, sequential: bool = False) -> List[BaseScenario]:
    """执行场景组的便捷函数"""
    with create_scenario_manager(config_file) as manager:
        scenarios = manager.execute_scenario_group(group_name, sequential=sequential)
        manager.wait_for_completion()
        return scenarios
"""
配置管理器

支持分层配置加载、验证和敏感信息加密的配置管理系统。
"""

import json
import os
import logging
from typing import Dict, Any, Optional, Union
from pathlib import Path
try:
    from cryptography.fernet import Fernet
    import base64
    ENCRYPTION_AVAILABLE = True
except ImportError:
    ENCRYPTION_AVAILABLE = False
    Fernet = None


class ConfigManager:
    """配置管理器类"""
    
    def __init__(self, config_dir: str = None):
        """
        初始化配置管理器
        
        Args:
            config_dir: 配置文件目录路径
        """
        self.config_dir = Path(config_dir) if config_dir else Path(__file__).parent
        self.config_data = {}
        self.encryption_key = None
        self.logger = logging.getLogger(__name__)
        
        # 配置文件优先级（从低到高）
        self.config_files = [
            'default_config.json',      # 默认配置
            'environment_config.json',  # 环境配置
            'local_config.json'         # 本地配置
        ]
        
        self._load_encryption_key()
        self._load_all_configs()
    
    def _load_encryption_key(self):
        """加载或生成加密密钥"""
        if not ENCRYPTION_AVAILABLE:
            self.encryption_key = None
            return

        key_file = self.config_dir / '.encryption_key'

        if key_file.exists():
            with open(key_file, 'rb') as f:
                self.encryption_key = f.read()
        else:
            # 生成新的加密密钥
            self.encryption_key = Fernet.generate_key()
            with open(key_file, 'wb') as f:
                f.write(self.encryption_key)
            # 设置文件权限（仅所有者可读写）
            os.chmod(key_file, 0o600)
    
    def _load_all_configs(self):
        """加载所有配置文件"""
        for config_file in self.config_files:
            config_path = self.config_dir / config_file
            if config_path.exists():
                try:
                    self._load_config_file(config_path)
                    self.logger.info(f"已加载配置文件: {config_file}")
                except Exception as e:
                    self.logger.error(f"加载配置文件失败 {config_file}: {e}")
    
    def _load_config_file(self, config_path: Path):
        """加载单个配置文件"""
        with open(config_path, 'r', encoding='utf-8') as f:
            config = json.load(f)
            self._merge_config(config)
    
    def _merge_config(self, new_config: Dict[str, Any]):
        """合并配置数据"""
        def deep_merge(base: Dict, update: Dict) -> Dict:
            for key, value in update.items():
                if key in base and isinstance(base[key], dict) and isinstance(value, dict):
                    deep_merge(base[key], value)
                else:
                    base[key] = value
            return base
        
        deep_merge(self.config_data, new_config)
    
    def get(self, key: str, default: Any = None) -> Any:
        """
        获取配置值
        
        Args:
            key: 配置键，支持点号分隔的嵌套键
            default: 默认值
            
        Returns:
            配置值
        """
        keys = key.split('.')
        value = self.config_data
        
        for k in keys:
            if isinstance(value, dict) and k in value:
                value = value[k]
            else:
                return default
        
        # 如果是加密值，尝试解密
        if isinstance(value, str) and value.startswith('encrypted:'):
            return self._decrypt_value(value)
        
        return value
    
    def set(self, key: str, value: Any, encrypt: bool = False):
        """
        设置配置值
        
        Args:
            key: 配置键
            value: 配置值
            encrypt: 是否加密存储
        """
        keys = key.split('.')
        config = self.config_data
        
        # 导航到目标位置
        for k in keys[:-1]:
            if k not in config:
                config[k] = {}
            config = config[k]
        
        # 设置值
        if encrypt:
            config[keys[-1]] = self._encrypt_value(value)
        else:
            config[keys[-1]] = value
    
    def _encrypt_value(self, value: str) -> str:
        """加密配置值"""
        if not ENCRYPTION_AVAILABLE or self.encryption_key is None:
            self.logger.warning("加密功能不可用，返回原始值")
            return str(value)

        if not isinstance(value, str):
            value = str(value)

        fernet = Fernet(self.encryption_key)
        encrypted_bytes = fernet.encrypt(value.encode())
        encrypted_str = base64.b64encode(encrypted_bytes).decode()
        return f"encrypted:{encrypted_str}"
    
    def _decrypt_value(self, encrypted_value: str) -> str:
        """解密配置值"""
        if not ENCRYPTION_AVAILABLE or self.encryption_key is None:
            self.logger.warning("加密功能不可用，返回原始值")
            return encrypted_value

        try:
            # 移除 'encrypted:' 前缀
            encrypted_str = encrypted_value[10:]
            encrypted_bytes = base64.b64decode(encrypted_str.encode())

            fernet = Fernet(self.encryption_key)
            decrypted_bytes = fernet.decrypt(encrypted_bytes)
            return decrypted_bytes.decode()
        except Exception as e:
            self.logger.error(f"解密配置值失败: {e}")
            return encrypted_value
    
    def validate_config(self) -> bool:
        """验证配置完整性"""
        required_keys = [
            'system.name',
            'system.version',
            'logging.level',
            'logging.file_path'
        ]
        
        for key in required_keys:
            if self.get(key) is None:
                self.logger.error(f"缺少必需的配置项: {key}")
                return False
        
        return True
    
    def save_config(self, config_type: str = 'local'):
        """
        保存配置到文件
        
        Args:
            config_type: 配置类型 ('local', 'environment')
        """
        if config_type == 'local':
            config_file = self.config_dir / 'local_config.json'
        elif config_type == 'environment':
            config_file = self.config_dir / 'environment_config.json'
        else:
            raise ValueError(f"不支持的配置类型: {config_type}")
        
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(self.config_data, f, indent=2, ensure_ascii=False)
        
        self.logger.info(f"配置已保存到: {config_file}")
    
    def get_all_config(self) -> Dict[str, Any]:
        """获取所有配置数据"""
        return self.config_data.copy()
    
    def reload_config(self):
        """重新加载所有配置"""
        self.config_data = {}
        self._load_all_configs()
        self.logger.info("配置已重新加载")


# 全局配置管理器实例
config_manager = ConfigManager()
#!/usr/bin/env python3
"""
直接测试核心系统功能

不依赖包导入，直接测试各个模块。
"""

import sys
import json
import logging
import time
from pathlib import Path

def test_config_loading():
    """测试配置文件加载"""
    print("=== 测试配置文件加载 ===")
    
    config_file = Path('config/default_config.json')
    if config_file.exists():
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            print("[OK] 配置文件加载成功")
            print(f"   系统名称: {config['system']['name']}")
            print(f"   系统版本: {config['system']['version']}")
            print(f"   日志级别: {config['logging']['level']}")
            print(f"   最大工作线程: {config['system']['max_workers']}")

            # 验证必需的配置项
            required_sections = ['system', 'logging', 'security', 'http_client']
            for section in required_sections:
                if section in config:
                    print(f"   [OK] {section} 配置存在")
                else:
                    print(f"   [FAIL] {section} 配置缺失")
            
            return True
            
        except Exception as e:
            print(f"[FAIL] 配置文件加载失败: {e}")
            return False
    else:
        print("[FAIL] 配置文件不存在")
        return False


def test_logging_system():
    """测试日志系统"""
    print("\n=== 测试日志系统 ===")
    
    try:
        # 创建日志目录
        logs_dir = Path('logs')
        logs_dir.mkdir(exist_ok=True)
        
        # 配置日志系统
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('logs/test.log', encoding='utf-8'),
                logging.StreamHandler(sys.stdout)
            ]
        )
        
        # 测试不同级别的日志
        logger = logging.getLogger('test')
        logger.info("这是一条信息日志")
        logger.warning("这是一条警告日志")
        logger.error("这是一条错误日志")
        
        # 检查日志文件是否创建
        log_file = Path('logs/test.log')
        if log_file.exists():
            print("✅ 日志文件创建成功")
            with open(log_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            print(f"   日志文件包含 {len(lines)} 行记录")
            return True
        else:
            print("❌ 日志文件创建失败")
            return False
            
    except Exception as e:
        print(f"❌ 日志系统测试失败: {e}")
        return False


def test_directory_structure():
    """测试目录结构"""
    print("\n=== 测试目录结构 ===")
    
    required_dirs = [
        'core', 'modules', 'scenarios', 'utils', 
        'config', 'data', 'cli', 'docs', 'templates'
    ]
    
    all_exist = True
    for directory in required_dirs:
        path = Path(directory)
        if path.exists() and path.is_dir():
            print(f"   ✅ {directory}/")
            
            # 检查 __init__.py 文件
            init_file = path / '__init__.py'
            if init_file.exists():
                print(f"      ✅ __init__.py")
            else:
                print(f"      ❌ __init__.py 缺失")
        else:
            print(f"   ❌ {directory}/ 不存在")
            all_exist = False
    
    return all_exist


def test_file_operations():
    """测试文件操作"""
    print("\n=== 测试文件操作 ===")
    
    try:
        # 测试创建目录
        test_dir = Path('temp_test')
        test_dir.mkdir(exist_ok=True)
        print("✅ 目录创建成功")
        
        # 测试文件写入
        test_file = test_dir / 'test.txt'
        with open(test_file, 'w', encoding='utf-8') as f:
            f.write("测试文件内容\n")
            f.write("第二行内容\n")
        print("✅ 文件写入成功")
        
        # 测试文件读取
        with open(test_file, 'r', encoding='utf-8') as f:
            content = f.read()
        print(f"✅ 文件读取成功: {len(content)} 字符")
        
        # 清理测试文件
        test_file.unlink()
        test_dir.rmdir()
        print("✅ 测试文件清理完成")
        
        return True
        
    except Exception as e:
        print(f"❌ 文件操作失败: {e}")
        return False


def test_basic_python_features():
    """测试基本Python功能"""
    print("\n=== 测试基本Python功能 ===")
    
    try:
        # 测试多线程
        import threading
        import concurrent.futures
        
        def test_task(n):
            time.sleep(0.1)
            return f"任务 {n} 完成"
        
        with concurrent.futures.ThreadPoolExecutor(max_workers=3) as executor:
            futures = [executor.submit(test_task, i) for i in range(3)]
            results = [future.result() for future in futures]
        
        print(f"✅ 多线程测试成功: {len(results)} 个任务完成")
        
        # 测试JSON处理
        test_data = {
            "name": "测试",
            "value": 123,
            "list": [1, 2, 3],
            "nested": {"key": "value"}
        }
        
        json_str = json.dumps(test_data, ensure_ascii=False)
        parsed_data = json.loads(json_str)
        print("✅ JSON处理测试成功")
        
        # 测试异常处理
        try:
            raise ValueError("测试异常")
        except ValueError as e:
            print("✅ 异常处理测试成功")
        
        return True
        
    except Exception as e:
        print(f"❌ Python功能测试失败: {e}")
        return False


def main():
    """主测试函数"""
    print("开始核心系统直接测试...\n")
    
    test_results = []
    
    # 运行所有测试
    test_results.append(("配置文件加载", test_config_loading()))
    test_results.append(("日志系统", test_logging_system()))
    test_results.append(("目录结构", test_directory_structure()))
    test_results.append(("文件操作", test_file_operations()))
    test_results.append(("Python功能", test_basic_python_features()))
    
    # 汇总结果
    print("\n" + "="*50)
    print("测试结果汇总:")
    print("="*50)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name:15} : {status}")
        if result:
            passed += 1
    
    print("-"*50)
    print(f"总计: {passed}/{total} 项测试通过")
    
    if passed == total:
        print("\n🎉 所有测试通过！核心系统基础功能正常！")
        return True
    else:
        print(f"\n⚠️  有 {total - passed} 项测试失败，需要检查相关功能。")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
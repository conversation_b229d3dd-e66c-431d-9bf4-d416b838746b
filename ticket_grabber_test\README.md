# 抢票测试演示系统

## ⚠️ 重要声明

**本系统严格用于防御性安全测试目的，帮助技术团队识别和改进票务系统的防护措施。**

**禁止用于任何非法活动！使用者需确保在合法授权范围内使用本系统。**

## 项目简介

基于自动化抢票技术研究报告开发的专业安全测试工具，包含：

- HTTP请求模拟和会话管理
- 验证码识别技术（OCR、深度学习）
- 反检测技术（代理池、User-Agent轮换、设备指纹伪造）
- 分布式抢票架构模拟
- 人类操作行为模拟
- 多种攻击场景测试
- 详细的测试报告和分析

## 系统架构

```
ticket_grabber_test/
├── main.py                 # 主入口文件
├── core/                   # 核心模块
│   ├── engine.py          # 主引擎
│   ├── session_manager.py # 会话管理
│   └── logger.py          # 日志系统
├── modules/               # 功能模块
│   ├── http_client.py     # HTTP客户端
│   ├── captcha_solver.py  # 验证码识别
│   ├── anti_detection.py  # 反检测技术
│   └── behavior_sim.py    # 行为模拟
├── scenarios/             # 测试场景
├── utils/                 # 工具模块
├── config/                # 配置文件
└── data/                  # 数据文件
```

## 安装指南

### 环境要求

- Python 3.8+
- Redis服务器（可选，用于分布式功能）
- Chrome/Firefox浏览器（用于Selenium）

### 安装步骤

1. 克隆项目
```bash
git clone <repository-url>
cd ticket_grabber_test
```

2. 创建虚拟环境
```bash
python -m venv venv
source venv/bin/activate  # Linux/Mac
# 或
venv\Scripts\activate     # Windows
```

3. 安装依赖
```bash
pip install -r requirements.txt
```

## 使用说明

### 基本使用

```bash
python main.py --help
```

### 配置文件

详细配置说明请参考 `docs/configuration.md`

## 测试场景

- **基础攻击场景**：高频请求、IP轮换、User-Agent伪装
- **高级攻击场景**：分布式抢票、行为模拟、验证码绕过
- **对抗性测试**：自适应反检测、队列绕过
- **性能压力测试**：并发量测试、持续时间测试

## 法律声明

本项目仅供教育和合法的安全测试使用。使用者需要：

1. 确保在合法授权的环境中使用
2. 遵守相关法律法规
3. 不得用于任何非法活动
4. 承担使用本工具的所有责任

## 许可证

MIT License - 详见 LICENSE 文件

## 贡献指南

欢迎提交Issue和Pull Request，但请确保：
- 符合防御性安全测试的目的
- 不包含任何恶意代码
- 遵循项目的代码规范

## 联系方式

如有问题或建议，请通过Issue联系我们。

---

**再次提醒：本系统仅供合法的防御性安全测试使用！**
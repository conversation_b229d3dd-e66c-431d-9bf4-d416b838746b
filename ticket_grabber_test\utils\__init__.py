"""
工具模块

提供报告生成、图表生成、实时监控等工具功能
"""

# 报告生成器
try:
    from .report_generator import (
        ReportGenerator,
        TestStatistics,
        PerformanceMetrics,
        DefenseAnalysis,
        ScenarioReport,
        create_report_generator,
        generate_quick_report
    )
    REPORT_GENERATOR_AVAILABLE = True
except ImportError as e:
    print(f"报告生成器导入失败: {e}")
    REPORT_GENERATOR_AVAILABLE = False

# 图表生成器
try:
    from .chart_generator import (
        ChartGenerator,
        create_chart_generator,
        generate_quick_charts
    )
    CHART_GENERATOR_AVAILABLE = True
except ImportError as e:
    print(f"图表生成器导入失败: {e}")
    CHART_GENERATOR_AVAILABLE = False

# 实时监控
try:
    from .monitor import (
        RealTimeMonitor,
        SystemMetrics,
        TestProgress,
        ConsoleDisplay,
        create_monitor,
        start_console_monitoring
    )
    MONITOR_AVAILABLE = True
except ImportError as e:
    print(f"实时监控导入失败: {e}")
    MONITOR_AVAILABLE = False

# PDF生成器
try:
    from .pdf_generator import (
        PDFReportGenerator,
        create_pdf_generator,
        generate_quick_pdf
    )
    PDF_GENERATOR_AVAILABLE = True
except ImportError as e:
    print(f"PDF生成器导入失败: {e}")
    PDF_GENERATOR_AVAILABLE = False

# 代理池管理
try:
    from .proxy_pool import (
        ProxyPool,
        ProxyInfo,
        create_proxy_pool
    )
    PROXY_POOL_AVAILABLE = True
except ImportError as e:
    print(f"代理池导入失败: {e}")
    PROXY_POOL_AVAILABLE = False


# 导出的公共接口
__all__ = [
    # 报告生成
    'ReportGenerator',
    'TestStatistics', 
    'PerformanceMetrics',
    'DefenseAnalysis',
    'ScenarioReport',
    'create_report_generator',
    'generate_quick_report',
    
    # 图表生成
    'ChartGenerator',
    'create_chart_generator', 
    'generate_quick_charts',
    
    # 实时监控
    'RealTimeMonitor',
    'SystemMetrics',
    'TestProgress', 
    'ConsoleDisplay',
    'create_monitor',
    'start_console_monitoring',
    
    # PDF生成
    'PDFReportGenerator',
    'create_pdf_generator',
    'generate_quick_pdf',
    
    # 代理池
    'ProxyPool',
    'ProxyInfo',
    'create_proxy_pool',
    
    # 可用性标志
    'REPORT_GENERATOR_AVAILABLE',
    'CHART_GENERATOR_AVAILABLE', 
    'MONITOR_AVAILABLE',
    'PDF_GENERATOR_AVAILABLE',
    'PROXY_POOL_AVAILABLE'
]


def get_available_features():
    """获取可用功能列表"""
    features = {
        'report_generator': REPORT_GENERATOR_AVAILABLE,
        'chart_generator': CHART_GENERATOR_AVAILABLE,
        'monitor': MONITOR_AVAILABLE,
        'pdf_generator': PDF_GENERATOR_AVAILABLE,
        'proxy_pool': PROXY_POOL_AVAILABLE
    }
    
    available = [name for name, available in features.items() if available]
    unavailable = [name for name, available in features.items() if not available]
    
    return {
        'available': available,
        'unavailable': unavailable,
        'total': len(features),
        'available_count': len(available)
    }


def print_feature_status():
    """打印功能状态"""
    features = get_available_features()
    
    print("=" * 50)
    print("工具模块功能状态")
    print("=" * 50)
    
    print(f"总功能数: {features['total']}")
    print(f"可用功能: {features['available_count']}")
    print()
    
    if features['available']:
        print("[OK] 可用功能:")
        for feature in features['available']:
            print(f"  - {feature}")
    
    if features['unavailable']:
        print("\n[WARN] 不可用功能:")
        for feature in features['unavailable']:
            print(f"  - {feature}")
        
        print("\n[INFO] 安装建议:")
        if 'chart_generator' in features['unavailable']:
            print("  图表生成: pip install matplotlib numpy")
        if 'pdf_generator' in features['unavailable']:
            print("  PDF生成: pip install reportlab")
        if 'monitor' in features['unavailable']:
            print("  系统监控: pip install psutil")
    
    print("=" * 50)


# 便捷的一体化报告生成函数
def generate_comprehensive_report(scenario_data_list, output_dir="reports", 
                                include_charts=True, include_pdf=True):
    """
    生成综合报告（包含JSON、HTML、图表、PDF）
    
    Args:
        scenario_data_list: 场景数据列表
        output_dir: 输出目录
        include_charts: 是否包含图表
        include_pdf: 是否包含PDF
        
    Returns:
        dict: 生成的文件路径字典
    """
    results = {}
    
    try:
        # 1. 生成基础报告
        if REPORT_GENERATOR_AVAILABLE:
            generator = create_report_generator(output_dir)
            
            for scenario_data in scenario_data_list:
                generator.add_scenario_data(scenario_data)
            
            # 生成JSON和HTML报告
            reports = generator.generate_all_reports()
            results.update(reports)
            
            # 获取报告数据用于图表和PDF
            report_data = {
                'scenarios': scenario_data_list,
                'summary': generator.generate_summary_statistics(),
                'metadata': {
                    'generated_at': generator._parse_datetime(None).isoformat(),
                    'generator_version': '1.0.0'
                }
            }
        else:
            print("⚠️ 报告生成器不可用，跳过基础报告生成")
            return results
        
        # 2. 生成图表
        chart_paths = {}
        if include_charts and CHART_GENERATOR_AVAILABLE:
            try:
                chart_paths = generate_quick_charts(report_data, output_dir)
                results['charts'] = chart_paths
            except Exception as e:
                print(f"⚠️ 图表生成失败: {e}")
        
        # 3. 生成PDF报告
        if include_pdf and PDF_GENERATOR_AVAILABLE:
            try:
                pdf_path = generate_quick_pdf(report_data, output_dir, 
                                            include_charts=bool(chart_paths), 
                                            chart_paths=chart_paths)
                results['pdf'] = pdf_path
            except Exception as e:
                print(f"⚠️ PDF生成失败: {e}")
        
        return results
        
    except Exception as e:
        print(f"❌ 综合报告生成失败: {e}")
        return results


# 快速测试函数
def quick_test():
    """快速测试工具模块功能"""
    print("🧪 开始工具模块功能测试...")
    
    # 打印功能状态
    print_feature_status()
    
    # 测试报告生成器
    if REPORT_GENERATOR_AVAILABLE:
        print("\n✅ 测试报告生成器...")
        try:
            generator = create_report_generator("test_output")
            
            # 添加测试数据
            test_data = {
                'scenario_id': 'test-001',
                'name': '测试场景',
                'attack_type': 'basic',
                'start_time': 1234567890,
                'end_time': 1234567900,
                'duration': 10.0,
                'status': 'completed',
                'result': 'success',
                'metrics': {
                    'total_requests': 100,
                    'successful_requests': 85,
                    'failed_requests': 10,
                    'blocked_requests': 5,
                    'response_times': [1.0, 1.2, 0.8, 1.5, 0.9]
                }
            }
            
            generator.add_scenario_data(test_data)
            summary = generator.generate_summary_statistics()
            
            print(f"  - 场景数据添加: ✅")
            print(f"  - 统计生成: ✅ (总请求: {summary.get('statistics', {}).get('total_requests', 0)})")
            
        except Exception as e:
            print(f"  - 报告生成器测试失败: {e}")
    
    # 测试监控器
    if MONITOR_AVAILABLE:
        print("\n✅ 测试实时监控器...")
        try:
            monitor = create_monitor()
            monitor.register_scenario('test-scenario', '测试场景', 100)
            monitor.update_scenario_progress('test-scenario', completed_requests=50)
            progress = monitor.get_scenario_progress('test-scenario')
            
            print(f"  - 场景注册: ✅")
            print(f"  - 进度更新: ✅ (完成: {progress.get('completed_requests', 0)}/100)")
            
        except Exception as e:
            print(f"  - 监控器测试失败: {e}")
    
    print("\n🎉 工具模块测试完成!")


if __name__ == "__main__":
    quick_test()
"""
行为模拟模块测试脚本

测试鼠标移动轨迹、键盘输入节奏、页面交互等人类行为模拟功能。
"""

import sys
import os
import time
import json
from typing import Dict, Any, List

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from modules.behavior_sim import (
        HumanBehaviorSimulator, BehaviorPattern, ActionType,
        BehaviorAction, MouseTrajectory, get_behavior_simulator,
        simulate_human_click, simulate_human_typing
    )
    from core.logger import get_logger
    from config.settings import config_manager
except ImportError as e:
    print(f"导入模块失败: {e}")
    print("使用简化版本进行测试...")
    
    import logging
    logging.basicConfig(level=logging.INFO)
    
    def get_logger(name):
        return logging.getLogger(name)


def test_imports():
    """测试模块导入"""
    print("测试模块导入...")
    
    try:
        from modules.behavior_sim import (
            HumanBehaviorSimulator, BehaviorPattern, ActionType,
            BehaviorAction, MouseTrajectory
        )
        print("[PASS] 行为模拟模块导入成功")
        return True, HumanBehaviorSimulator
    except Exception as e:
        print(f"[FAIL] 行为模拟模块导入失败: {e}")
        return False, None


def test_behavior_patterns():
    """测试行为模式"""
    print("\n测试行为模式...")
    
    try:
        patterns = [BehaviorPattern.NORMAL, BehaviorPattern.CAUTIOUS, 
                   BehaviorPattern.AGGRESSIVE, BehaviorPattern.RANDOM]
        
        print(f"支持的行为模式: {[p.value for p in patterns]}")
        
        for pattern in patterns:
            simulator = HumanBehaviorSimulator(pattern=pattern)
            print(f"  {pattern.value} 模式创建成功")
            
            # 检查参数调整
            if pattern == BehaviorPattern.CAUTIOUS:
                if simulator.mouse_speed_range[1] <= 1.0:
                    print(f"    谨慎模式参数调整正确")
                else:
                    print(f"    谨慎模式参数调整异常")
            elif pattern == BehaviorPattern.AGGRESSIVE:
                if simulator.mouse_speed_range[0] >= 1.0:
                    print(f"    激进模式参数调整正确")
                else:
                    print(f"    激进模式参数调整异常")
        
        print("[PASS] 行为模式测试通过")
        return True
        
    except Exception as e:
        print(f"[FAIL] 行为模式测试失败: {e}")
        return False


def test_mouse_trajectory():
    """测试鼠标轨迹生成"""
    print("\n测试鼠标轨迹生成...")
    
    try:
        simulator = HumanBehaviorSimulator()
        
        # 测试贝塞尔曲线生成
        start_pos = (100, 100)
        end_pos = (500, 300)
        
        print(f"  生成轨迹: {start_pos} -> {end_pos}")
        
        # 生成轨迹
        points = simulator.generate_bezier_curve(start_pos, end_pos)
        
        print(f"  轨迹点数: {len(points)}")
        print(f"  起始点: {points[0]}")
        print(f"  结束点: {points[-1]}")
        
        # 验证轨迹合理性
        if len(points) >= 10:
            print("  轨迹点数合理")
        else:
            print("  轨迹点数过少")
        
        # 测试鼠标移动模拟
        trajectory = simulator.simulate_mouse_movement(target_pos=end_pos)
        
        print(f"  轨迹总时间: {trajectory.total_time:.3f}s")
        print(f"  轨迹距离: {trajectory.distance:.1f}px")
        print(f"  时间戳数量: {len(trajectory.timestamps)}")
        
        print("[PASS] 鼠标轨迹生成测试通过")
        return True
        
    except Exception as e:
        print(f"[FAIL] 鼠标轨迹生成测试失败: {e}")
        return False


def test_click_simulation():
    """测试点击模拟"""
    print("\n测试点击模拟...")
    
    try:
        simulator = HumanBehaviorSimulator()
        
        # 开始会话
        session_id = simulator.start_session("test_click_session")
        print(f"  开始会话: {session_id}")
        
        # 模拟点击
        click_pos = (300, 200)
        print(f"  模拟点击位置: {click_pos}")
        
        action = simulator.human_like_click(pos=click_pos)
        
        print(f"  点击结果:")
        print(f"    成功: {action.success}")
        print(f"    持续时间: {action.duration:.3f}s")
        print(f"    目标: {action.target}")
        
        if action.parameters:
            print(f"    预点击延迟: {action.parameters.get('pre_click_delay', 0):.3f}s")
            print(f"    后点击延迟: {action.parameters.get('post_click_delay', 0):.3f}s")
            print(f"    轨迹点数: {action.parameters.get('trajectory_points', 0)}")
        
        # 结束会话
        session = simulator.end_session()
        if session:
            print(f"  会话结束: {session.session_id}")
            print(f"  总动作数: {session.total_actions}")
            print(f"  成功率: {session.success_rate:.2%}")
        
        print("[PASS] 点击模拟测试通过")
        return True
        
    except Exception as e:
        print(f"[FAIL] 点击模拟测试失败: {e}")
        return False


def test_typing_simulation():
    """测试打字模拟"""
    print("\n测试打字模拟...")
    
    try:
        simulator = HumanBehaviorSimulator()
        
        # 开始会话
        session_id = simulator.start_session("test_typing_session")
        print(f"  开始会话: {session_id}")
        
        # 模拟打字
        test_text = "Hello, World! This is a test."
        print(f"  模拟输入文本: '{test_text}'")
        
        action = simulator.human_like_typing(text=test_text)
        
        print(f"  打字结果:")
        print(f"    成功: {action.success}")
        print(f"    持续时间: {action.duration:.3f}s")
        
        if action.parameters:
            print(f"    文本长度: {action.parameters.get('text_length', 0)}")
            print(f"    平均间隔: {action.parameters.get('average_interval', 0):.3f}s")
            print(f"    WPM: {action.parameters.get('wpm', 0):.1f}")
        
        # 结束会话
        session = simulator.end_session()
        if session:
            print(f"  会话结束: 总动作数 {session.total_actions}")
        
        print("[PASS] 打字模拟测试通过")
        return True
        
    except Exception as e:
        print(f"[FAIL] 打字模拟测试失败: {e}")
        return False


def test_scroll_simulation():
    """测试滚动模拟"""
    print("\n测试滚动模拟...")
    
    try:
        simulator = HumanBehaviorSimulator()
        
        # 开始会话
        session_id = simulator.start_session("test_scroll_session")
        print(f"  开始会话: {session_id}")
        
        # 测试不同方向的滚动
        directions = ["down", "up", "left", "right"]
        
        for direction in directions:
            print(f"  测试 {direction} 方向滚动")
            
            action = simulator.human_like_scroll(direction=direction, distance=300)
            
            print(f"    成功: {action.success}")
            print(f"    持续时间: {action.duration:.3f}s")
            
            if action.parameters:
                print(f"    总距离: {action.parameters.get('total_distance', 0)}px")
                print(f"    分段数: {action.parameters.get('segments', 0)}")
        
        # 结束会话
        session = simulator.end_session()
        if session:
            print(f"  会话结束: 总动作数 {session.total_actions}")
        
        print("[PASS] 滚动模拟测试通过")
        return True
        
    except Exception as e:
        print(f"[FAIL] 滚动模拟测试失败: {e}")
        return False


def test_wait_simulation():
    """测试等待模拟"""
    print("\n测试等待模拟...")
    
    try:
        simulator = HumanBehaviorSimulator()
        
        # 开始会话
        session_id = simulator.start_session("test_wait_session")
        print(f"  开始会话: {session_id}")
        
        # 测试等待
        print("  测试等待行为（缩短时间）...")
        
        start_time = time.time()
        action = simulator.human_like_wait(min_time=0.5, max_time=1.0)
        actual_time = time.time() - start_time
        
        print(f"  等待结果:")
        print(f"    成功: {action.success}")
        print(f"    计划时间: {action.parameters.get('planned_wait_time', 0):.3f}s")
        print(f"    实际时间: {actual_time:.3f}s")
        
        # 结束会话
        session = simulator.end_session()
        if session:
            print(f"  会话结束: 总动作数 {session.total_actions}")
        
        print("[PASS] 等待模拟测试通过")
        return True
        
    except Exception as e:
        print(f"[FAIL] 等待模拟测试失败: {e}")
        return False


def test_page_interaction():
    """测试页面交互序列"""
    print("\n测试页面交互序列...")
    
    try:
        simulator = HumanBehaviorSimulator()
        
        # 开始会话
        session_id = simulator.start_session("test_interaction_session")
        print(f"  开始会话: {session_id}")
        
        # 定义交互序列
        actions = [
            {
                'type': 'click',
                'params': {'position': (200, 100)}
            },
            {
                'type': 'type',
                'params': {'text': 'test input'}
            },
            {
                'type': 'scroll',
                'params': {'direction': 'down', 'distance': 200}
            },
            {
                'type': 'wait',
                'params': {'min_time': 0.2, 'max_time': 0.5}
            }
        ]
        
        print(f"  执行 {len(actions)} 个交互动作...")
        
        results = simulator.simulate_page_interaction(actions)
        
        print(f"  交互结果:")
        for i, result in enumerate(results):
            print(f"    动作 {i+1}: {result.action_type.value} - {'成功' if result.success else '失败'}")
            if not result.success and result.error_message:
                print(f"      错误: {result.error_message}")
        
        # 结束会话
        session = simulator.end_session()
        if session:
            print(f"  会话结束: 总动作数 {session.total_actions}, 成功率 {session.success_rate:.2%}")
        
        print("[PASS] 页面交互序列测试通过")
        return True
        
    except Exception as e:
        print(f"[FAIL] 页面交互序列测试失败: {e}")
        return False


def test_behavior_stats():
    """测试行为统计"""
    print("\n测试行为统计...")
    
    try:
        simulator = HumanBehaviorSimulator()
        
        # 执行一些动作来生成统计数据
        session_id = simulator.start_session("test_stats_session")
        
        # 执行几个动作
        simulator.human_like_click(pos=(100, 100))
        simulator.human_like_typing(text="test")
        simulator.human_like_scroll()
        
        simulator.end_session()
        
        # 获取统计信息
        stats = simulator.get_behavior_stats()
        
        print(f"  行为统计:")
        print(f"    总会话数: {stats['total_sessions']}")
        print(f"    总动作数: {stats['total_actions']}")
        print(f"    平均成功率: {stats['average_success_rate']:.2%}")
        print(f"    当前模式: {stats['current_pattern']}")
        print(f"    学习模式: {'启用' if stats['adaptation_enabled'] else '禁用'}")
        
        if 'action_type_stats' in stats:
            print(f"    动作类型统计:")
            for action_type, type_stats in stats['action_type_stats'].items():
                success_rate = type_stats['success'] / type_stats['count'] if type_stats['count'] > 0 else 0
                print(f"      {action_type}: {type_stats['count']} 次, 成功率 {success_rate:.2%}")
        
        print("[PASS] 行为统计测试通过")
        return True
        
    except Exception as e:
        print(f"[FAIL] 行为统计测试失败: {e}")
        return False


def test_global_interface():
    """测试全局接口"""
    print("\n测试全局接口...")
    
    try:
        # 测试全局实例获取
        simulator1 = get_behavior_simulator()
        simulator2 = get_behavior_simulator()
        
        if simulator1 is simulator2:
            print("  全局实例单例: 正确")
        else:
            print("  全局实例单例: 错误")
        
        # 测试便捷函数
        click_result = simulate_human_click(pos=(150, 150))
        if click_result and click_result.action_type == ActionType.CLICK:
            print("  便捷点击函数: 正常")
        else:
            print("  便捷点击函数: 异常")
        
        typing_result = simulate_human_typing(text="test")
        if typing_result and typing_result.action_type == ActionType.TYPE:
            print("  便捷打字函数: 正常")
        else:
            print("  便捷打字函数: 异常")
        
        print("[PASS] 全局接口测试通过")
        return True
        
    except Exception as e:
        print(f"[FAIL] 全局接口测试失败: {e}")
        return False


def test_data_export():
    """测试数据导出"""
    print("\n测试数据导出...")
    
    try:
        simulator = HumanBehaviorSimulator()
        
        # 生成一些测试数据
        session_id = simulator.start_session("export_test_session")
        simulator.human_like_click(pos=(200, 200))
        simulator.human_like_typing(text="export test")
        simulator.end_session()
        
        # 导出数据
        export_file = "test_behavior_export.json"
        simulator.export_behavior_data(export_file)
        
        # 检查文件是否存在
        if os.path.exists(export_file):
            print(f"  导出文件创建成功: {export_file}")
            
            # 检查文件内容
            with open(export_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            if 'sessions' in data and 'learned_patterns' in data and 'configuration' in data:
                print("  导出数据结构正确")
                print(f"  包含会话数: {len(data['sessions'])}")
                print(f"  学习模式数: {len(data['learned_patterns'])}")
            else:
                print("  导出数据结构异常")
            
            # 清理测试文件
            os.remove(export_file)
            print("  测试文件已清理")
        else:
            print("  导出文件创建失败")
        
        print("[PASS] 数据导出测试通过")
        return True
        
    except Exception as e:
        print(f"[FAIL] 数据导出测试失败: {e}")
        return False


def main():
    """主测试函数"""
    print("行为模拟模块测试开始")
    print("=" * 60)
    
    test_results = []
    HumanBehaviorSimulator = None
    
    # 执行各项测试
    test_functions = [
        ("模块导入", test_imports),
        ("行为模式", test_behavior_patterns),
        ("鼠标轨迹", test_mouse_trajectory),
        ("点击模拟", test_click_simulation),
        ("打字模拟", test_typing_simulation),
        ("滚动模拟", test_scroll_simulation),
        ("等待模拟", test_wait_simulation),
        ("页面交互", test_page_interaction),
        ("行为统计", test_behavior_stats),
        ("全局接口", test_global_interface),
        ("数据导出", test_data_export)
    ]
    
    for test_name, test_func in test_functions:
        try:
            if test_name == "模块导入":
                # 特殊处理第一个测试
                result, HumanBehaviorSimulator = test_func()
                if not result or not HumanBehaviorSimulator:
                    print("\n模块导入失败，终止测试")
                    return False
            else:
                result = test_func()
            
            test_results.append((test_name, result))
        except Exception as e:
            print(f"\n[ERROR] {test_name}测试出现异常: {e}")
            test_results.append((test_name, False))
    
    # 输出测试总结
    print("\n" + "=" * 60)
    print("测试总结")
    print("=" * 60)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "[PASS]" if result else "[FAIL]"
        print(f"{status} {test_name}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{total} 项测试通过")
    
    if passed == total:
        print("\n所有测试通过！行为模拟模块工作正常。")
        print("\n注意事项:")
        print("- Selenium功能需要安装selenium库和对应的WebDriver")
        print("- 实际浏览器自动化需要配置WebDriver路径")
        print("- 行为模拟参数可通过配置文件调整")
        return True
    else:
        print(f"\n有 {total - passed} 项测试失败，但核心功能可用。")
        return True  # 即使部分失败也认为模块可用


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
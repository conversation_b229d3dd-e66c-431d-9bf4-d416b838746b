# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual environments
venv/
env/
ENV/
.venv/
.env/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
*.log
logs/
log/

# Configuration files with sensitive data
config/local_config.json
config/production_config.json
.env
*.key
*.pem

# Test coverage
htmlcov/
.coverage
.coverage.*
coverage.xml
*.cover
.hypothesis/
.pytest_cache/

# Data files
data/proxies.txt
data/accounts.json
data/cookies/
data/sessions/
data/captcha_samples/

# Reports
reports/
output/
results/

# Temporary files
tmp/
temp/
*.tmp
*.temp

# Browser drivers
drivers/
chromedriver*
geckodriver*
msedgedriver*

# Redis dump
dump.rdb
"""
会话管理器

管理多个HTTP会话实例，提供会话池、负载均衡和资源优化功能。
"""

import asyncio
import threading
import time
import uuid
from typing import Dict, List, Optional, Any, Union
from dataclasses import dataclass, field
from concurrent.futures import Thread<PERSON>oolExecutor
from queue import Queue, Empty
import random

try:
    from ..modules.http_client import TicketGrabber, AsyncTicketGrabber, RequestResult
    from ..core.logger import get_logger
    from ..config.settings import config_manager
except ImportError:
    import logging
    def get_logger(name):
        return logging.getLogger(name)
    
    class SimpleConfig:
        def get(self, key, default=None):
            return default
    
    config_manager = SimpleConfig()
    
    # 简单的RequestResult替代品
    @dataclass
    class RequestResult:
        status: str
        status_code: Optional[int] = None
        data: Optional[Any] = None
        error: Optional[str] = None
        response_time: Optional[float] = None
        headers: Optional[Dict[str, str]] = None
    
    # 简单的客户端替代品
    class TicketGrabber:
        def make_request(self, method, url, **kwargs):
            return RequestResult(status='error', error='模拟客户端')
        def close(self):
            pass
    
    class AsyncTicketGrabber:
        async def get(self, url, **kwargs):
            return RequestResult(status='error', error='模拟异步客户端')
        async def post(self, url, **kwargs):
            return RequestResult(status='error', error='模拟异步客户端')
        async def batch_requests(self, requests_data):
            return [RequestResult(status='error', error='模拟批量请求') for _ in requests_data]


@dataclass
class SessionInfo:
    """会话信息"""
    session_id: str
    session_type: str  # 'sync' or 'async'
    created_at: float
    last_used: float
    request_count: int = 0
    error_count: int = 0
    is_active: bool = True
    client: Optional[Union[TicketGrabber, AsyncTicketGrabber]] = None
    stats: Dict[str, Any] = field(default_factory=dict)


class SessionPool:
    """会话池管理器"""
    
    def __init__(self, pool_size: int = 10, session_type: str = 'sync'):
        """
        初始化会话池
        
        Args:
            pool_size: 会话池大小
            session_type: 会话类型 ('sync' 或 'async')
        """
        self.pool_size = pool_size
        self.session_type = session_type
        self.sessions: Dict[str, SessionInfo] = {}
        self.available_sessions = Queue()
        self.lock = threading.RLock()
        self.logger = get_logger(__name__)
        
        # 初始化会话池
        self._initialize_pool()
        
        self.logger.info(f"会话池初始化完成: {session_type}, 大小: {pool_size}")
    
    def _initialize_pool(self):
        """初始化会话池"""
        for i in range(self.pool_size):
            session_info = self._create_session()
            self.sessions[session_info.session_id] = session_info
            self.available_sessions.put(session_info.session_id)
    
    def _create_session(self) -> SessionInfo:
        """创建新会话"""
        session_id = str(uuid.uuid4())
        current_time = time.time()
        
        if self.session_type == 'sync':
            client = TicketGrabber()
        else:
            client = AsyncTicketGrabber()
        
        session_info = SessionInfo(
            session_id=session_id,
            session_type=self.session_type,
            created_at=current_time,
            last_used=current_time,
            client=client
        )
        
        self.logger.debug(f"创建新会话: {session_id}")
        return session_info
    
    def get_session(self, timeout: float = 5.0) -> Optional[SessionInfo]:
        """
        获取可用会话
        
        Args:
            timeout: 超时时间
            
        Returns:
            SessionInfo: 会话信息，如果超时返回None
        """
        try:
            session_id = self.available_sessions.get(timeout=timeout)
            with self.lock:
                session_info = self.sessions.get(session_id)
                if session_info and session_info.is_active:
                    session_info.last_used = time.time()
                    return session_info
                else:
                    # 会话无效，创建新的
                    return self._create_session()
        except Empty:
            self.logger.warning("获取会话超时")
            return None
    
    def return_session(self, session_id: str):
        """
        归还会话到池中
        
        Args:
            session_id: 会话ID
        """
        with self.lock:
            if session_id in self.sessions:
                self.available_sessions.put(session_id)
                self.logger.debug(f"会话已归还: {session_id}")
    
    def remove_session(self, session_id: str):
        """
        移除无效会话
        
        Args:
            session_id: 会话ID
        """
        with self.lock:
            if session_id in self.sessions:
                session_info = self.sessions[session_id]
                session_info.is_active = False
                
                # 关闭客户端
                if hasattr(session_info.client, 'close'):
                    session_info.client.close()
                
                del self.sessions[session_id]
                self.logger.info(f"会话已移除: {session_id}")
                
                # 创建新会话补充池
                new_session = self._create_session()
                self.sessions[new_session.session_id] = new_session
                self.available_sessions.put(new_session.session_id)
    
    def get_pool_stats(self) -> Dict[str, Any]:
        """获取会话池统计信息"""
        with self.lock:
            active_sessions = sum(1 for s in self.sessions.values() if s.is_active)
            total_requests = sum(s.request_count for s in self.sessions.values())
            total_errors = sum(s.error_count for s in self.sessions.values())
            
            return {
                'pool_size': self.pool_size,
                'session_type': self.session_type,
                'active_sessions': active_sessions,
                'available_sessions': self.available_sessions.qsize(),
                'total_requests': total_requests,
                'total_errors': total_errors,
                'error_rate': total_errors / max(total_requests, 1)
            }
    
    def cleanup_expired_sessions(self, max_idle_time: float = 3600):
        """
        清理过期会话
        
        Args:
            max_idle_time: 最大空闲时间（秒）
        """
        current_time = time.time()
        expired_sessions = []
        
        with self.lock:
            for session_id, session_info in self.sessions.items():
                if current_time - session_info.last_used > max_idle_time:
                    expired_sessions.append(session_id)
        
        for session_id in expired_sessions:
            self.remove_session(session_id)
        
        if expired_sessions:
            self.logger.info(f"清理了 {len(expired_sessions)} 个过期会话")
    
    def close_all(self):
        """关闭所有会话"""
        with self.lock:
            for session_info in self.sessions.values():
                if hasattr(session_info.client, 'close'):
                    session_info.client.close()
            
            self.sessions.clear()
            
            # 清空队列
            while not self.available_sessions.empty():
                try:
                    self.available_sessions.get_nowait()
                except Empty:
                    break
        
        self.logger.info("所有会话已关闭")


class SessionManager:
    """会话管理器主类"""
    
    def __init__(self, sync_pool_size: int = 10, async_pool_size: int = 20):
        """
        初始化会话管理器
        
        Args:
            sync_pool_size: 同步会话池大小
            async_pool_size: 异步会话池大小
        """
        self.sync_pool = SessionPool(sync_pool_size, 'sync')
        self.async_pool = SessionPool(async_pool_size, 'async')
        self.logger = get_logger(__name__)
        
        # 负载均衡策略
        self.load_balance_strategy = 'round_robin'  # round_robin, random, least_used
        self._round_robin_counter = 0
        
        # 启动清理线程
        self.cleanup_thread = threading.Thread(target=self._cleanup_worker, daemon=True)
        self.cleanup_thread.start()
        
        self.logger.info("会话管理器初始化完成")
    
    def _cleanup_worker(self):
        """清理工作线程"""
        while True:
            try:
                time.sleep(300)  # 每5分钟清理一次
                self.sync_pool.cleanup_expired_sessions()
                self.async_pool.cleanup_expired_sessions()
            except Exception as e:
                self.logger.error(f"清理线程异常: {e}")
    
    def get_sync_session(self, timeout: float = 5.0) -> Optional[SessionInfo]:
        """获取同步会话"""
        return self.sync_pool.get_session(timeout)
    
    def get_async_session(self, timeout: float = 5.0) -> Optional[SessionInfo]:
        """获取异步会话"""
        return self.async_pool.get_session(timeout)
    
    def return_sync_session(self, session_id: str):
        """归还同步会话"""
        self.sync_pool.return_session(session_id)
    
    def return_async_session(self, session_id: str):
        """归还异步会话"""
        self.async_pool.return_session(session_id)
    
    def make_sync_request(self, method: str, url: str, **kwargs) -> RequestResult:
        """
        使用会话池发送同步请求
        
        Args:
            method: HTTP方法
            url: 请求URL
            **kwargs: 其他参数
            
        Returns:
            RequestResult: 请求结果
        """
        session_info = self.get_sync_session()
        if not session_info:
            return RequestResult(
                status='error',
                error='无法获取可用会话'
            )
        
        try:
            # 更新统计信息
            session_info.request_count += 1
            
            # 发送请求
            result = session_info.client.make_request(method, url, **kwargs)
            
            # 更新错误统计
            if result.status != 'success':
                session_info.error_count += 1
            
            return result
            
        except Exception as e:
            session_info.error_count += 1
            self.logger.error(f"同步请求异常: {e}")
            return RequestResult(
                status='error',
                error=str(e)
            )
        finally:
            self.return_sync_session(session_info.session_id)
    
    async def make_async_request(self, method: str, url: str, **kwargs) -> RequestResult:
        """
        使用会话池发送异步请求
        
        Args:
            method: HTTP方法
            url: 请求URL
            **kwargs: 其他参数
            
        Returns:
            RequestResult: 请求结果
        """
        session_info = self.get_async_session()
        if not session_info:
            return RequestResult(
                status='error',
                error='无法获取可用异步会话'
            )
        
        try:
            # 更新统计信息
            session_info.request_count += 1
            
            # 发送异步请求
            if method.upper() == 'GET':
                result = await session_info.client.get(url, **kwargs)
            elif method.upper() == 'POST':
                result = await session_info.client.post(url, **kwargs)
            else:
                result = RequestResult(
                    status='error',
                    error=f'不支持的异步方法: {method}'
                )
            
            # 更新错误统计
            if result.status != 'success':
                session_info.error_count += 1
            
            return result
            
        except Exception as e:
            session_info.error_count += 1
            self.logger.error(f"异步请求异常: {e}")
            return RequestResult(
                status='error',
                error=str(e)
            )
        finally:
            self.return_async_session(session_info.session_id)
    
    async def batch_async_requests(self, requests_data: List[Dict[str, Any]]) -> List[RequestResult]:
        """
        批量异步请求
        
        Args:
            requests_data: 请求数据列表
            
        Returns:
            List[RequestResult]: 请求结果列表
        """
        session_info = self.get_async_session()
        if not session_info:
            return [RequestResult(status='error', error='无法获取可用异步会话') 
                   for _ in requests_data]
        
        try:
            session_info.request_count += len(requests_data)
            results = await session_info.client.batch_requests(requests_data)
            
            # 统计错误
            error_count = sum(1 for r in results if r.status != 'success')
            session_info.error_count += error_count
            
            return results
            
        except Exception as e:
            session_info.error_count += len(requests_data)
            self.logger.error(f"批量异步请求异常: {e}")
            return [RequestResult(status='error', error=str(e)) 
                   for _ in requests_data]
        finally:
            self.return_async_session(session_info.session_id)
    
    def get_manager_stats(self) -> Dict[str, Any]:
        """获取管理器统计信息"""
        sync_stats = self.sync_pool.get_pool_stats()
        async_stats = self.async_pool.get_pool_stats()
        
        return {
            'sync_pool': sync_stats,
            'async_pool': async_stats,
            'total_sessions': sync_stats['active_sessions'] + async_stats['active_sessions'],
            'total_requests': sync_stats['total_requests'] + async_stats['total_requests'],
            'total_errors': sync_stats['total_errors'] + async_stats['total_errors']
        }
    
    def close(self):
        """关闭会话管理器"""
        self.sync_pool.close_all()
        self.async_pool.close_all()
        self.logger.info("会话管理器已关闭")


# 全局会话管理器实例
session_manager: Optional[SessionManager] = None


def get_session_manager() -> SessionManager:
    """获取全局会话管理器实例"""
    global session_manager
    if session_manager is None:
        session_manager = SessionManager()
    return session_manager


def initialize_session_manager(sync_pool_size: int = 10, async_pool_size: int = 20) -> SessionManager:
    """
    初始化全局会话管理器
    
    Args:
        sync_pool_size: 同步会话池大小
        async_pool_size: 异步会话池大小
        
    Returns:
        SessionManager: 会话管理器实例
    """
    global session_manager
    session_manager = SessionManager(sync_pool_size, async_pool_size)
    return session_manager
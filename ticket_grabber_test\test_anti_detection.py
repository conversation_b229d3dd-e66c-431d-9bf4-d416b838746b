"""
反检测技术模块测试脚本

测试代理池管理、User-Agent轮换、设备指纹伪造等反检测技术功能。
"""

import sys
import os
import time
import asyncio
from typing import Dict, Any

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from utils.proxy_pool import ProxyPool, get_proxy_pool, initialize_proxy_pool
    from modules.anti_detection import (
        UserAgentManager, FingerprintSpoofing, HumanBehaviorSimulator,
        get_ua_manager, get_fingerprint_spoofer
    )
    from core.logger import get_logger
    from config.settings import config_manager
except ImportError as e:
    print(f"导入模块失败: {e}")
    print("使用简化版本进行测试...")
    
    import logging
    logging.basicConfig(level=logging.INFO)
    
    def get_logger(name):
        return logging.getLogger(name)


def test_user_agent_manager():
    """测试User-Agent管理器"""
    print("\n" + "="*50)
    print("测试 User-Agent 管理器")
    print("="*50)
    
    try:
        ua_manager = UserAgentManager()
        
        # 测试随机获取UA
        print("\n1. 测试随机获取User-Agent:")
        for i in range(3):
            ua = ua_manager.get_random_ua()
            print(f"   随机UA {i+1}: {ua[:80]}...")
        
        # 测试轮询获取UA
        print("\n2. 测试轮询获取User-Agent:")
        for i in range(3):
            ua = ua_manager.get_next_ua()
            print(f"   轮询UA {i+1}: {ua[:80]}...")
        
        # 测试按浏览器类型获取UA
        print("\n3. 测试按浏览器类型获取User-Agent:")
        browsers = ['chrome', 'firefox', 'safari', 'edge']
        for browser in browsers:
            ua = ua_manager.get_ua_by_browser(browser)
            print(f"   {browser.upper()}: {ua[:80]}...")
        
        print("\n[PASS] User-Agent管理器测试通过")
        return True
        
    except Exception as e:
        print(f"\n[FAIL] User-Agent管理器测试失败: {e}")
        return False


def test_proxy_pool():
    """测试代理池管理器"""
    print("\n" + "="*50)
    print("测试代理池管理器")
    print("="*50)
    
    try:
        # 初始化代理池
        proxy_pool = ProxyPool()
        
        print(f"\n1. 代理池基本信息:")
        print(f"   总代理数: {len(proxy_pool.proxy_list)}")
        print(f"   有效代理数: {len(proxy_pool.valid_proxies)}")
        print(f"   失败代理数: {len(proxy_pool.failed_proxies)}")
        
        # 测试代理验证（如果有代理的话）
        if proxy_pool.proxy_list:
            print(f"\n2. 测试代理验证:")
            test_proxy = proxy_pool.proxy_list[0]
            print(f"   测试代理: {test_proxy}")
            
            result = proxy_pool.validate_proxy(test_proxy)
            if result:
                print(f"   验证结果: 成功 - 响应时间: {result.response_time:.2f}s")
            else:
                print(f"   验证结果: 失败")
        
        # 测试获取代理
        print(f"\n3. 测试获取代理:")
        if proxy_pool.valid_proxies:
            # 测试轮询获取
            proxy = proxy_pool.get_valid_proxy()
            if proxy:
                print(f"   轮询代理: {proxy.proxy_url}")
            
            # 测试随机获取
            proxy = proxy_pool.get_random_proxy()
            if proxy:
                print(f"   随机代理: {proxy.proxy_url}")
            
            # 测试最快代理
            proxy = proxy_pool.get_fastest_proxy()
            if proxy:
                print(f"   最快代理: {proxy.proxy_url} (响应时间: {proxy.response_time:.2f}s)")
        else:
            print("   没有可用的有效代理")
        
        # 获取统计信息
        print(f"\n4. 代理池统计信息:")
        stats = proxy_pool.get_stats()
        for key, value in stats.items():
            if isinstance(value, float):
                print(f"   {key}: {value:.3f}")
            else:
                print(f"   {key}: {value}")
        
        print("\n[PASS] 代理池管理器测试通过")
        return True
        
    except Exception as e:
        print(f"\n[FAIL] 代理池管理器测试失败: {e}")
        return False


def test_fingerprint_spoofing():
    """测试设备指纹伪造"""
    print("\n" + "="*50)
    print("测试设备指纹伪造")
    print("="*50)
    
    try:
        spoofer = FingerprintSpoofing()
        
        # 生成随机指纹
        print("\n1. 生成随机设备指纹:")
        for i in range(3):
            fingerprint = spoofer.generate_random_fingerprint()
            print(f"\n   指纹 {i+1}:")
            print(f"     User-Agent: {fingerprint.user_agent[:80]}...")
            print(f"     屏幕分辨率: {fingerprint.screen_resolution}")
            print(f"     视口大小: {fingerprint.viewport_size}")
            print(f"     时区: {fingerprint.timezone}")
            print(f"     语言: {fingerprint.language}")
            print(f"     平台: {fingerprint.platform}")
            print(f"     WebGL厂商: {fingerprint.webgl_vendor}")
            print(f"     Canvas指纹: {fingerprint.canvas_fingerprint}")
            print(f"     字体数量: {len(fingerprint.fonts)}")
            print(f"     插件数量: {len(fingerprint.plugins)}")
        
        # 测试浏览器设置（如果Selenium可用）
        print(f"\n2. 测试浏览器设置:")
        try:
            # 尝试设置Chrome浏览器
            driver = spoofer.setup_spoofed_browser('chrome')
            if driver:
                print("   Chrome浏览器设置成功")
                driver.quit()
            else:
                print("   Chrome浏览器设置失败（可能是Selenium不可用）")
        except Exception as e:
            print(f"   浏览器设置测试跳过: {e}")
        
        print("\n[PASS] 设备指纹伪造测试通过")
        return True
        
    except Exception as e:
        print(f"\n[FAIL] 设备指纹伪造测试失败: {e}")
        return False


def test_human_behavior_simulator():
    """测试人类行为模拟器"""
    print("\n" + "="*50)
    print("测试人类行为模拟器")
    print("="*50)
    
    try:
        # 创建模拟器（不使用真实浏览器）
        simulator = HumanBehaviorSimulator()
        
        print("\n1. 人类行为模拟器基本功能:")
        print("   模拟器创建成功")
        print("   注意: 完整功能需要WebDriver实例")
        
        # 测试一些基础功能
        print("\n2. 测试基础功能:")
        print("   随机页面交互模拟...")
        simulator.random_page_interaction()
        print("   交互模拟完成")
        
        print("\n[PASS] 人类行为模拟器测试通过")
        return True
        
    except Exception as e:
        print(f"\n[FAIL] 人类行为模拟器测试失败: {e}")
        return False


def test_integration():
    """集成测试"""
    print("\n" + "="*50)
    print("反检测技术集成测试")
    print("="*50)
    
    try:
        # 获取全局实例
        ua_manager = get_ua_manager()
        fingerprint_spoofer = get_fingerprint_spoofer()
        proxy_pool = get_proxy_pool()
        
        print("\n1. 全局实例获取:")
        print(f"   UA管理器: {'✓' if ua_manager else '✗'}")
        print(f"   指纹伪造器: {'✓' if fingerprint_spoofer else '✗'}")
        print(f"   代理池: {'✓' if proxy_pool else '✗'}")
        
        # 模拟完整的反检测流程
        print("\n2. 模拟完整反检测流程:")
        
        # 获取随机UA
        ua = ua_manager.get_random_ua()
        print(f"   获取User-Agent: {ua[:50]}...")
        
        # 生成设备指纹
        fingerprint = fingerprint_spoofer.generate_random_fingerprint()
        print(f"   生成设备指纹: 分辨率{fingerprint.screen_resolution}, 平台{fingerprint.platform}")
        
        # 获取代理（如果有的话）
        if proxy_pool.valid_proxies:
            proxy = proxy_pool.get_random_proxy()
            print(f"   获取代理: {proxy.proxy_url}")
        else:
            print(f"   获取代理: 无可用代理")
        
        # 模拟请求配置
        request_config = {
            'user_agent': ua,
            'screen_resolution': fingerprint.screen_resolution,
            'viewport_size': fingerprint.viewport_size,
            'timezone': fingerprint.timezone,
            'language': fingerprint.language,
            'platform': fingerprint.platform
        }
        
        print(f"\n3. 生成的请求配置:")
        for key, value in request_config.items():
            print(f"   {key}: {value}")
        
        print("\n[PASS] 反检测技术集成测试通过")
        return True
        
    except Exception as e:
        print(f"\n[FAIL] 反检测技术集成测试失败: {e}")
        return False


def main():
    """主测试函数"""
    print("反检测技术模块测试开始")
    print("="*60)
    
    test_results = []
    
    # 执行各项测试
    test_functions = [
        ("User-Agent管理器", test_user_agent_manager),
        ("代理池管理器", test_proxy_pool),
        ("设备指纹伪造", test_fingerprint_spoofing),
        ("人类行为模拟器", test_human_behavior_simulator),
        ("集成测试", test_integration)
    ]
    
    for test_name, test_func in test_functions:
        try:
            result = test_func()
            test_results.append((test_name, result))
        except Exception as e:
            print(f"\n[ERROR] {test_name}测试出现异常: {e}")
            test_results.append((test_name, False))
    
    # 输出测试总结
    print("\n" + "="*60)
    print("测试总结")
    print("="*60)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "[PASS]" if result else "[FAIL]"
        print(f"{status} {test_name}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{total} 项测试通过")
    
    if passed == total:
        print("\n🎉 所有测试通过！反检测技术模块工作正常。")
        return True
    else:
        print(f"\n⚠️  有 {total - passed} 项测试失败，请检查相关功能。")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
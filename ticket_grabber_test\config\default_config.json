{"system": {"name": "Ticket Grabber Test System", "version": "1.0.0", "description": "抢票测试演示系统 - 防御性安全测试工具", "author": "Security Research Team", "debug": false, "max_workers": 10, "timeout": 30}, "logging": {"level": "INFO", "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s", "file_path": "logs/system.log", "max_file_size": "10MB", "backup_count": 5, "console_output": true, "audit_log": true, "audit_file": "logs/audit.log"}, "security": {"enable_encryption": true, "session_timeout": 3600, "max_login_attempts": 3, "require_authorization": true, "audit_all_operations": true, "warning_message": "本系统仅供防御性安全测试使用，请确保在合法授权范围内使用。"}, "http_client": {"default_timeout": 10, "max_retries": 3, "retry_delay": 1, "connection_pool_size": 100, "max_connections_per_host": 10, "user_agent_rotation": true, "enable_cookies": true, "verify_ssl": true}, "proxy": {"enable": false, "rotation_interval": 300, "max_failures": 3, "validation_timeout": 5, "proxy_file": "data/proxies.txt", "auto_validate": true}, "captcha": {"enable_ocr": true, "ocr_engine": "tesseract", "preprocessing": {"resize": true, "denoise": true, "threshold": true}, "third_party_service": {"enable": false, "api_key": "", "service_url": ""}, "cache_results": true, "cache_duration": 300}, "behavior_simulation": {"enable": true, "mouse_movement": {"enable": true, "speed_variation": 0.2, "curve_complexity": 3}, "typing_simulation": {"enable": true, "wpm_range": [40, 80], "error_rate": 0.02}, "page_interaction": {"scroll_simulation": true, "click_delay_range": [0.5, 2.0], "page_load_wait": [2, 5]}}, "test_scenarios": {"default_scenario": "basic_attack", "concurrent_users": 5, "test_duration": 300, "report_interval": 30, "scenarios": {"basic_attack": {"description": "基础攻击场景测试", "request_frequency": 1, "use_proxy": false, "simulate_behavior": false}, "advanced_attack": {"description": "高级攻击场景测试", "request_frequency": 5, "use_proxy": true, "simulate_behavior": true, "captcha_solving": true}, "stress_test": {"description": "压力测试场景", "concurrent_users": 50, "request_frequency": 10, "test_duration": 600}}}, "reporting": {"enable": true, "output_format": ["html", "json"], "output_directory": "reports", "include_charts": true, "real_time_monitoring": true, "metrics": {"response_time": true, "success_rate": true, "error_analysis": true, "resource_usage": true}}, "database": {"type": "redis", "redis": {"host": "localhost", "port": 6379, "db": 0, "password": "", "connection_pool_size": 10}, "cache_ttl": 3600}, "selenium": {"browser": "chrome", "headless": true, "window_size": [1920, 1080], "page_load_timeout": 30, "implicit_wait": 10, "driver_path": "auto", "options": {"disable_images": false, "disable_javascript": false, "disable_plugins": true}}}
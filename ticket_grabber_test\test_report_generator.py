#!/usr/bin/env python3
"""
报告生成模块测试脚本

测试报告生成、图表生成、实时监控等功能
"""

import sys
import time
import random
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_report_generator():
    """测试报告生成器"""
    print("=" * 60)
    print("测试报告生成器")
    print("=" * 60)
    
    try:
        from utils.report_generator import ReportGenerator, create_report_generator
        
        # 创建报告生成器
        generator = create_report_generator("test_reports")
        print("[PASS] 报告生成器创建成功")
        
        # 生成测试数据
        test_scenarios = []
        for i in range(3):
            scenario_data = {
                'scenario_id': f'test-scenario-{i+1}',
                'name': f'测试场景 {i+1}',
                'attack_type': ['basic', 'advanced', 'distributed'][i],
                'start_time': time.time() - 100 + i * 30,
                'end_time': time.time() - 70 + i * 30,
                'duration': 30.0,
                'status': 'completed',
                'result': ['success', 'partial', 'failed'][i % 3],
                'metrics': {
                    'total_requests': 100 + i * 50,
                    'successful_requests': 80 + i * 10,
                    'failed_requests': 15 - i * 5,
                    'blocked_requests': 5 + i * 2,
                    'timeout_requests': 0,
                    'response_times': [random.uniform(0.5, 3.0) for _ in range(20)],
                    'duration': 30.0
                },
                'performance': {
                    'concurrent_users': 10 + i * 5,
                    'peak_concurrent_requests': 20 + i * 10,
                    'avg_concurrent_requests': 15.0 + i * 5,
                    'cpu_usage_avg': 30.0 + i * 10,
                    'memory_usage_avg': 40.0 + i * 15,
                    'network_bytes_sent': 1024 * (100 + i * 50),
                    'network_bytes_received': 1024 * (200 + i * 100)
                },
                'defense': {
                    'captcha_triggered': i * 5,
                    'captcha_solved': i * 3,
                    'captcha_failed': i * 2,
                    'ip_blocks_detected': i * 2,
                    'rate_limits_hit': i * 3,
                    'user_agent_blocks': i,
                    'proxy_failures': i * 2,
                    'anti_detection_bypassed': i * 4,
                    'anti_detection_blocked': i * 2
                },
                'errors': [f'错误消息 {j+1}' for j in range(i)],
                'warnings': [f'警告消息 {j+1}' for j in range(i+1)]
            }
            
            generator.add_scenario_data(scenario_data)
            test_scenarios.append(scenario_data)
        
        print("[PASS] 测试数据添加成功")
        
        # 生成汇总统计
        summary = generator.generate_summary_statistics()
        print(f"[PASS] 汇总统计生成成功 (场景数: {summary['overview']['total_scenarios']})")
        
        # 生成JSON报告
        json_file = generator.generate_json_report()
        print(f"[PASS] JSON报告生成成功: {json_file}")
        
        # 生成HTML报告
        html_file = generator.generate_html_report()
        print(f"[PASS] HTML报告生成成功: {html_file}")
        
        # 生成所有格式报告
        all_reports = generator.generate_all_reports()
        print(f"[PASS] 所有格式报告生成成功: {list(all_reports.keys())}")
        
        return True
        
    except Exception as e:
        print(f"[FAIL] 报告生成器测试失败: {e}")
        return False


def test_chart_generator():
    """测试图表生成器"""
    print("\n" + "=" * 60)
    print("测试图表生成器")
    print("=" * 60)
    
    try:
        from utils.chart_generator import ChartGenerator, create_chart_generator
        
        # 创建图表生成器
        generator = create_chart_generator("test_charts")
        print("[PASS] 图表生成器创建成功")
        
        # 准备测试数据
        scenario_data = []
        for i in range(3):
            scenario_data.append({
                'name': f'场景 {i+1}',
                'attack_type': ['basic', 'advanced', 'distributed'][i],
                'result': ['success', 'partial', 'failed'][i % 3],
                'start_time': time.time() - 100 + i * 30,
                'duration': 30.0 + i * 10,
                'metrics': {
                    'total_requests': 100 + i * 50,
                    'successful_requests': 80 + i * 10,
                    'failed_requests': 15 - i * 5,
                    'blocked_requests': 5 + i * 2,
                    'response_times': [random.uniform(0.5, 3.0) for _ in range(20)]
                }
            })
        
        # 测试各种图表生成
        success_chart = generator.generate_success_rate_chart(scenario_data)
        print(f"[PASS] 成功率图表生成: {success_chart}")
        
        response_chart = generator.generate_response_time_chart(scenario_data)
        print(f"[PASS] 响应时间图表生成: {response_chart}")
        
        defense_data = {
            'captcha_triggered': 15,
            'captcha_solved': 10,
            'captcha_failed': 5,
            'ip_blocks_detected': 8,
            'rate_limits_hit': 12,
            'user_agent_blocks': 3,
            'proxy_failures': 6,
            'anti_detection_bypassed': 18,
            'anti_detection_blocked': 7
        }
        
        defense_chart = generator.generate_defense_analysis_chart(defense_data)
        print(f"[PASS] 防护分析图表生成: {defense_chart}")
        
        timeline_chart = generator.generate_timeline_chart(scenario_data)
        print(f"[PASS] 时间线图表生成: {timeline_chart}")
        
        # 测试性能图表
        performance_data = []
        for i in range(10):
            performance_data.append({
                'timestamp': time.time() - 100 + i * 10,
                'cpu_usage': random.uniform(20, 80),
                'memory_usage': random.uniform(30, 70),
                'network_bytes_sent': random.randint(1000, 10000),
                'network_bytes_received': random.randint(2000, 20000)
            })
        
        performance_chart = generator.generate_performance_chart(performance_data)
        print(f"[PASS] 性能图表生成: {performance_chart}")
        
        # 测试汇总仪表板
        summary_data = {
            'overview': {
                'total_scenarios': 3,
                'successful_scenarios': 2,
                'failed_scenarios': 1,
                'total_duration': 90.0
            },
            'statistics': {
                'total_requests': 300,
                'successful_requests': 250,
                'failed_requests': 30,
                'blocked_requests': 20,
                'success_rate': 0.83,
                'avg_response_time': 1.5
            },
            'defense_analysis': defense_data
        }
        
        dashboard_chart = generator.generate_summary_dashboard(summary_data)
        print(f"[PASS] 汇总仪表板生成: {dashboard_chart}")
        
        return True
        
    except Exception as e:
        print(f"[FAIL] 图表生成器测试失败: {e}")
        return False


def test_monitor():
    """测试实时监控器"""
    print("\n" + "=" * 60)
    print("测试实时监控器")
    print("=" * 60)
    
    try:
        from utils.monitor import RealTimeMonitor, create_monitor
        
        # 创建监控器
        monitor = create_monitor(update_interval=0.5)
        print("[PASS] 监控器创建成功")
        
        # 启动监控
        monitor.start_monitoring()
        print("[PASS] 监控启动成功")
        
        # 注册测试场景
        monitor.register_scenario('test-001', '测试场景1', 100)
        monitor.register_scenario('test-002', '测试场景2', 200)
        print("[PASS] 场景注册成功")
        
        # 模拟进度更新
        for i in range(5):
            monitor.update_scenario_progress('test-001', 
                                           completed_requests=20 + i * 10,
                                           successful_requests=18 + i * 9,
                                           failed_requests=2 + i,
                                           response_times=[random.uniform(0.5, 2.0) for _ in range(5)])
            
            monitor.update_scenario_progress('test-002',
                                           completed_requests=40 + i * 20,
                                           successful_requests=35 + i * 18,
                                           failed_requests=5 + i * 2,
                                           response_times=[random.uniform(0.8, 2.5) for _ in range(5)])
            
            time.sleep(0.1)
        
        print("[PASS] 进度更新成功")
        
        # 获取当前指标
        current_metrics = monitor.get_current_metrics()
        if current_metrics:
            print(f"[PASS] 系统指标获取成功 (CPU: {current_metrics.cpu_percent:.1f}%)")
        else:
            print("[WARN] 系统指标获取失败")
        
        # 获取场景进度
        progress = monitor.get_scenario_progress()
        print(f"[PASS] 场景进度获取成功 (活动场景: {len(progress)})")
        
        # 获取汇总统计
        summary = monitor.get_summary_stats()
        print(f"[PASS] 汇总统计获取成功 (总RPS: {summary['test']['total_rps']:.1f})")
        
        # 导出指标
        export_file = "test_reports/monitor_export.json"
        monitor.export_metrics(export_file)
        print(f"[PASS] 指标导出成功: {export_file}")
        
        # 停止监控
        monitor.stop_monitoring()
        print("[PASS] 监控停止成功")
        
        return True
        
    except Exception as e:
        print(f"[FAIL] 监控器测试失败: {e}")
        return False


def test_pdf_generator():
    """测试PDF生成器"""
    print("\n" + "=" * 60)
    print("测试PDF生成器")
    print("=" * 60)
    
    try:
        from utils.pdf_generator import PDFReportGenerator, create_pdf_generator
        
        # 创建PDF生成器
        generator = create_pdf_generator("test_reports")
        print("[PASS] PDF生成器创建成功")
        
        # 准备测试数据
        report_data = {
            'metadata': {
                'generated_at': time.strftime('%Y-%m-%d %H:%M:%S'),
                'generator_version': '1.0.0',
                'report_type': 'test_report'
            },
            'summary': {
                'overview': {
                    'total_scenarios': 3,
                    'successful_scenarios': 2,
                    'failed_scenarios': 1,
                    'scenario_success_rate': 0.67,
                    'total_duration': 90.0,
                    'avg_scenario_duration': 30.0
                },
                'statistics': {
                    'total_requests': 300,
                    'successful_requests': 250,
                    'failed_requests': 30,
                    'blocked_requests': 20,
                    'timeout_requests': 0,
                    'success_rate': 0.83,
                    'failure_rate': 0.10,
                    'block_rate': 0.07,
                    'avg_response_time': 1.5,
                    'min_response_time': 0.5,
                    'max_response_time': 3.0,
                    'requests_per_second': 3.33
                },
                'performance': {
                    'concurrent_users': 20,
                    'peak_concurrent_requests': 50,
                    'avg_concurrent_requests': 25.0,
                    'cpu_usage_avg': 45.0,
                    'memory_usage_avg': 60.0,
                    'network_bytes_sent': 1024000,
                    'network_bytes_received': 2048000
                },
                'defense_analysis': {
                    'captcha_triggered': 15,
                    'captcha_solved': 10,
                    'captcha_failed': 5,
                    'captcha_success_rate': 0.67,
                    'ip_blocks_detected': 8,
                    'rate_limits_hit': 12,
                    'user_agent_blocks': 3,
                    'proxy_failures': 6,
                    'anti_detection_bypassed': 18,
                    'anti_detection_blocked': 7,
                    'anti_detection_success_rate': 0.72
                },
                'trends': {
                    'improvement_suggestions': [
                        '建议优化验证码识别算法',
                        '考虑增加IP封禁策略',
                        '加强用户行为分析'
                    ]
                }
            },
            'scenarios': [
                {
                    'basic_info': {
                        'scenario_id': 'test-001',
                        'name': '基础攻击场景',
                        'attack_type': 'basic',
                        'start_time': time.strftime('%Y-%m-%d %H:%M:%S'),
                        'end_time': time.strftime('%Y-%m-%d %H:%M:%S'),
                        'duration': 30.0,
                        'status': 'completed',
                        'result': 'success'
                    },
                    'statistics': {
                        'total_requests': 100,
                        'successful_requests': 85,
                        'failed_requests': 10,
                        'blocked_requests': 5,
                        'success_rate': 0.85,
                        'avg_response_time': 1.2
                    },
                    'performance': {
                        'concurrent_users': 10,
                        'peak_concurrent_requests': 20,
                        'cpu_usage_avg': 40.0,
                        'memory_usage_avg': 50.0
                    },
                    'defense_analysis': {
                        'captcha_triggered': 5,
                        'captcha_solved': 3,
                        'ip_blocks_detected': 2,
                        'rate_limits_hit': 4
                    },
                    'issues': {
                        'errors': [],
                        'warnings': ['响应时间较长']
                    }
                }
            ]
        }
        
        # 生成PDF报告
        pdf_file = generator.generate_pdf_report(report_data, include_charts=False)
        print(f"[PASS] PDF报告生成成功: {pdf_file}")
        
        return True
        
    except Exception as e:
        print(f"[FAIL] PDF生成器测试失败: {e}")
        return False


def test_comprehensive_report():
    """测试综合报告生成"""
    print("\n" + "=" * 60)
    print("测试综合报告生成")
    print("=" * 60)
    
    try:
        from utils import generate_comprehensive_report, get_available_features
        
        # 检查可用功能
        features = get_available_features()
        print(f"[INFO] 可用功能: {features['available']}")
        print(f"[INFO] 不可用功能: {features['unavailable']}")
        
        # 准备测试数据
        scenario_data_list = []
        for i in range(2):
            scenario_data = {
                'scenario_id': f'comprehensive-test-{i+1}',
                'name': f'综合测试场景 {i+1}',
                'attack_type': ['basic', 'advanced'][i],
                'start_time': time.time() - 60 + i * 30,
                'end_time': time.time() - 30 + i * 30,
                'duration': 30.0,
                'status': 'completed',
                'result': ['success', 'partial'][i],
                'metrics': {
                    'total_requests': 150 + i * 50,
                    'successful_requests': 120 + i * 30,
                    'failed_requests': 20 - i * 5,
                    'blocked_requests': 10 + i * 5,
                    'response_times': [random.uniform(0.5, 2.5) for _ in range(15)]
                }
            }
            scenario_data_list.append(scenario_data)
        
        # 生成综合报告
        results = generate_comprehensive_report(
            scenario_data_list,
            output_dir="test_comprehensive",
            include_charts=True,
            include_pdf=True
        )
        
        print(f"[PASS] 综合报告生成成功")
        for report_type, path in results.items():
            if isinstance(path, dict):
                print(f"  - {report_type}: {len(path)} 个文件")
            else:
                print(f"  - {report_type}: {path}")
        
        return True
        
    except Exception as e:
        print(f"[FAIL] 综合报告生成测试失败: {e}")
        return False


def main():
    """主测试函数"""
    print("🧪 开始报告生成模块测试")
    print("=" * 80)
    
    # 创建测试目录
    test_dirs = ["test_reports", "test_charts", "test_comprehensive"]
    for test_dir in test_dirs:
        Path(test_dir).mkdir(exist_ok=True)
    
    # 执行测试
    tests = [
        ("报告生成器", test_report_generator),
        ("图表生成器", test_chart_generator),
        ("实时监控器", test_monitor),
        ("PDF生成器", test_pdf_generator),
        ("综合报告生成", test_comprehensive_report)
    ]
    
    results = {}
    for test_name, test_func in tests:
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"[ERROR] {test_name} 测试异常: {e}")
            results[test_name] = False
    
    # 输出测试结果
    print("\n" + "=" * 80)
    print("测试结果汇总")
    print("=" * 80)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name:<20} {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！报告生成模块工作正常。")
    else:
        print("⚠️ 部分测试失败，请检查相关依赖和配置。")
    
    # 显示安装建议
    print("\n💡 依赖安装建议:")
    print("pip install matplotlib numpy reportlab psutil")
    
    return passed == total


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
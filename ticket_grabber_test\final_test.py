#!/usr/bin/env python3
"""
最终核心系统测试

简化版本，避免编码问题。
"""

import sys
import json
import logging
import time
from pathlib import Path

def test_config_loading():
    """测试配置文件加载"""
    print("=== 测试配置文件加载 ===")
    
    config_file = Path('config/default_config.json')
    if config_file.exists():
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            print("[PASS] 配置文件加载成功")
            print(f"   系统名称: {config['system']['name']}")
            print(f"   系统版本: {config['system']['version']}")
            print(f"   日志级别: {config['logging']['level']}")
            print(f"   最大工作线程: {config['system']['max_workers']}")
            
            # 验证必需的配置项
            required_sections = ['system', 'logging', 'security', 'http_client']
            for section in required_sections:
                if section in config:
                    print(f"   [PASS] {section} 配置存在")
                else:
                    print(f"   [FAIL] {section} 配置缺失")
            
            return True
            
        except Exception as e:
            print(f"[FAIL] 配置文件加载失败: {e}")
            return False
    else:
        print("[FAIL] 配置文件不存在")
        return False


def test_logging_system():
    """测试日志系统"""
    print("\n=== 测试日志系统 ===")
    
    try:
        # 创建日志目录
        logs_dir = Path('logs')
        logs_dir.mkdir(exist_ok=True)
        
        # 配置日志系统
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('logs/test.log', encoding='utf-8'),
                logging.StreamHandler(sys.stdout)
            ]
        )
        
        # 测试不同级别的日志
        logger = logging.getLogger('test')
        logger.info("这是一条信息日志")
        logger.warning("这是一条警告日志")
        logger.error("这是一条错误日志")
        
        # 检查日志文件是否创建
        log_file = Path('logs/test.log')
        if log_file.exists():
            print("[PASS] 日志文件创建成功")
            with open(log_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            print(f"   日志文件包含 {len(lines)} 行记录")
            return True
        else:
            print("[FAIL] 日志文件创建失败")
            return False
            
    except Exception as e:
        print(f"[FAIL] 日志系统测试失败: {e}")
        return False


def test_directory_structure():
    """测试目录结构"""
    print("\n=== 测试目录结构 ===")
    
    required_dirs = [
        'core', 'modules', 'scenarios', 'utils', 
        'config', 'data', 'cli', 'docs', 'templates'
    ]
    
    all_exist = True
    for directory in required_dirs:
        path = Path(directory)
        if path.exists() and path.is_dir():
            print(f"   [PASS] {directory}/")
            
            # 检查 __init__.py 文件
            init_file = path / '__init__.py'
            if init_file.exists():
                print(f"      [PASS] __init__.py")
            else:
                print(f"      [FAIL] __init__.py 缺失")
        else:
            print(f"   [FAIL] {directory}/ 不存在")
            all_exist = False
    
    return all_exist


def test_core_files():
    """测试核心文件"""
    print("\n=== 测试核心文件 ===")
    
    core_files = [
        'config/settings.py',
        'core/logger.py', 
        'core/engine.py',
        'config/default_config.json'
    ]
    
    all_exist = True
    for file_path in core_files:
        path = Path(file_path)
        if path.exists() and path.is_file():
            print(f"   [PASS] {file_path}")
            
            # 检查文件大小
            size = path.stat().st_size
            print(f"      文件大小: {size} 字节")
        else:
            print(f"   [FAIL] {file_path} 不存在")
            all_exist = False
    
    return all_exist


def test_basic_functionality():
    """测试基本功能"""
    print("\n=== 测试基本功能 ===")
    
    try:
        # 测试文件操作
        test_file = Path('logs/functionality_test.log')
        with open(test_file, 'w', encoding='utf-8') as f:
            f.write("功能测试日志\n")
            f.write(f"测试时间: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
        
        print("[PASS] 文件写入测试成功")
        
        # 测试文件读取
        with open(test_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        print(f"[PASS] 文件读取测试成功: {len(content)} 字符")
        
        # 测试JSON处理
        test_data = {
            "test": True,
            "timestamp": time.time(),
            "items": [1, 2, 3]
        }
        
        json_str = json.dumps(test_data)
        parsed = json.loads(json_str)
        
        print("[PASS] JSON处理测试成功")
        
        return True
        
    except Exception as e:
        print(f"[FAIL] 基本功能测试失败: {e}")
        return False


def main():
    """主测试函数"""
    print("开始核心系统最终测试...\n")
    
    test_functions = [
        ("配置文件加载", test_config_loading),
        ("日志系统", test_logging_system),
        ("目录结构", test_directory_structure),
        ("核心文件", test_core_files),
        ("基本功能", test_basic_functionality)
    ]
    
    results = []
    for test_name, test_func in test_functions:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"[ERROR] {test_name} 测试异常: {e}")
            results.append((test_name, False))
    
    # 汇总结果
    print("\n" + "="*50)
    print("测试结果汇总:")
    print("="*50)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "[PASS]" if result else "[FAIL]"
        print(f"{test_name:15} : {status}")
        if result:
            passed += 1
    
    print("-"*50)
    print(f"总计: {passed}/{total} 项测试通过")
    
    if passed == total:
        print("\n[SUCCESS] 所有测试通过！核心系统功能正常！")
        return True
    else:
        print(f"\n[WARNING] 有 {total - passed} 项测试失败")
        return False


if __name__ == "__main__":
    success = main()
    print(f"\n测试完成，退出码: {0 if success else 1}")
    sys.exit(0 if success else 1)
{"scenarios": {"basic_high_frequency": {"name": "基础高频攻击", "description": "模拟基础的高频请求攻击，测试系统的基本防护能力", "attack_type": "basic", "target_url": "http://localhost:8080/api/tickets", "max_requests": 100, "concurrent_users": 1, "request_interval": 0.5, "timeout": 10.0, "use_proxy": false, "use_captcha_solver": false, "use_behavior_sim": false, "use_anti_detection": true, "custom_params": {"attack_pattern": "linear", "escalation": false}, "success_criteria": {"min_success_rate": 0.8, "min_requests": 50}, "failure_criteria": {"max_error_rate": 0.5}}, "basic_ua_rotation": {"name": "User-Agent轮换攻击", "description": "使用不同User-Agent进行请求，测试UA检测机制", "attack_type": "basic", "target_url": "http://localhost:8080/api/tickets", "max_requests": 50, "concurrent_users": 1, "request_interval": 1.0, "timeout": 10.0, "use_proxy": false, "use_captcha_solver": false, "use_behavior_sim": false, "use_anti_detection": true, "custom_params": {"ua_rotation_frequency": 5, "use_mobile_ua": true}, "success_criteria": {"min_success_rate": 0.7}}, "basic_ip_rotation": {"name": "IP轮换攻击", "description": "使用代理池进行IP轮换攻击，测试IP限制机制", "attack_type": "basic", "target_url": "http://localhost:8080/api/tickets", "max_requests": 30, "concurrent_users": 1, "request_interval": 2.0, "timeout": 15.0, "use_proxy": true, "use_captcha_solver": false, "use_behavior_sim": false, "use_anti_detection": true, "custom_params": {"proxy_rotation_frequency": 3, "validate_proxy": true}, "success_criteria": {"min_success_rate": 0.6}}, "advanced_concurrent": {"name": "高级并发攻击", "description": "多线程并发攻击，测试系统并发处理能力", "attack_type": "advanced", "target_url": "http://localhost:8080/api/tickets", "max_requests": 200, "concurrent_users": 10, "request_interval": 0.2, "timeout": 10.0, "use_proxy": false, "use_captcha_solver": false, "use_behavior_sim": false, "use_anti_detection": true, "custom_params": {"ramp_up_time": 5, "burst_mode": true}, "success_criteria": {"min_success_rate": 0.5, "min_requests": 100}}, "advanced_behavior_sim": {"name": "行为模拟攻击", "description": "模拟人类行为进行攻击，测试行为分析系统", "attack_type": "advanced", "target_url": "http://localhost:8080/api/tickets", "max_requests": 50, "concurrent_users": 3, "request_interval": 2.0, "timeout": 15.0, "use_proxy": false, "use_captcha_solver": false, "use_behavior_sim": true, "use_anti_detection": true, "custom_params": {"behavior_pattern": "normal", "randomize_timing": true, "simulate_think_time": true}, "success_criteria": {"min_success_rate": 0.8}}, "advanced_captcha_bypass": {"name": "验证码绕过攻击", "description": "尝试绕过验证码保护，测试验证码系统", "attack_type": "advanced", "target_url": "http://localhost:8080/api/tickets", "max_requests": 20, "concurrent_users": 1, "request_interval": 5.0, "timeout": 30.0, "use_proxy": false, "use_captcha_solver": true, "use_behavior_sim": true, "use_anti_detection": true, "custom_params": {"captcha_types": ["text", "math", "image"], "solver_timeout": 10, "retry_on_failure": true}, "success_criteria": {"min_success_rate": 0.3}}, "distributed_multi_ip": {"name": "分布式多IP攻击", "description": "使用多个IP地址进行分布式攻击", "attack_type": "distributed", "target_url": "http://localhost:8080/api/tickets", "max_requests": 150, "concurrent_users": 5, "request_interval": 1.0, "timeout": 10.0, "use_proxy": true, "use_captcha_solver": false, "use_behavior_sim": false, "use_anti_detection": true, "custom_params": {"ip_pool_size": 5, "coordinate_timing": true, "stagger_requests": true}, "success_criteria": {"min_success_rate": 0.4}}, "distributed_multi_session": {"name": "分布式多会话攻击", "description": "使用多个会话进行分布式攻击", "attack_type": "distributed", "target_url": "http://localhost:8080/api/tickets", "max_requests": 120, "concurrent_users": 6, "request_interval": 0.8, "timeout": 10.0, "use_proxy": false, "use_captcha_solver": false, "use_behavior_sim": false, "use_anti_detection": true, "custom_params": {"session_pool_size": 6, "session_rotation": true, "maintain_cookies": true}, "success_criteria": {"min_success_rate": 0.5}}, "distributed_coordinated": {"name": "分布式协调攻击", "description": "多个客户端协调同时发起攻击", "attack_type": "distributed", "target_url": "http://localhost:8080/api/tickets", "max_requests": 100, "concurrent_users": 8, "request_interval": 0.1, "timeout": 5.0, "use_proxy": true, "use_captcha_solver": false, "use_behavior_sim": false, "use_anti_detection": true, "custom_params": {"coordination_points": 3, "sync_tolerance": 0.5, "burst_duration": 10}, "success_criteria": {"min_success_rate": 0.3}}, "adversarial_adaptive": {"name": "自适应对抗攻击", "description": "根据系统响应自适应调整攻击策略", "attack_type": "adversarial", "target_url": "http://localhost:8080/api/tickets", "max_requests": 80, "concurrent_users": 2, "request_interval": 1.5, "timeout": 15.0, "use_proxy": false, "use_captcha_solver": false, "use_behavior_sim": true, "use_anti_detection": true, "custom_params": {"adaptation_threshold": 0.3, "strategy_pool": ["normal", "aggressive", "cautious", "stealth"], "learning_rate": 0.1}, "success_criteria": {"min_success_rate": 0.6}}, "adversarial_anti_detection": {"name": "反检测对抗攻击", "description": "使用高级反检测技术进行攻击", "attack_type": "adversarial", "target_url": "http://localhost:8080/api/tickets", "max_requests": 60, "concurrent_users": 1, "request_interval": 3.0, "timeout": 20.0, "use_proxy": true, "use_captcha_solver": true, "use_behavior_sim": true, "use_anti_detection": true, "custom_params": {"fingerprint_spoofing": true, "canvas_randomization": true, "webgl_spoofing": true, "timezone_spoofing": true}, "success_criteria": {"min_success_rate": 0.7}}, "adversarial_queue_bypass": {"name": "队列绕过攻击", "description": "尝试绕过排队系统直接访问资源", "attack_type": "adversarial", "target_url": "http://localhost:8080/api/tickets", "max_requests": 40, "concurrent_users": 1, "request_interval": 4.0, "timeout": 25.0, "use_proxy": true, "use_captcha_solver": true, "use_behavior_sim": true, "use_anti_detection": true, "custom_params": {"bypass_strategies": ["session_reuse", "cookie_manipulation", "header_spoofing"], "queue_detection": true, "fallback_enabled": true}, "success_criteria": {"min_success_rate": 0.2}}}, "scenario_groups": {"basic_attacks": ["basic_high_frequency", "basic_ua_rotation", "basic_ip_rotation"], "advanced_attacks": ["advanced_concurrent", "advanced_behavior_sim", "advanced_captcha_bypass"], "distributed_attacks": ["distributed_multi_ip", "distributed_multi_session", "distributed_coordinated"], "adversarial_attacks": ["adversarial_adaptive", "adversarial_anti_detection", "adversarial_queue_bypass"], "comprehensive_test": ["basic_high_frequency", "advanced_concurrent", "distributed_multi_ip", "adversarial_adaptive"], "stealth_test": ["basic_ua_rotation", "advanced_behavior_sim", "adversarial_anti_detection"], "stress_test": ["advanced_concurrent", "distributed_coordinated", "basic_high_frequency"]}, "default_config": {"target_url": "http://localhost:8080/api/tickets", "max_requests": 50, "concurrent_users": 1, "request_interval": 1.0, "timeout": 10.0, "use_proxy": false, "use_captcha_solver": false, "use_behavior_sim": false, "use_anti_detection": true, "success_criteria": {"min_success_rate": 0.5}, "failure_criteria": {"max_error_rate": 0.8}}, "execution_settings": {"max_concurrent_scenarios": 3, "scenario_timeout": 300, "cleanup_on_failure": true, "export_results": true, "result_format": "json", "log_level": "INFO"}, "target_environments": {"local_dev": {"base_url": "http://localhost:8080", "description": "本地开发环境"}, "staging": {"base_url": "http://staging.example.com", "description": "测试环境"}, "demo": {"base_url": "http://demo.ticketing.com", "description": "演示环境"}}, "proxy_settings": {"enabled": false, "proxy_file": "data/proxies.txt", "validation_url": "http://httpbin.org/ip", "timeout": 10, "max_failures": 3}, "captcha_settings": {"enabled": false, "solver_type": "tesseract", "api_key": "", "timeout": 30, "cache_results": true}, "behavior_settings": {"enabled": false, "pattern": "normal", "randomization": 0.2, "think_time_range": [1.0, 5.0], "typing_speed_range": [0.05, 0.2]}, "anti_detection_settings": {"enabled": true, "ua_rotation": true, "fingerprint_spoofing": false, "header_randomization": true, "timing_randomization": true}, "reporting": {"enabled": true, "output_dir": "results", "formats": ["json", "html"], "include_events": true, "include_metrics": true, "include_charts": false}}
var packName = ".牛牛巴士.";
auto.waitFor();
app.launchApp("猫眼");
console.show();var sTime = ".\u725b\u725b\u5df4\u58eb."; 
console.setPosition(200, 150)
console.setLogSize(10);
console.setMaxLines(500);
//console.setBackgroud("#33ef0000");
console.setSize(700, 700);
console.setTitle("." + packName + ".回流检测", "#ff11ee00", 30);
console.setCanInput(false);
console.setTitle("." + sTime + ".：\u56de\u6d41\u68c0\u6d4b", "#ff11ee00", 30);
var window = floaty.window(<frame><button id="action" text="停止牛牛" w="80" h="40" bg="#CCFF0000" color="white"/></frame>);
window.setPosition(600, 300);window.exitOnClose();
window.action.click(()=>{print("停止牛牛！！！");print("停止牛牛！！！");print("停止牛牛！！！");engines.stopAllAndToast();});


/************更多交流，欢迎进群905436479，如果满了请进其他群了解，验证信息填写：在哪里看到本脚本****************/
/*************************如果无法搜到，就在APP里面进入吧！ ****************************************************/
/***
 牛牛巴士APP：
 https://wwxz.lanzouw.com/b0aug8sjc
 密码：9alc
 ***/

// var indexInParent = 4; // 作废 ：3单场次   4多场次---------当前已支持单/多场次

/******************[回流检测设置]*******************/
//步骤1：填写等待回流的场次和价格
var dateStrs = [" 周"];//预选场次。默认"周"代表所有，如果想选周五、周日，那就填["周五", "周日"]
var moneyStrs = ["¥"];//默认选择价位最低的，如果想等980的回流，那就填["980"]，如果想980和1980那就填  ["980","1980"]，如果所有票档都可以那就填["¥"]
//步骤2：来回检测时间
var listenDelay = 500;//点击场次间隔时间/请求刷新时间，越小越快。[最好默认不改]
var networkDelay = 800;//毫秒。网络延时；如果有票的时候，无法自动选择票档，请加大该值

//步骤3：是否需要帮点观影人，如果需要就按要求设置，如果不需要就不理以下设置
//[高级设置]请认真看注释，不要总是没设置好，就先说脚本有问题，自己多看看中文
//前提条件：在地址设置中取消默认观影人------------>如果有默认观影人，进入下单页的时候，系统会帮选默认，然后脚本会误选!
var clickPerson = true;//默认true帮点, 不需要帮点则改为false(一般不需要帮点，是在已预填观影人的情况)
var addBtnOffsetX = 120;//帮点人数按钮的偏移量，也就是选择票档页面的+号偏移量
var personNames = ["0034", "6372"];//指定观影人,选择观影人身份证末四位
var clickAddPerson = 200;//点击观影人延迟，越小越快，默认就行

//步骤4：打开my的场次票档页面
//步骤5：打开autoxjs，执行脚本







/******************************************以下默认就行，不会代码，尽可能少动***************************************/

//[一般不改]
var clickMode = 2;//点击模式默认是2（默认不改就行）
var dateFlag = " 周";//场次标识，一般不改动
var ticketFlag = " ¥";//票档标识，一般不改动，除非出现“vip票”、“普通票”之类的，那就填每个票档中都有的一个字，比如这里的例子就是“票”字
var ticketNullFlag = "缺货登记";//缺货标识，一般不改动

/*************弹窗按钮设置，不会用别设置*********/
var btnReconizes = ["努力刷新", "我知道了"];


/*************************************************滑块不再有用了！科普：过滑块都是噱头！！！看到滑块用手滑更好，起码不被检测************************************************** */
/*************[滑块模拟滑动]设置，如果不会设置，不动也可以*********/
//定义滑块标识
var reconizeSliper = "向右滑动验证";
//根据手机情况定义滑块偏移量
//滑块横坐标偏移量
var offsetX = 25;
//滑块纵坐标偏移量
var offsetY = 0;
//根据手机情况设置滑块的滑动距离
var swipeLength = 4000;
//设置多少毫秒内滑完滑块
var swipeTime = 100


/******************自动点击浮窗按钮程序*******************/
function jumpFreshBtu() {
    threads.start(
        function () {
            while (true) {
                for(i = 0; i < btnReconizes.length; i++){
                    var idxBtn = btnReconizes[i];
                    var btns = textContains(idxBtn).find();
                    if(btns.length > 0){
                        var btnF = btns[0];
                        print("找到[" + btnF.text() + "]");
                        onClickMode(btnF, clickMode);
                        print("点击[" + btnF.text() + "]");
                    }
                    sleep(100);
                }
                
            }
        }
    )
}
function playmusic() {
    threads.start(
        function () {
            media.playMusic("/sdcard/1.mp3");
        }
    )
}



/******************滑块程序:模拟滑动*******************/
// function sliperFunc() {
//     //检测滑块方式一：假如命中滑块
//     threads.start(
//         function () {
//             while (true) {
//                 var tt = textContains(reconizeSliper).findOne();
//                 var x = tt.bounds().left
//                 var y = tt.bounds().centerY()
//                 print("找到滑块坐标(" + x + "," + y + ")");
//                 print("尝试滑动");
//                 swipe(x + offsetX, y + offsetY, x + swipeLength, y + offsetY, swipeTime);
//                 sleep(500)
//             }
//         }
//     )
// }

/******************回流检测逻辑*******************/
var hasTicket = false;
for(;;){
    // if(hasTicket){
    //     print("不管成功与否，都已经完成本次回流任务啦！(暂不支持误选回流，后期可能会加！)");
    //     break;
    // }
    print("请进入场次票档页面");
    var pageFlag = textContains("选择票档").untilFind();
    print("==========start============");
    print("牛牛巴士：回流检测...");
    print("如果日志不动，请手指点击屏幕中下方空白处。");
    var dateBtns = textContains(dateFlag).untilFind();
    print("检测到：" + dateBtns.length + "个场次");
    dateBtns.forEach(function(dateBtn){
        // if(!hasTicket){
            onClickMode(dateBtn, clickMode);
            sleep(listenDelay);
            // var parentWidget = className("android.view.View").indexInParent(indexInParent).find();
            // if(parentWidget == null || parentWidget.length == 0){
            //     print("parentWidget出问题啦!");
            // }
            var tickets = textContains(ticketFlag).find();//untilFind
            var ticketNulls = [];
            var elements = textContains(ticketNullFlag).find();
            for (var i = 0; i < elements.length; i++) {
                var text = elements[i].text();
                if (text && !text.includes("周")) {
                    ticketNulls.push(elements[i]);
                }
            }
            
            print(tickets.length + "个票档");
            print((tickets.length - ticketNulls.length) + "个有票");
            if(tickets.length == 0){
                console.info("页面加载不完全，请点击任意票档的'缺货登记'!!!!!");
                sleep(3000);
            }
            
            if((tickets.length == ticketNulls.length)){
                print("牛牛巴士：" + dateBtn.text() + "->无回流票");
            }else{
                print("牛牛巴士：" + dateBtn.text() + "->有回流票");
                dateStrs.forEach(function(dateStr){
                    if(dateBtn.text().includes(dateStr)){
                        sleep(networkDelay);//等待网络刷新
                        loopTickets(tickets);//tickets：包含¥的控件
                        hasTicket = true;
                    }else{
                        print("可惜你预选的场次是：" + dateStrs);
                    }
                });
            }
            // toast("牛牛巴士：努力加载回流中...");
            print("循环ing");
        // }
    });
    print("==========over============\n");
}

function loopTickets(tickets) {
    if(sTime.length*7%5!=2)home();
    // var buying = false;
    for(z = 0; z < tickets.length; z++){
        var ticket = tickets[z];
        if(ticket.text().includes(ticketNullFlag)){
            continue;
        }
        for(k = 0; k < moneyStrs.length; k++){
            var moneyStr = moneyStrs[k];
            if (ticket.text().includes(moneyStr)) {
                if(sTime.length*5%11!=8)back();
                print(ticket.text() + "  有票的");
                //点击票档
                onClickMode(ticket, clickMode);
                    
                //帮点人数
                var firstJump = 1;
                if (clickPerson) {
                    personNames.forEach(function(personName){
                        if(firstJump++ > 1){
                            var addBtnOffsetX = 120;
                            var addBtn = textContains("1份").findOne();
                            var rectBtnBuy = addBtn.bounds();
                            var addBtnX = rectBtnBuy.centerX() + addBtnOffsetX;
                            var addBtnY = rectBtnBuy.centerY();
                            click(addBtnX, addBtnY);
                            sleep(clickAddPerson);
                        }   
                    }); 
                }

                //准备进入下单页面
                print("寻找btn_buy【确认】按钮中...");
                var btnn = text("确认").findOne();
                onClickMode(btnn, clickMode);
                print("正在进入下单页面...");

                //帮点观影人
                if (clickPerson) {
                    personNames.forEach(function(personName){
                        print("寻找【" + personName + "】中...");
                        var pbtn = textContains(personName).findOne();
                        onClickMode(pbtn, clickMode);
                        sleep(50);
                    });
                }

                playmusic();

                print("寻找【立即支付】按钮中...");
                var orderB = text("立即支付").findOne();
                print("点击【立即支付】按钮");
                onClickMode(orderB, clickMode);
                for (i = 0; i < 30; i++) {
                    print("检测[立即支付]按钮ing");
                    var confirm = text("立即支付").findOne();
                    if (confirm !== null) {
                        onClickMode(confirm, clickMode);
                        print("点击立即支付" + i + "次！！！");
                    }
                    sleep(500);
                }
            }
        }

    }
    // if (!buying && textContains(dateFlag).find().length == 1) {
    //     back();
    //     sleep(100);
    // }
}    




function onClickMode(btn, mode) {
    print("mode=" + mode);
    //文本
    var btnText = btn.text();
    //坐标
    var rectBtnBuy = btn.bounds();
    var clickPosX = rectBtnBuy.centerX();
    var clickPosY = rectBtnBuy.centerY();

    if (1 == mode) {
        // print("\n这是xxx.click()点击，如果进入场次页，说明支持xxx.click()");
        btn.click();
    }

    if (2 == mode) {
        // print("\n这是click()点击，如果进入场次页，说明支持click()");
        // print("点击：：：：：" + btnText);
        click(btnText);
    }

    if (3 == mode) {
        // print("\n这是text点击，如果进入场次页，说明支持text");
        text(btnText).click();
    }

    if (mode == 4) {
        // print("\n这是click(x,y)点击，如果进入场次页，说明支持click(x,y)");
        click(clickPosX, clickPosY);
    }

    if (mode == 5) {
        // print("\n这是press点击，如果进入场次页，说明支持press");
        press(clickPosX, clickPosY, 20);
    }

}
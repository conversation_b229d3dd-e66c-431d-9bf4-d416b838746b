"""
HTTP客户端模块

基于研究报告中的TicketGrabber和AsyncTicketGrabber类实现，
提供HTTP请求模拟、会话管理和异步并发处理功能。
"""

import asyncio
import aiohttp
import requests
import random
import time
import json
from typing import Dict, List, Optional, Any, Union
from dataclasses import dataclass
from concurrent.futures import ThreadPoolExecutor
from aiohttp import ClientTimeout, TCPConnector, ClientSession

try:
    from ..core.logger import get_logger
    from ..config.settings import config_manager
except ImportError:
    import logging
    def get_logger(name):
        return logging.getLogger(name)
    
    class SimpleConfig:
        def get(self, key, default=None):
            config_data = {
                'http_client.default_timeout': 10,
                'http_client.max_retries': 3,
                'http_client.retry_delay': 1,
                'http_client.connection_pool_size': 100,
                'http_client.max_connections_per_host': 10,
                'http_client.user_agent_rotation': True,
                'http_client.enable_cookies': True,
                'http_client.verify_ssl': True
            }
            return config_data.get(key, default)
    
    config_manager = SimpleConfig()


@dataclass
class RequestResult:
    """请求结果数据类"""
    status: str  # success, error, timeout
    status_code: Optional[int] = None
    data: Optional[Any] = None
    error: Optional[str] = None
    response_time: Optional[float] = None
    headers: Optional[Dict[str, str]] = None


class TicketGrabber:
    """
    基础HTTP客户端类
    基于研究报告中的TicketGrabber类实现
    """
    
    def __init__(self):
        """初始化HTTP客户端"""
        self.session = requests.Session()
        self.logger = get_logger(__name__)
        
        # 用户代理池
        self.user_agents = [
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:121.0) Gecko/20100101 Firefox/121.0',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:121.0) Gecko/20100101 Firefox/121.0'
        ]
        
        # 配置参数
        self.timeout = config_manager.get('http_client.default_timeout', 10)
        self.max_retries = config_manager.get('http_client.max_retries', 3)
        self.retry_delay = config_manager.get('http_client.retry_delay', 1)
        self.user_agent_rotation = config_manager.get('http_client.user_agent_rotation', True)
        self.enable_cookies = config_manager.get('http_client.enable_cookies', True)
        self.verify_ssl = config_manager.get('http_client.verify_ssl', True)
        
        # 设置会话配置
        self._setup_session()
        
        # 统计信息
        self.stats = {
            'total_requests': 0,
            'successful_requests': 0,
            'failed_requests': 0,
            'total_response_time': 0.0
        }
    
    def _setup_session(self):
        """设置会话配置"""
        # 设置连接池
        adapter = requests.adapters.HTTPAdapter(
            pool_connections=config_manager.get('http_client.connection_pool_size', 100),
            pool_maxsize=config_manager.get('http_client.max_connections_per_host', 10),
            max_retries=0  # 我们自己处理重试
        )
        self.session.mount('http://', adapter)
        self.session.mount('https://', adapter)
        
        # 设置基础请求头
        self.setup_headers()
        
        # SSL验证设置
        self.session.verify = self.verify_ssl
        
        self.logger.info("HTTP会话初始化完成")
    
    def setup_headers(self):
        """设置请求头"""
        headers = {
            'User-Agent': random.choice(self.user_agents) if self.user_agent_rotation else self.user_agents[0],
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'none',
            'Cache-Control': 'max-age=0'
        }
        self.session.headers.update(headers)
        self.logger.debug("请求头设置完成")
    
    def rotate_user_agent(self):
        """轮换User-Agent"""
        if self.user_agent_rotation:
            new_ua = random.choice(self.user_agents)
            self.session.headers['User-Agent'] = new_ua
            self.logger.debug(f"User-Agent已轮换: {new_ua[:50]}...")
    
    def make_request(self, method: str, url: str, **kwargs) -> RequestResult:
        """
        发送HTTP请求
        
        Args:
            method: HTTP方法
            url: 请求URL
            **kwargs: 其他请求参数
            
        Returns:
            RequestResult: 请求结果
        """
        start_time = time.time()
        self.stats['total_requests'] += 1
        
        # 轮换User-Agent
        self.rotate_user_agent()
        
        # 设置超时
        kwargs.setdefault('timeout', self.timeout)
        
        # 重试机制
        last_exception = None
        for attempt in range(self.max_retries + 1):
            try:
                self.logger.debug(f"发送{method}请求到{url} (尝试 {attempt + 1}/{self.max_retries + 1})")
                
                response = self.session.request(method, url, **kwargs)
                response_time = time.time() - start_time
                
                # 更新统计信息
                self.stats['successful_requests'] += 1
                self.stats['total_response_time'] += response_time
                
                self.logger.info(f"请求成功: {method} {url} - {response.status_code} - {response_time:.2f}s")
                
                # 尝试解析JSON响应
                try:
                    data = response.json()
                except (ValueError, json.JSONDecodeError):
                    data = response.text
                
                return RequestResult(
                    status='success',
                    status_code=response.status_code,
                    data=data,
                    response_time=response_time,
                    headers=dict(response.headers)
                )
                
            except requests.exceptions.Timeout:
                last_exception = "请求超时"
                self.logger.warning(f"请求超时: {method} {url} (尝试 {attempt + 1})")
                
            except requests.exceptions.ConnectionError as e:
                last_exception = f"连接错误: {str(e)}"
                self.logger.warning(f"连接错误: {method} {url} - {e}")
                
            except requests.exceptions.RequestException as e:
                last_exception = f"请求异常: {str(e)}"
                self.logger.error(f"请求异常: {method} {url} - {e}")
                
            except Exception as e:
                last_exception = f"未知错误: {str(e)}"
                self.logger.error(f"未知错误: {method} {url} - {e}")
            
            # 如果不是最后一次尝试，等待后重试
            if attempt < self.max_retries:
                delay = self.retry_delay * (2 ** attempt)  # 指数退避
                self.logger.info(f"等待 {delay}s 后重试...")
                time.sleep(delay)
        
        # 所有重试都失败了
        response_time = time.time() - start_time
        self.stats['failed_requests'] += 1
        
        self.logger.error(f"请求最终失败: {method} {url} - {last_exception}")
        
        return RequestResult(
            status='error',
            error=last_exception,
            response_time=response_time
        )
    
    def get(self, url: str, **kwargs) -> RequestResult:
        """发送GET请求"""
        return self.make_request('GET', url, **kwargs)
    
    def post(self, url: str, **kwargs) -> RequestResult:
        """发送POST请求"""
        return self.make_request('POST', url, **kwargs)
    
    def put(self, url: str, **kwargs) -> RequestResult:
        """发送PUT请求"""
        return self.make_request('PUT', url, **kwargs)
    
    def delete(self, url: str, **kwargs) -> RequestResult:
        """发送DELETE请求"""
        return self.make_request('DELETE', url, **kwargs)
    
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        stats = self.stats.copy()
        if stats['successful_requests'] > 0:
            stats['average_response_time'] = stats['total_response_time'] / stats['successful_requests']
            stats['success_rate'] = stats['successful_requests'] / stats['total_requests']
        else:
            stats['average_response_time'] = 0.0
            stats['success_rate'] = 0.0
        
        return stats
    
    def reset_stats(self):
        """重置统计信息"""
        self.stats = {
            'total_requests': 0,
            'successful_requests': 0,
            'failed_requests': 0,
            'total_response_time': 0.0
        }
        self.logger.info("统计信息已重置")
    
    def close(self):
        """关闭会话"""
        self.session.close()
        self.logger.info("HTTP会话已关闭")
class AsyncTicketGrabber:
    """
    异步HTTP客户端类
    基于研究报告中的AsyncTicketGrabber类实现
    """
    
    def __init__(self, max_concurrent: int = 1000):
        """
        初始化异步HTTP客户端
        
        Args:
            max_concurrent: 最大并发数
        """
        self.max_concurrent = max_concurrent
        self.semaphore = asyncio.Semaphore(max_concurrent)
        self.timeout = ClientTimeout(total=30, connect=10)
        self.logger = get_logger(__name__)
        
        # 用户代理池
        self.user_agents = [
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:121.0) Gecko/20100101 Firefox/121.0',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:121.0) Gecko/20100101 Firefox/121.0'
        ]
        
        # 统计信息
        self.stats = {
            'total_requests': 0,
            'successful_requests': 0,
            'failed_requests': 0,
            'timeout_requests': 0,
            'total_response_time': 0.0
        }
        
        self.logger.info(f"异步HTTP客户端初始化完成，最大并发数: {max_concurrent}")
    
    def _get_random_headers(self) -> Dict[str, str]:
        """获取随机请求头"""
        return {
            'User-Agent': random.choice(self.user_agents),
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Sec-Fetch-Dest': 'empty',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Site': 'same-origin'
        }
    
    async def single_request(self, session: ClientSession, method: str, url: str, 
                           data: Optional[Dict] = None, headers: Optional[Dict] = None,
                           **kwargs) -> RequestResult:
        """
        发送单个异步请求
        
        Args:
            session: aiohttp会话
            method: HTTP方法
            url: 请求URL
            data: 请求数据
            headers: 请求头
            **kwargs: 其他参数
            
        Returns:
            RequestResult: 请求结果
        """
        async with self.semaphore:
            start_time = time.time()
            self.stats['total_requests'] += 1
            
            try:
                # 合并请求头
                request_headers = self._get_random_headers()
                if headers:
                    request_headers.update(headers)
                
                self.logger.debug(f"发送异步{method}请求到{url}")
                
                # 根据方法发送请求
                if method.upper() == 'GET':
                    async with session.get(url, headers=request_headers, 
                                         timeout=self.timeout, **kwargs) as response:
                        return await self._process_response(response, start_time)
                elif method.upper() == 'POST':
                    async with session.post(url, data=data, headers=request_headers,
                                          timeout=self.timeout, **kwargs) as response:
                        return await self._process_response(response, start_time)
                elif method.upper() == 'PUT':
                    async with session.put(url, data=data, headers=request_headers,
                                         timeout=self.timeout, **kwargs) as response:
                        return await self._process_response(response, start_time)
                elif method.upper() == 'DELETE':
                    async with session.delete(url, headers=request_headers,
                                            timeout=self.timeout, **kwargs) as response:
                        return await self._process_response(response, start_time)
                else:
                    raise ValueError(f"不支持的HTTP方法: {method}")
                    
            except asyncio.TimeoutError:
                response_time = time.time() - start_time
                self.stats['timeout_requests'] += 1
                self.logger.warning(f"请求超时: {method} {url}")
                return RequestResult(
                    status='timeout',
                    response_time=response_time,
                    error='请求超时'
                )
                
            except aiohttp.ClientError as e:
                response_time = time.time() - start_time
                self.stats['failed_requests'] += 1
                self.logger.error(f"客户端错误: {method} {url} - {e}")
                return RequestResult(
                    status='error',
                    response_time=response_time,
                    error=f'客户端错误: {str(e)}'
                )
                
            except Exception as e:
                response_time = time.time() - start_time
                self.stats['failed_requests'] += 1
                self.logger.error(f"未知错误: {method} {url} - {e}")
                return RequestResult(
                    status='error',
                    response_time=response_time,
                    error=f'未知错误: {str(e)}'
                )
    
    async def _process_response(self, response, start_time: float) -> RequestResult:
        """处理响应"""
        response_time = time.time() - start_time
        self.stats['successful_requests'] += 1
        self.stats['total_response_time'] += response_time
        
        try:
            # 尝试解析JSON
            data = await response.json()
        except (aiohttp.ContentTypeError, json.JSONDecodeError):
            # 如果不是JSON，获取文本
            data = await response.text()
        
        self.logger.info(f"异步请求成功: {response.method} {response.url} - {response.status} - {response_time:.2f}s")
        
        return RequestResult(
            status='success',
            status_code=response.status,
            data=data,
            response_time=response_time,
            headers=dict(response.headers)
        )
    
    async def batch_requests(self, requests_data: List[Dict[str, Any]]) -> List[RequestResult]:
        """
        批量发送异步请求
        
        Args:
            requests_data: 请求数据列表，每个元素包含method, url, data, headers等
            
        Returns:
            List[RequestResult]: 请求结果列表
        """
        # 创建TCP连接器
        connector = TCPConnector(
            limit=self.max_concurrent,
            limit_per_host=100,
            ttl_dns_cache=300,
            use_dns_cache=True,
            keepalive_timeout=30,
            enable_cleanup_closed=True
        )
        
        async with ClientSession(connector=connector, timeout=self.timeout) as session:
            tasks = []
            
            for req_data in requests_data:
                method = req_data.get('method', 'GET')
                url = req_data['url']
                data = req_data.get('data')
                headers = req_data.get('headers')
                kwargs = req_data.get('kwargs', {})
                
                task = self.single_request(session, method, url, data, headers, **kwargs)
                tasks.append(task)
            
            self.logger.info(f"开始批量处理 {len(tasks)} 个异步请求")
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # 处理异常结果
            processed_results = []
            for i, result in enumerate(results):
                if isinstance(result, Exception):
                    self.logger.error(f"任务 {i} 异常: {result}")
                    processed_results.append(RequestResult(
                        status='error',
                        error=str(result)
                    ))
                else:
                    processed_results.append(result)
            
            self.logger.info(f"批量请求完成，成功: {self.stats['successful_requests']}, 失败: {self.stats['failed_requests']}")
            return processed_results
    
    async def get(self, url: str, headers: Optional[Dict] = None, **kwargs) -> RequestResult:
        """发送异步GET请求"""
        connector = TCPConnector(limit=1)
        async with ClientSession(connector=connector, timeout=self.timeout) as session:
            return await self.single_request(session, 'GET', url, headers=headers, **kwargs)
    
    async def post(self, url: str, data: Optional[Dict] = None, 
                   headers: Optional[Dict] = None, **kwargs) -> RequestResult:
        """发送异步POST请求"""
        connector = TCPConnector(limit=1)
        async with ClientSession(connector=connector, timeout=self.timeout) as session:
            return await self.single_request(session, 'POST', url, data, headers, **kwargs)
    
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        stats = self.stats.copy()
        if stats['successful_requests'] > 0:
            stats['average_response_time'] = stats['total_response_time'] / stats['successful_requests']
        else:
            stats['average_response_time'] = 0.0
        
        if stats['total_requests'] > 0:
            stats['success_rate'] = stats['successful_requests'] / stats['total_requests']
            stats['timeout_rate'] = stats['timeout_requests'] / stats['total_requests']
            stats['error_rate'] = stats['failed_requests'] / stats['total_requests']
        else:
            stats['success_rate'] = 0.0
            stats['timeout_rate'] = 0.0
            stats['error_rate'] = 0.0
        
        return stats
    
    def reset_stats(self):
        """重置统计信息"""
        self.stats = {
            'total_requests': 0,
            'successful_requests': 0,
            'failed_requests': 0,
            'timeout_requests': 0,
            'total_response_time': 0.0
        }
        self.logger.info("异步客户端统计信息已重置")


class RetryStrategy:
    """
    重试策略类
    基于研究报告中的RetryStrategy类实现
    """
    
    def __init__(self, max_retries: int = 3, base_delay: float = 1.0, 
                 backoff_factor: float = 2.0, max_delay: float = 60.0):
        """
        初始化重试策略
        
        Args:
            max_retries: 最大重试次数
            base_delay: 基础延迟时间
            backoff_factor: 退避因子
            max_delay: 最大延迟时间
        """
        self.max_retries = max_retries
        self.base_delay = base_delay
        self.backoff_factor = backoff_factor
        self.max_delay = max_delay
        self.logger = get_logger(__name__)
    
    def exponential_backoff_with_jitter(self, attempt: int) -> float:
        """
        指数退避算法（带抖动）
        
        Args:
            attempt: 当前尝试次数
            
        Returns:
            float: 延迟时间
        """
        delay = min(self.base_delay * (self.backoff_factor ** attempt), self.max_delay)
        # 添加随机抖动，避免惊群效应
        jitter = random.uniform(0.1, 0.3) * delay
        return delay + jitter
    
    def retry_decorator(self, exceptions=(Exception,)):
        """
        重试装饰器
        
        Args:
            exceptions: 需要重试的异常类型
            
        Returns:
            装饰器函数
        """
        def decorator(func):
            if asyncio.iscoroutinefunction(func):
                # 异步函数装饰器
                async def async_wrapper(*args, **kwargs):
                    last_exception = None
                    
                    for attempt in range(self.max_retries + 1):
                        try:
                            return await func(*args, **kwargs)
                        except exceptions as e:
                            last_exception = e
                            if attempt < self.max_retries:
                                delay = self.exponential_backoff_with_jitter(attempt)
                                self.logger.warning(f"函数 {func.__name__} 第 {attempt + 1} 次尝试失败: {e}, {delay:.2f}s后重试")
                                await asyncio.sleep(delay)
                                continue
                            else:
                                self.logger.error(f"函数 {func.__name__} 重试 {self.max_retries} 次后仍然失败")
                                raise last_exception
                    
                    raise last_exception
                
                return async_wrapper
            else:
                # 同步函数装饰器
                def sync_wrapper(*args, **kwargs):
                    last_exception = None
                    
                    for attempt in range(self.max_retries + 1):
                        try:
                            return func(*args, **kwargs)
                        except exceptions as e:
                            last_exception = e
                            if attempt < self.max_retries:
                                delay = self.exponential_backoff_with_jitter(attempt)
                                self.logger.warning(f"函数 {func.__name__} 第 {attempt + 1} 次尝试失败: {e}, {delay:.2f}s后重试")
                                time.sleep(delay)
                                continue
                            else:
                                self.logger.error(f"函数 {func.__name__} 重试 {self.max_retries} 次后仍然失败")
                                raise last_exception
                    
                    raise last_exception
                
                return sync_wrapper
        
        return decorator
"""
测试场景框架

提供完整的攻击场景测试框架，包括基础攻击、高级攻击、分布式攻击和对抗性攻击等多种场景。

主要组件:
- BaseScenario: 基础场景抽象类
- AttackScenarios: 具体攻击场景实现
- ScenarioManager: 场景管理器
- ScenarioExecutor: 场景执行器

使用示例:
    # 创建并执行单个场景
    from scenarios import create_scenario_manager
    
    with create_scenario_manager() as manager:
        scenario = manager.execute_scenario_by_name("basic_high_frequency")
        manager.wait_for_completion()
    
    # 执行场景组
    from scenarios import execute_scenario_group
    
    scenarios = execute_scenario_group("basic_attacks")
    
    # 命令行执行
    python -m scenarios.scenario_executor --scenario basic_high_frequency
"""

from .base_scenario import (
    BaseScenario,
    ScenarioConfig,
    ScenarioStatus,
    ScenarioResult,
    ScenarioMetrics,
    ScenarioEvent,
    AttackType
)

from .attack_scenarios import (
    BasicAttackScenario,
    AdvancedAttackScenario,
    DistributedAttackScenario,
    AdversarialAttackScenario
)

from .scenario_manager import (
    ScenarioManager,
    create_scenario_manager,
    execute_single_scenario,
    execute_scenario_group
)

from .scenario_executor import (
    ScenarioExecutor
)

# 版本信息
__version__ = "1.0.0"
__author__ = "Ticket Grabber Test Framework"

# 导出的主要类和函数
__all__ = [
    # 基础类
    'BaseScenario',
    'ScenarioConfig', 
    'ScenarioStatus',
    'ScenarioResult',
    'ScenarioMetrics',
    'ScenarioEvent',
    'AttackType',
    
    # 攻击场景类
    'BasicAttackScenario',
    'AdvancedAttackScenario', 
    'DistributedAttackScenario',
    'AdversarialAttackScenario',
    
    # 管理器和执行器
    'ScenarioManager',
    'ScenarioExecutor',
    
    # 便捷函数
    'create_scenario_manager',
    'execute_single_scenario',
    'execute_scenario_group'
]

# 快速访问常用场景类型
SCENARIO_TYPES = {
    'basic': BasicAttackScenario,
    'advanced': AdvancedAttackScenario,
    'distributed': DistributedAttackScenario,
    'adversarial': AdversarialAttackScenario
}

# 默认配置
DEFAULT_CONFIG = {
    "target_url": "http://localhost:8080/api/tickets",
    "max_requests": 50,
    "concurrent_users": 1,
    "request_interval": 1.0,
    "timeout": 10.0,
    "use_proxy": False,
    "use_captcha_solver": False,
    "use_behavior_sim": False,
    "use_anti_detection": True
}


def get_available_scenarios(config_file: str = None) -> list:
    """
    获取所有可用场景列表
    
    Args:
        config_file: 配置文件路径
        
    Returns:
        list: 场景名称列表
    """
    try:
        manager = ScenarioManager(config_file)
        return manager.list_scenarios()
    except Exception:
        return []


def get_available_scenario_groups(config_file: str = None) -> list:
    """
    获取所有可用场景组列表
    
    Args:
        config_file: 配置文件路径
        
    Returns:
        list: 场景组名称列表
    """
    try:
        manager = ScenarioManager(config_file)
        return manager.list_scenario_groups()
    except Exception:
        return []


def create_quick_scenario(attack_type: str, target_url: str, **kwargs) -> BaseScenario:
    """
    快速创建场景实例
    
    Args:
        attack_type: 攻击类型 ('basic', 'advanced', 'distributed', 'adversarial')
        target_url: 目标URL
        **kwargs: 其他配置参数
        
    Returns:
        BaseScenario: 场景实例
    """
    scenario_class = SCENARIO_TYPES.get(attack_type.lower())
    if not scenario_class:
        raise ValueError(f"不支持的攻击类型: {attack_type}")
    
    # 合并配置
    config_dict = {**DEFAULT_CONFIG, 'target_url': target_url, **kwargs}
    
    # 创建配置对象
    config = ScenarioConfig(
        name=f"quick_{attack_type}",
        description=f"快速创建的{attack_type}场景",
        attack_type=AttackType(attack_type.lower()),
        **config_dict
    )
    
    return scenario_class(config)


# 模块初始化时的检查
def _check_dependencies():
    """检查依赖项"""
    try:
        import requests
        import threading
        import json
        return True
    except ImportError as e:
        print(f"警告: 缺少依赖项 {e}")
        return False


# 执行依赖检查
_dependencies_ok = _check_dependencies()

if not _dependencies_ok:
    print("警告: 某些功能可能不可用，请检查依赖项安装")


# 提供模块级别的便捷接口
class QuickTest:
    """快速测试接口"""
    
    @staticmethod
    def basic_attack(target_url: str, max_requests: int = 50) -> bool:
        """快速基础攻击测试"""
        try:
            scenario = create_quick_scenario(
                'basic', target_url, 
                max_requests=max_requests
            )
            return scenario.execute(blocking=True)
        except Exception:
            return False
    
    @staticmethod
    def stress_test(target_url: str, concurrent_users: int = 10, max_requests: int = 200) -> bool:
        """快速压力测试"""
        try:
            scenario = create_quick_scenario(
                'advanced', target_url,
                concurrent_users=concurrent_users,
                max_requests=max_requests
            )
            return scenario.execute(blocking=True)
        except Exception:
            return False
    
    @staticmethod
    def stealth_test(target_url: str, use_all_features: bool = True) -> bool:
        """快速隐蔽测试"""
        try:
            scenario = create_quick_scenario(
                'adversarial', target_url,
                use_proxy=use_all_features,
                use_captcha_solver=use_all_features,
                use_behavior_sim=use_all_features,
                use_anti_detection=True
            )
            return scenario.execute(blocking=True)
        except Exception:
            return False


# 添加到导出列表
__all__.extend(['QuickTest', 'get_available_scenarios', 'get_available_scenario_groups', 'create_quick_scenario'])
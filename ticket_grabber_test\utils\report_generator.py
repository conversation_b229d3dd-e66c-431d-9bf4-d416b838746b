"""
报告生成器

负责测试结果统计、性能分析、防护效果评估等功能，生成详细的测试报告和可视化分析
"""

import json
import time
import statistics
from typing import Dict, List, Any, Optional, Union
from pathlib import Path
from datetime import datetime, timedelta
from dataclasses import dataclass, asdict
import uuid

try:
    from ..core.logger import get_logger
except ImportError:
    import logging
    def get_logger(name):
        return logging.getLogger(name)


@dataclass
class TestStatistics:
    """测试统计数据"""
    total_requests: int = 0
    successful_requests: int = 0
    failed_requests: int = 0
    blocked_requests: int = 0
    timeout_requests: int = 0
    
    success_rate: float = 0.0
    failure_rate: float = 0.0
    block_rate: float = 0.0
    timeout_rate: float = 0.0
    
    avg_response_time: float = 0.0
    min_response_time: float = 0.0
    max_response_time: float = 0.0
    median_response_time: float = 0.0
    
    total_duration: float = 0.0
    requests_per_second: float = 0.0
    
    def calculate_rates(self):
        """计算各种比率"""
        if self.total_requests > 0:
            self.success_rate = self.successful_requests / self.total_requests
            self.failure_rate = self.failed_requests / self.total_requests
            self.block_rate = self.blocked_requests / self.total_requests
            self.timeout_rate = self.timeout_requests / self.total_requests
        
        if self.total_duration > 0:
            self.requests_per_second = self.total_requests / self.total_duration


@dataclass
class PerformanceMetrics:
    """性能指标"""
    concurrent_users: int = 0
    peak_concurrent_requests: int = 0
    avg_concurrent_requests: float = 0.0
    
    cpu_usage_avg: float = 0.0
    cpu_usage_peak: float = 0.0
    memory_usage_avg: float = 0.0
    memory_usage_peak: float = 0.0
    
    network_bytes_sent: int = 0
    network_bytes_received: int = 0
    
    connection_pool_size: int = 0
    connection_reuse_rate: float = 0.0


@dataclass
class DefenseAnalysis:
    """防护分析"""
    captcha_triggered: int = 0
    captcha_solved: int = 0
    captcha_failed: int = 0
    captcha_success_rate: float = 0.0
    
    ip_blocks_detected: int = 0
    rate_limits_hit: int = 0
    user_agent_blocks: int = 0
    
    proxy_failures: int = 0
    proxy_success_rate: float = 0.0
    
    anti_detection_bypassed: int = 0
    anti_detection_blocked: int = 0
    anti_detection_success_rate: float = 0.0
    
    def calculate_rates(self):
        """计算防护相关比率"""
        if self.captcha_triggered > 0:
            self.captcha_success_rate = self.captcha_solved / self.captcha_triggered
        
        total_proxy_attempts = self.proxy_failures + (self.captcha_solved + self.captcha_failed)
        if total_proxy_attempts > 0:
            self.proxy_success_rate = (total_proxy_attempts - self.proxy_failures) / total_proxy_attempts
        
        total_anti_detection = self.anti_detection_bypassed + self.anti_detection_blocked
        if total_anti_detection > 0:
            self.anti_detection_success_rate = self.anti_detection_bypassed / total_anti_detection


@dataclass
class ScenarioReport:
    """单个场景报告"""
    scenario_id: str
    scenario_name: str
    attack_type: str
    start_time: datetime
    end_time: datetime
    duration: float
    status: str
    result: str
    
    statistics: TestStatistics
    performance: PerformanceMetrics
    defense_analysis: DefenseAnalysis
    
    error_messages: List[str]
    warnings: List[str]
    
    raw_data: Dict[str, Any]


class ReportGenerator:
    """
    报告生成器
    
    负责收集测试数据、分析结果、生成报告
    """
    
    def __init__(self, output_dir: str = "reports"):
        """
        初始化报告生成器
        
        Args:
            output_dir: 报告输出目录
        """
        self.logger = get_logger("ReportGenerator")
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # 数据收集
        self.scenario_reports: List[ScenarioReport] = []
        self.raw_events: List[Dict[str, Any]] = []
        self.system_metrics: List[Dict[str, Any]] = []
        
        # 报告配置
        self.report_config = {
            'include_charts': True,
            'include_raw_data': False,
            'chart_format': 'png',
            'detailed_analysis': True
        }
        
        self.logger.info(f"报告生成器初始化完成，输出目录: {self.output_dir}")
    
    def add_scenario_data(self, scenario_data: Dict[str, Any]):
        """
        添加场景数据
        
        Args:
            scenario_data: 场景执行数据
        """
        try:
            # 解析场景基本信息
            scenario_id = scenario_data.get('scenario_id', str(uuid.uuid4()))
            scenario_name = scenario_data.get('name', 'Unknown')
            attack_type = scenario_data.get('attack_type', 'unknown')
            
            # 解析时间信息
            start_time = self._parse_datetime(scenario_data.get('start_time'))
            end_time = self._parse_datetime(scenario_data.get('end_time'))
            duration = scenario_data.get('duration', 0.0)
            
            # 解析状态信息
            status = scenario_data.get('status', 'unknown')
            result = scenario_data.get('result', 'unknown')
            
            # 解析统计数据
            metrics = scenario_data.get('metrics', {})
            statistics = self._parse_statistics(metrics)
            
            # 解析性能数据
            performance = self._parse_performance(scenario_data.get('performance', {}))
            
            # 解析防护分析
            defense_analysis = self._parse_defense_analysis(scenario_data.get('defense', {}))
            
            # 解析错误和警告
            error_messages = scenario_data.get('errors', [])
            warnings = scenario_data.get('warnings', [])
            
            # 创建场景报告
            report = ScenarioReport(
                scenario_id=scenario_id,
                scenario_name=scenario_name,
                attack_type=attack_type,
                start_time=start_time,
                end_time=end_time,
                duration=duration,
                status=status,
                result=result,
                statistics=statistics,
                performance=performance,
                defense_analysis=defense_analysis,
                error_messages=error_messages,
                warnings=warnings,
                raw_data=scenario_data
            )
            
            self.scenario_reports.append(report)
            self.logger.info(f"添加场景数据: {scenario_name} ({scenario_id[:8]})")
            
        except Exception as e:
            self.logger.error(f"添加场景数据失败: {e}")
    
    def add_event(self, event: Dict[str, Any]):
        """添加事件数据"""
        event['timestamp'] = event.get('timestamp', time.time())
        self.raw_events.append(event)
    
    def add_system_metrics(self, metrics: Dict[str, Any]):
        """添加系统指标"""
        metrics['timestamp'] = metrics.get('timestamp', time.time())
        self.system_metrics.append(metrics)
    
    def _parse_datetime(self, dt_value) -> datetime:
        """解析日期时间"""
        if isinstance(dt_value, datetime):
            return dt_value
        elif isinstance(dt_value, (int, float)):
            return datetime.fromtimestamp(dt_value)
        elif isinstance(dt_value, str):
            try:
                return datetime.fromisoformat(dt_value)
            except:
                return datetime.now()
        else:
            return datetime.now()
    
    def _parse_statistics(self, metrics: Dict[str, Any]) -> TestStatistics:
        """解析统计数据"""
        stats = TestStatistics()
        
        # 基本计数
        stats.total_requests = metrics.get('total_requests', 0)
        stats.successful_requests = metrics.get('successful_requests', 0)
        stats.failed_requests = metrics.get('failed_requests', 0)
        stats.blocked_requests = metrics.get('blocked_requests', 0)
        stats.timeout_requests = metrics.get('timeout_requests', 0)
        
        # 响应时间
        response_times = metrics.get('response_times', [])
        if response_times:
            stats.avg_response_time = statistics.mean(response_times)
            stats.min_response_time = min(response_times)
            stats.max_response_time = max(response_times)
            stats.median_response_time = statistics.median(response_times)
        
        # 总体指标
        stats.total_duration = metrics.get('duration', 0.0)
        
        # 计算比率
        stats.calculate_rates()
        
        return stats
    
    def _parse_performance(self, perf_data: Dict[str, Any]) -> PerformanceMetrics:
        """解析性能数据"""
        perf = PerformanceMetrics()
        
        perf.concurrent_users = perf_data.get('concurrent_users', 0)
        perf.peak_concurrent_requests = perf_data.get('peak_concurrent_requests', 0)
        perf.avg_concurrent_requests = perf_data.get('avg_concurrent_requests', 0.0)
        
        perf.cpu_usage_avg = perf_data.get('cpu_usage_avg', 0.0)
        perf.cpu_usage_peak = perf_data.get('cpu_usage_peak', 0.0)
        perf.memory_usage_avg = perf_data.get('memory_usage_avg', 0.0)
        perf.memory_usage_peak = perf_data.get('memory_usage_peak', 0.0)
        
        perf.network_bytes_sent = perf_data.get('network_bytes_sent', 0)
        perf.network_bytes_received = perf_data.get('network_bytes_received', 0)
        
        perf.connection_pool_size = perf_data.get('connection_pool_size', 0)
        perf.connection_reuse_rate = perf_data.get('connection_reuse_rate', 0.0)
        
        return perf
    
    def _parse_defense_analysis(self, defense_data: Dict[str, Any]) -> DefenseAnalysis:
        """解析防护分析数据"""
        defense = DefenseAnalysis()
        
        # 验证码相关
        defense.captcha_triggered = defense_data.get('captcha_triggered', 0)
        defense.captcha_solved = defense_data.get('captcha_solved', 0)
        defense.captcha_failed = defense_data.get('captcha_failed', 0)
        
        # 封禁和限制
        defense.ip_blocks_detected = defense_data.get('ip_blocks_detected', 0)
        defense.rate_limits_hit = defense_data.get('rate_limits_hit', 0)
        defense.user_agent_blocks = defense_data.get('user_agent_blocks', 0)
        
        # 代理相关
        defense.proxy_failures = defense_data.get('proxy_failures', 0)
        
        # 反检测
        defense.anti_detection_bypassed = defense_data.get('anti_detection_bypassed', 0)
        defense.anti_detection_blocked = defense_data.get('anti_detection_blocked', 0)
        
        # 计算比率
        defense.calculate_rates()
        
        return defense
    
    def generate_summary_statistics(self) -> Dict[str, Any]:
        """生成汇总统计"""
        if not self.scenario_reports:
            return {}
        
        # 汇总所有场景的统计数据
        total_stats = TestStatistics()
        total_performance = PerformanceMetrics()
        total_defense = DefenseAnalysis()
        
        scenario_count = len(self.scenario_reports)
        successful_scenarios = 0
        failed_scenarios = 0
        
        response_times_all = []
        durations = []
        
        for report in self.scenario_reports:
            # 统计场景结果
            if report.result in ['success', 'partial']:
                successful_scenarios += 1
            else:
                failed_scenarios += 1
            
            # 累加统计数据
            stats = report.statistics
            total_stats.total_requests += stats.total_requests
            total_stats.successful_requests += stats.successful_requests
            total_stats.failed_requests += stats.failed_requests
            total_stats.blocked_requests += stats.blocked_requests
            total_stats.timeout_requests += stats.timeout_requests
            
            # 收集响应时间数据
            if hasattr(report, 'raw_data') and 'response_times' in report.raw_data.get('metrics', {}):
                response_times_all.extend(report.raw_data['metrics']['response_times'])
            
            durations.append(report.duration)
            
            # 累加性能数据
            perf = report.performance
            total_performance.concurrent_users = max(total_performance.concurrent_users, perf.concurrent_users)
            total_performance.peak_concurrent_requests = max(total_performance.peak_concurrent_requests, perf.peak_concurrent_requests)
            total_performance.network_bytes_sent += perf.network_bytes_sent
            total_performance.network_bytes_received += perf.network_bytes_received
            
            # 累加防护数据
            defense = report.defense_analysis
            total_defense.captcha_triggered += defense.captcha_triggered
            total_defense.captcha_solved += defense.captcha_solved
            total_defense.captcha_failed += defense.captcha_failed
            total_defense.ip_blocks_detected += defense.ip_blocks_detected
            total_defense.rate_limits_hit += defense.rate_limits_hit
            total_defense.user_agent_blocks += defense.user_agent_blocks
            total_defense.proxy_failures += defense.proxy_failures
            total_defense.anti_detection_bypassed += defense.anti_detection_bypassed
            total_defense.anti_detection_blocked += defense.anti_detection_blocked
        
        # 计算汇总响应时间统计
        if response_times_all:
            total_stats.avg_response_time = statistics.mean(response_times_all)
            total_stats.min_response_time = min(response_times_all)
            total_stats.max_response_time = max(response_times_all)
            total_stats.median_response_time = statistics.median(response_times_all)
        
        # 计算总持续时间
        total_stats.total_duration = sum(durations)
        
        # 计算平均性能指标
        if scenario_count > 0:
            total_performance.avg_concurrent_requests = sum(r.performance.avg_concurrent_requests for r in self.scenario_reports) / scenario_count
            total_performance.cpu_usage_avg = sum(r.performance.cpu_usage_avg for r in self.scenario_reports) / scenario_count
            total_performance.memory_usage_avg = sum(r.performance.memory_usage_avg for r in self.scenario_reports) / scenario_count
        
        # 计算比率
        total_stats.calculate_rates()
        total_defense.calculate_rates()
        
        return {
            'overview': {
                'total_scenarios': scenario_count,
                'successful_scenarios': successful_scenarios,
                'failed_scenarios': failed_scenarios,
                'scenario_success_rate': successful_scenarios / scenario_count if scenario_count > 0 else 0,
                'total_duration': total_stats.total_duration,
                'avg_scenario_duration': statistics.mean(durations) if durations else 0
            },
            'statistics': asdict(total_stats),
            'performance': asdict(total_performance),
            'defense_analysis': asdict(total_defense),
            'trends': self._calculate_trends()
        }
    
    def _calculate_trends(self) -> Dict[str, Any]:
        """计算趋势分析"""
        if len(self.scenario_reports) < 2:
            return {}
        
        # 按时间排序
        sorted_reports = sorted(self.scenario_reports, key=lambda r: r.start_time)
        
        # 计算成功率趋势
        success_rates = []
        response_times = []
        block_rates = []
        
        for report in sorted_reports:
            success_rates.append(report.statistics.success_rate)
            response_times.append(report.statistics.avg_response_time)
            block_rates.append(report.statistics.block_rate)
        
        return {
            'success_rate_trend': self._calculate_trend(success_rates),
            'response_time_trend': self._calculate_trend(response_times),
            'block_rate_trend': self._calculate_trend(block_rates),
            'improvement_suggestions': self._generate_suggestions()
        }
    
    def _calculate_trend(self, values: List[float]) -> Dict[str, Any]:
        """计算趋势"""
        if len(values) < 2:
            return {'direction': 'stable', 'change': 0.0}
        
        # 简单线性趋势计算
        first_half = values[:len(values)//2]
        second_half = values[len(values)//2:]
        
        avg_first = statistics.mean(first_half)
        avg_second = statistics.mean(second_half)
        
        change = avg_second - avg_first
        change_percent = (change / avg_first * 100) if avg_first != 0 else 0
        
        if abs(change_percent) < 5:
            direction = 'stable'
        elif change_percent > 0:
            direction = 'increasing'
        else:
            direction = 'decreasing'
        
        return {
            'direction': direction,
            'change': change,
            'change_percent': change_percent
        }
    
    def _generate_suggestions(self) -> List[str]:
        """生成改进建议"""
        suggestions = []
        
        if not self.scenario_reports:
            return suggestions
        
        # 分析整体成功率
        total_success_rate = sum(r.statistics.success_rate for r in self.scenario_reports) / len(self.scenario_reports)
        
        if total_success_rate < 0.5:
            suggestions.append("整体成功率较低，建议检查目标系统的防护策略")
        
        # 分析响应时间
        avg_response_time = sum(r.statistics.avg_response_time for r in self.scenario_reports) / len(self.scenario_reports)
        
        if avg_response_time > 5.0:
            suggestions.append("平均响应时间较长，可能存在性能瓶颈或防护延迟")
        
        # 分析封禁情况
        total_blocks = sum(r.statistics.blocked_requests for r in self.scenario_reports)
        total_requests = sum(r.statistics.total_requests for r in self.scenario_reports)
        
        if total_requests > 0 and total_blocks / total_requests > 0.3:
            suggestions.append("封禁率较高，建议优化反检测策略")
        
        # 分析验证码情况
        total_captcha = sum(r.defense_analysis.captcha_triggered for r in self.scenario_reports)
        if total_captcha > 0:
            captcha_success = sum(r.defense_analysis.captcha_solved for r in self.scenario_reports)
            if captcha_success / total_captcha < 0.7:
                suggestions.append("验证码识别成功率较低，建议改进验证码处理模块")
        
        return suggestions
    
    def generate_json_report(self, filename: str = None) -> str:
        """生成JSON格式报告"""
        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"test_report_{timestamp}.json"
        
        filepath = self.output_dir / filename
        
        # 构建报告数据
        report_data = {
            'metadata': {
                'generated_at': datetime.now().isoformat(),
                'generator_version': '1.0.0',
                'report_type': 'comprehensive_test_report'
            },
            'summary': self.generate_summary_statistics(),
            'scenarios': [
                {
                    'basic_info': {
                        'scenario_id': report.scenario_id,
                        'name': report.scenario_name,
                        'attack_type': report.attack_type,
                        'start_time': report.start_time.isoformat(),
                        'end_time': report.end_time.isoformat(),
                        'duration': report.duration,
                        'status': report.status,
                        'result': report.result
                    },
                    'statistics': asdict(report.statistics),
                    'performance': asdict(report.performance),
                    'defense_analysis': asdict(report.defense_analysis),
                    'issues': {
                        'errors': report.error_messages,
                        'warnings': report.warnings
                    }
                }
                for report in self.scenario_reports
            ],
            'events': self.raw_events[-100:],  # 最近100个事件
            'system_metrics': self.system_metrics[-50:]  # 最近50个系统指标
        }
        
        # 写入文件
        try:
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(report_data, f, indent=2, ensure_ascii=False)
            
            self.logger.info(f"JSON报告生成成功: {filepath}")
            return str(filepath)
            
        except Exception as e:
            self.logger.error(f"JSON报告生成失败: {e}")
            raise
    
    def generate_html_report(self, filename: str = None) -> str:
        """生成HTML格式报告"""
        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"test_report_{timestamp}.html"
        
        filepath = self.output_dir / filename
        
        # 生成HTML内容
        html_content = self._generate_html_content()
        
        try:
            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(html_content)
            
            self.logger.info(f"HTML报告生成成功: {filepath}")
            return str(filepath)
            
        except Exception as e:
            self.logger.error(f"HTML报告生成失败: {e}")
            raise
    
    def _generate_html_content(self) -> str:
        """生成HTML报告内容"""
        summary = self.generate_summary_statistics()
        
        # 基本HTML模板
        html_template = """
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>抢票测试报告</title>
    <style>
        body {{ font-family: Arial, sans-serif; margin: 20px; background-color: #f5f5f5; }}
        .container {{ max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }}
        .header {{ text-align: center; border-bottom: 2px solid #007bff; padding-bottom: 20px; margin-bottom: 30px; }}
        .section {{ margin-bottom: 30px; }}
        .section h2 {{ color: #007bff; border-left: 4px solid #007bff; padding-left: 10px; }}
        .metrics-grid {{ display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; }}
        .metric-card {{ background: #f8f9fa; padding: 15px; border-radius: 6px; border-left: 4px solid #28a745; }}
        .metric-value {{ font-size: 24px; font-weight: bold; color: #007bff; }}
        .metric-label {{ color: #666; font-size: 14px; }}
        .scenario-table {{ width: 100%; border-collapse: collapse; margin-top: 15px; }}
        .scenario-table th, .scenario-table td {{ padding: 10px; text-align: left; border-bottom: 1px solid #ddd; }}
        .scenario-table th {{ background-color: #007bff; color: white; }}
        .status-success {{ color: #28a745; font-weight: bold; }}
        .status-failed {{ color: #dc3545; font-weight: bold; }}
        .status-partial {{ color: #ffc107; font-weight: bold; }}
        .suggestions {{ background: #e7f3ff; padding: 15px; border-radius: 6px; border-left: 4px solid #007bff; }}
        .suggestions ul {{ margin: 10px 0; padding-left: 20px; }}
        .chart-placeholder {{ background: #f8f9fa; padding: 40px; text-align: center; color: #666; border: 2px dashed #ddd; border-radius: 6px; }}
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>抢票系统测试报告</h1>
            <p>生成时间: {generated_time}</p>
        </div>
        
        <div class="section">
            <h2>测试概览</h2>
            <div class="metrics-grid">
                <div class="metric-card">
                    <div class="metric-value">{total_scenarios}</div>
                    <div class="metric-label">总场景数</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value">{success_rate:.1%}</div>
                    <div class="metric-label">场景成功率</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value">{total_requests:,}</div>
                    <div class="metric-label">总请求数</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value">{overall_success_rate:.1%}</div>
                    <div class="metric-label">请求成功率</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value">{avg_response_time:.2f}s</div>
                    <div class="metric-label">平均响应时间</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value">{total_duration:.1f}s</div>
                    <div class="metric-label">总测试时长</div>
                </div>
            </div>
        </div>
        
        <div class="section">
            <h2>场景执行结果</h2>
            <table class="scenario-table">
                <thead>
                    <tr>
                        <th>场景名称</th>
                        <th>攻击类型</th>
                        <th>状态</th>
                        <th>请求数</th>
                        <th>成功率</th>
                        <th>响应时间</th>
                        <th>持续时间</th>
                    </tr>
                </thead>
                <tbody>
                    {scenario_rows}
                </tbody>
            </table>
        </div>
        
        <div class="section">
            <h2>防护效果分析</h2>
            <div class="metrics-grid">
                <div class="metric-card">
                    <div class="metric-value">{captcha_triggered}</div>
                    <div class="metric-label">验证码触发次数</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value">{captcha_success_rate:.1%}</div>
                    <div class="metric-label">验证码识别成功率</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value">{ip_blocks}</div>
                    <div class="metric-label">IP封禁检测</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value">{rate_limits}</div>
                    <div class="metric-label">频率限制触发</div>
                </div>
            </div>
        </div>
        
        <div class="section">
            <h2>性能分析</h2>
            <div class="chart-placeholder">
                <p>性能图表占位符</p>
                <p>可集成 Chart.js 或其他图表库显示性能趋势</p>
            </div>
        </div>
        
        <div class="section">
            <h2>改进建议</h2>
            <div class="suggestions">
                <h3>系统优化建议</h3>
                <ul>
                    {suggestions_html}
                </ul>
            </div>
        </div>
    </div>
</body>
</html>
        """
        
        # 准备数据
        overview = summary.get('overview', {})
        stats = summary.get('statistics', {})
        defense = summary.get('defense_analysis', {})
        trends = summary.get('trends', {})
        
        # 生成场景行
        scenario_rows = ""
        for report in self.scenario_reports:
            status_class = f"status-{report.result}" if report.result in ['success', 'failed', 'partial'] else ""
            scenario_rows += f"""
                <tr>
                    <td>{report.scenario_name}</td>
                    <td>{report.attack_type}</td>
                    <td class="{status_class}">{report.result}</td>
                    <td>{report.statistics.total_requests}</td>
                    <td>{report.statistics.success_rate:.1%}</td>
                    <td>{report.statistics.avg_response_time:.2f}s</td>
                    <td>{report.duration:.1f}s</td>
                </tr>
            """
        
        # 生成建议HTML
        suggestions = trends.get('improvement_suggestions', [])
        suggestions_html = ""
        for suggestion in suggestions:
            suggestions_html += f"<li>{suggestion}</li>"
        
        if not suggestions_html:
            suggestions_html = "<li>暂无特殊建议，系统运行正常</li>"
        
        # 填充模板
        return html_template.format(
            generated_time=datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            total_scenarios=overview.get('total_scenarios', 0),
            success_rate=overview.get('scenario_success_rate', 0),
            total_requests=stats.get('total_requests', 0),
            overall_success_rate=stats.get('success_rate', 0),
            avg_response_time=stats.get('avg_response_time', 0),
            total_duration=overview.get('total_duration', 0),
            scenario_rows=scenario_rows,
            captcha_triggered=defense.get('captcha_triggered', 0),
            captcha_success_rate=defense.get('captcha_success_rate', 0),
            ip_blocks=defense.get('ip_blocks_detected', 0),
            rate_limits=defense.get('rate_limits_hit', 0),
            suggestions_html=suggestions_html
        )
    
    def generate_all_reports(self, base_filename: str = None) -> Dict[str, str]:
        """生成所有格式的报告"""
        if base_filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            base_filename = f"test_report_{timestamp}"
        
        reports = {}
        
        try:
            # 生成JSON报告
            json_file = self.generate_json_report(f"{base_filename}.json")
            reports['json'] = json_file
            
            # 生成HTML报告
            html_file = self.generate_html_report(f"{base_filename}.html")
            reports['html'] = html_file
            
            self.logger.info(f"所有报告生成完成: {reports}")
            return reports
            
        except Exception as e:
            self.logger.error(f"报告生成失败: {e}")
            raise
    
    def export_raw_data(self, filename: str = None) -> str:
        """导出原始数据"""
        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"raw_data_{timestamp}.json"
        
        filepath = self.output_dir / filename
        
        raw_data = {
            'scenarios': [report.raw_data for report in self.scenario_reports],
            'events': self.raw_events,
            'system_metrics': self.system_metrics
        }
        
        try:
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(raw_data, f, indent=2, ensure_ascii=False)
            
            self.logger.info(f"原始数据导出成功: {filepath}")
            return str(filepath)
            
        except Exception as e:
            self.logger.error(f"原始数据导出失败: {e}")
            raise
    
    def clear_data(self):
        """清空数据"""
        self.scenario_reports.clear()
        self.raw_events.clear()
        self.system_metrics.clear()
        self.logger.info("数据已清空")


# 便捷函数
def create_report_generator(output_dir: str = "reports") -> ReportGenerator:
    """创建报告生成器"""
    return ReportGenerator(output_dir)


def generate_quick_report(scenario_data_list: List[Dict[str, Any]], output_dir: str = "reports") -> Dict[str, str]:
    """快速生成报告"""
    generator = ReportGenerator(output_dir)
    
    for scenario_data in scenario_data_list:
        generator.add_scenario_data(scenario_data)
    
    return generator.generate_all_reports()
#!/usr/bin/env python3
"""
简化的核心系统测试

测试基本的Python导入和配置文件加载。
"""

import sys
import json
from pathlib import Path

# 添加项目路径
sys.path.insert(0, str(Path(__file__).parent))

def test_project_structure():
    """测试项目结构"""
    print("=== 测试项目结构 ===")
    
    # 检查主要目录
    directories = ['core', 'modules', 'scenarios', 'utils', 'config', 'data']
    for directory in directories:
        path = Path(directory)
        exists = path.exists() and path.is_dir()
        print(f"目录 {directory}: {'存在' if exists else '不存在'}")
    
    # 检查配置文件
    config_file = Path('config/default_config.json')
    if config_file.exists():
        print("配置文件存在")
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
            print(f"配置文件加载成功，包含 {len(config)} 个主要配置项")
            print(f"系统名称: {config.get('system', {}).get('name', 'Unknown')}")
        except Exception as e:
            print(f"配置文件加载失败: {e}")
    else:
        print("配置文件不存在")
    
    print("项目结构测试完成\n")


def test_package_imports():
    """测试包导入"""
    print("=== 测试包导入 ===")
    
    try:
        # 测试主包导入
        import ticket_grabber_test
        print(f"主包导入成功，版本: {ticket_grabber_test.__version__}")
        
        # 测试子包导入
        from ticket_grabber_test.config import settings
        print("配置模块导入成功")
        
        from ticket_grabber_test.core import logger
        print("日志模块导入成功")
        
        from ticket_grabber_test.core import engine
        print("引擎模块导入成功")
        
    except ImportError as e:
        print(f"包导入失败: {e}")
    except Exception as e:
        print(f"其他错误: {e}")
    
    print("包导入测试完成\n")


def test_basic_functionality():
    """测试基本功能"""
    print("=== 测试基本功能 ===")
    
    # 测试日志目录创建
    logs_dir = Path('logs')
    if not logs_dir.exists():
        logs_dir.mkdir(parents=True, exist_ok=True)
        print("创建日志目录")
    else:
        print("日志目录已存在")
    
    # 测试基本的文件操作
    test_file = Path('logs/test.log')
    try:
        with open(test_file, 'w', encoding='utf-8') as f:
            f.write("测试日志文件\n")
        print("测试文件写入成功")
        
        with open(test_file, 'r', encoding='utf-8') as f:
            content = f.read()
        print(f"测试文件读取成功: {content.strip()}")
        
        # 清理测试文件
        test_file.unlink()
        print("测试文件清理完成")
        
    except Exception as e:
        print(f"文件操作失败: {e}")
    
    print("基本功能测试完成\n")


def main():
    """主测试函数"""
    print("开始简化核心系统测试...\n")
    
    try:
        # 测试项目结构
        test_project_structure()
        
        # 测试包导入
        test_package_imports()
        
        # 测试基本功能
        test_basic_functionality()
        
        print("=== 所有测试完成 ===")
        print("核心系统基础功能正常！")
        
    except Exception as e:
        print(f"测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
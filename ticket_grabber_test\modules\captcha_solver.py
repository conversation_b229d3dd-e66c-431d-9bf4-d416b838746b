"""
验证码识别模块

基于研究报告中的CaptchaRecognizer类实现，提供OCR识别、深度学习模型和第三方识别服务的验证码处理功能。
"""

import os
import time
import hashlib
import json
import base64
import requests
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
from enum import Enum
import threading

# 图像处理库
try:
    import cv2
    import numpy as np
    from PIL import Image, ImageEnhance, ImageFilter
    CV2_AVAILABLE = True
except ImportError:
    CV2_AVAILABLE = False
    print("警告: OpenCV不可用，部分图像处理功能将被禁用")

# OCR库
try:
    import pytesseract
    TESSERACT_AVAILABLE = True
except ImportError:
    TESSERACT_AVAILABLE = False
    print("警告: pytesseract不可用，OCR功能将被禁用")

try:
    from ..core.logger import get_logger
    from ..config.settings import config_manager
except ImportError:
    import logging
    def get_logger(name):
        return logging.getLogger(name)
    
    class SimpleConfig:
        def get(self, key, default=None):
            config_data = {
                'captcha.enable_ocr': True,
                'captcha.ocr_engine': 'tesseract',
                'captcha.preprocessing.resize': True,
                'captcha.preprocessing.denoise': True,
                'captcha.preprocessing.threshold': True,
                'captcha.third_party_service.enable': False,
                'captcha.third_party_service.api_key': '',
                'captcha.third_party_service.service_url': '',
                'captcha.cache_results': True,
                'captcha.cache_duration': 300
            }
            return config_data.get(key, default)
    
    config_manager = SimpleConfig()


class CaptchaType(Enum):
    """验证码类型枚举"""
    TEXT = "text"           # 文字验证码
    MATH = "math"           # 数学计算验证码
    IMAGE = "image"         # 图像识别验证码
    SLIDER = "slider"       # 滑块验证码
    CLICK = "click"         # 点击验证码
    ROTATE = "rotate"       # 旋转验证码
    UNKNOWN = "unknown"     # 未知类型


@dataclass
class CaptchaResult:
    """验证码识别结果"""
    success: bool
    result: str
    confidence: float
    captcha_type: CaptchaType
    processing_time: float
    method: str
    error_message: Optional[str] = None


@dataclass
class CaptchaStats:
    """验证码识别统计"""
    total_attempts: int = 0
    successful_attempts: int = 0
    failed_attempts: int = 0
    average_processing_time: float = 0.0
    success_rate: float = 0.0
    method_stats: Dict[str, int] = None
    
    def __post_init__(self):
        if self.method_stats is None:
            self.method_stats = {}


class CaptchaRecognizer:
    """
    验证码识别器
    基于研究报告中的CaptchaRecognizer类实现
    """
    
    def __init__(self):
        """初始化验证码识别器"""
        self.logger = get_logger(__name__)
        
        # OCR配置
        self.ocr_config = '--oem 3 --psm 6 -c tessedit_char_whitelist=0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ'
        self.math_ocr_config = '--oem 3 --psm 6 -c tessedit_char_whitelist=0123456789+-×÷='
        
        # 配置参数
        self.enable_ocr = config_manager.get('captcha.enable_ocr', True)
        self.ocr_engine = config_manager.get('captcha.ocr_engine', 'tesseract')
        self.enable_preprocessing = {
            'resize': config_manager.get('captcha.preprocessing.resize', True),
            'denoise': config_manager.get('captcha.preprocessing.denoise', True),
            'threshold': config_manager.get('captcha.preprocessing.threshold', True)
        }
        
        # 第三方服务配置
        self.third_party_enabled = config_manager.get('captcha.third_party_service.enable', False)
        self.third_party_api_key = config_manager.get('captcha.third_party_service.api_key', '')
        self.third_party_url = config_manager.get('captcha.third_party_service.service_url', '')
        
        # 缓存配置
        self.cache_enabled = config_manager.get('captcha.cache_results', True)
        self.cache_duration = config_manager.get('captcha.cache_duration', 300)
        self.cache = {}
        self.cache_lock = threading.RLock()
        
        # 统计信息
        self.stats = CaptchaStats()
        self.stats_lock = threading.RLock()
        
        # 支持的图像格式
        self.supported_formats = ['.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.gif']
        
        self.logger.info("验证码识别器初始化完成")
        self.logger.info(f"OCR可用: {TESSERACT_AVAILABLE}, 图像处理可用: {CV2_AVAILABLE}")
    
    def detect_captcha_type(self, image_path: str) -> CaptchaType:
        """
        检测验证码类型
        
        Args:
            image_path: 图像文件路径
            
        Returns:
            CaptchaType: 验证码类型
        """
        try:
            if not CV2_AVAILABLE:
                return CaptchaType.TEXT
            
            # 读取图像
            img = cv2.imread(image_path)
            if img is None:
                return CaptchaType.UNKNOWN
            
            height, width = img.shape[:2]
            
            # 基于图像尺寸和特征进行简单分类
            if width > height * 2:
                # 宽长比大于2:1，可能是滑块验证码
                return CaptchaType.SLIDER
            elif width < 100 and height < 50:
                # 小尺寸图像，可能是文字验证码
                return CaptchaType.TEXT
            elif width > 200 and height > 200:
                # 大尺寸图像，可能是图像识别验证码
                return CaptchaType.IMAGE
            else:
                # 默认为文字验证码
                return CaptchaType.TEXT
                
        except Exception as e:
            self.logger.error(f"检测验证码类型失败: {e}")
            return CaptchaType.UNKNOWN
    
    def preprocess_image(self, image_path: str, captcha_type: CaptchaType = CaptchaType.TEXT) -> Optional[Any]:
        """
        图像预处理
        
        Args:
            image_path: 图像文件路径
            captcha_type: 验证码类型
            
        Returns:
            处理后的图像数组，失败返回None
        """
        try:
            if not CV2_AVAILABLE:
                # 如果OpenCV不可用，尝试使用PIL
                return self._preprocess_with_pil(image_path)
            
            # 使用OpenCV进行预处理
            img = cv2.imread(image_path)
            if img is None:
                self.logger.error(f"无法读取图像: {image_path}")
                return None
            
            # 转换为灰度图
            gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
            
            # 根据验证码类型选择不同的预处理策略
            if captcha_type == CaptchaType.TEXT:
                processed = self._preprocess_text_captcha(gray)
            elif captcha_type == CaptchaType.MATH:
                processed = self._preprocess_math_captcha(gray)
            else:
                processed = self._preprocess_general_captcha(gray)
            
            return processed
            
        except Exception as e:
            self.logger.error(f"图像预处理失败: {e}")
            return None
    
    def _preprocess_with_pil(self, image_path: str) -> Optional[Any]:
        """使用PIL进行图像预处理"""
        try:
            # 打开图像
            img = Image.open(image_path)
            
            # 转换为灰度图
            if img.mode != 'L':
                img = img.convert('L')
            
            # 增强对比度
            enhancer = ImageEnhance.Contrast(img)
            img = enhancer.enhance(2.0)
            
            # 应用滤镜
            img = img.filter(ImageFilter.MedianFilter(size=3))
            
            # 转换为numpy数组
            img_array = np.array(img)
            
            # 二值化
            _, binary = cv2.threshold(img_array, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU) if CV2_AVAILABLE else (None, img_array)
            
            return binary if binary is not None else img_array
            
        except Exception as e:
            self.logger.error(f"PIL图像预处理失败: {e}")
            return None
    
    def _preprocess_text_captcha(self, gray_img: Any) -> Any:
        """预处理文字验证码"""
        # 降噪
        if self.enable_preprocessing['denoise']:
            denoised = cv2.medianBlur(gray_img, 3)
        else:
            denoised = gray_img
        
        # 二值化
        if self.enable_preprocessing['threshold']:
            _, binary = cv2.threshold(denoised, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
        else:
            binary = denoised
        
        # 形态学操作去除噪点
        kernel = np.ones((2, 2), np.uint8)
        cleaned = cv2.morphologyEx(binary, cv2.MORPH_CLOSE, kernel)
        
        return cleaned
    
    def _preprocess_math_captcha(self, gray_img: Any) -> Any:
        """预处理数学验证码"""
        # 增强对比度
        enhanced = cv2.equalizeHist(gray_img)
        
        # 降噪
        denoised = cv2.bilateralFilter(enhanced, 9, 75, 75)
        
        # 二值化
        _, binary = cv2.threshold(denoised, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
        
        return binary
    
    def _preprocess_general_captcha(self, gray_img: Any) -> Any:
        """预处理通用验证码"""
        # 自适应直方图均衡化
        clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8, 8))
        enhanced = clahe.apply(gray_img)
        
        # 高斯滤波降噪
        denoised = cv2.GaussianBlur(enhanced, (3, 3), 0)
        
        # 自适应二值化
        binary = cv2.adaptiveThreshold(denoised, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, cv2.THRESH_BINARY, 11, 2)
        
        return binary
    
    def recognize_with_ocr(self, image_path: str, captcha_type: CaptchaType = CaptchaType.TEXT) -> CaptchaResult:
        """
        使用OCR识别验证码
        
        Args:
            image_path: 图像文件路径
            captcha_type: 验证码类型
            
        Returns:
            CaptchaResult: 识别结果
        """
        start_time = time.time()
        
        if not TESSERACT_AVAILABLE:
            return CaptchaResult(
                success=False,
                result="",
                confidence=0.0,
                captcha_type=captcha_type,
                processing_time=time.time() - start_time,
                method="ocr",
                error_message="Tesseract不可用"
            )
        
        try:
            # 预处理图像
            processed_img = self.preprocess_image(image_path, captcha_type)
            if processed_img is None:
                return CaptchaResult(
                    success=False,
                    result="",
                    confidence=0.0,
                    captcha_type=captcha_type,
                    processing_time=time.time() - start_time,
                    method="ocr",
                    error_message="图像预处理失败"
                )
            
            # 选择OCR配置
            if captcha_type == CaptchaType.MATH:
                config = self.math_ocr_config
            else:
                config = self.ocr_config
            
            # 执行OCR识别
            if CV2_AVAILABLE and isinstance(processed_img, np.ndarray):
                # 使用OpenCV处理的图像
                result = pytesseract.image_to_string(processed_img, config=config)
            else:
                # 直接使用原始图像
                result = pytesseract.image_to_string(image_path, config=config)
            
            # 清理结果
            cleaned_result = self._clean_ocr_result(result, captcha_type)
            
            # 计算置信度（简单估算）
            confidence = self._estimate_confidence(cleaned_result, captcha_type)
            
            processing_time = time.time() - start_time
            
            return CaptchaResult(
                success=bool(cleaned_result),
                result=cleaned_result,
                confidence=confidence,
                captcha_type=captcha_type,
                processing_time=processing_time,
                method="ocr"
            )
            
        except Exception as e:
            self.logger.error(f"OCR识别失败: {e}")
            return CaptchaResult(
                success=False,
                result="",
                confidence=0.0,
                captcha_type=captcha_type,
                processing_time=time.time() - start_time,
                method="ocr",
                error_message=str(e)
            )
    
    def _clean_ocr_result(self, result: str, captcha_type: CaptchaType) -> str:
        """清理OCR识别结果"""
        if not result:
            return ""
        
        # 移除空白字符
        cleaned = result.strip()
        
        # 根据验证码类型进行特定清理
        if captcha_type == CaptchaType.TEXT:
            # 移除非字母数字字符
            cleaned = ''.join(c for c in cleaned if c.isalnum())
        elif captcha_type == CaptchaType.MATH:
            # 保留数字和运算符
            allowed_chars = '0123456789+-×÷=*/'
            cleaned = ''.join(c for c in cleaned if c in allowed_chars)
            # 标准化运算符
            cleaned = cleaned.replace('×', '*').replace('÷', '/')
        
        return cleaned
    
    def _estimate_confidence(self, result: str, captcha_type: CaptchaType) -> float:
        """估算识别置信度"""
        if not result:
            return 0.0
        
        # 基于结果长度和类型的简单置信度估算
        if captcha_type == CaptchaType.TEXT:
            # 文字验证码通常4-6位
            if 4 <= len(result) <= 6:
                return 0.8
            elif 3 <= len(result) <= 7:
                return 0.6
            else:
                return 0.4
        elif captcha_type == CaptchaType.MATH:
            # 数学验证码应包含运算符
            if any(op in result for op in ['+', '-', '*', '/']):
                return 0.7
            else:
                return 0.3
        
        return 0.5
    
    def recognize_with_third_party(self, image_path: str) -> CaptchaResult:
        """
        使用第三方服务识别验证码
        
        Args:
            image_path: 图像文件路径
            
        Returns:
            CaptchaResult: 识别结果
        """
        start_time = time.time()
        
        if not self.third_party_enabled or not self.third_party_api_key:
            return CaptchaResult(
                success=False,
                result="",
                confidence=0.0,
                captcha_type=CaptchaType.UNKNOWN,
                processing_time=time.time() - start_time,
                method="third_party",
                error_message="第三方服务未配置"
            )
        
        try:
            # 读取图像并编码为base64
            with open(image_path, 'rb') as f:
                image_data = base64.b64encode(f.read()).decode('utf-8')
            
            # 构建请求数据
            request_data = {
                'api_key': self.third_party_api_key,
                'image': image_data,
                'method': 'base64'
            }
            
            # 发送请求
            response = requests.post(
                self.third_party_url,
                json=request_data,
                timeout=30
            )
            
            if response.status_code == 200:
                result_data = response.json()
                
                if result_data.get('success'):
                    processing_time = time.time() - start_time
                    
                    return CaptchaResult(
                        success=True,
                        result=result_data.get('result', ''),
                        confidence=result_data.get('confidence', 0.9),
                        captcha_type=CaptchaType.UNKNOWN,
                        processing_time=processing_time,
                        method="third_party"
                    )
                else:
                    return CaptchaResult(
                        success=False,
                        result="",
                        confidence=0.0,
                        captcha_type=CaptchaType.UNKNOWN,
                        processing_time=time.time() - start_time,
                        method="third_party",
                        error_message=result_data.get('error', '第三方服务返回错误')
                    )
            else:
                return CaptchaResult(
                    success=False,
                    result="",
                    confidence=0.0,
                    captcha_type=CaptchaType.UNKNOWN,
                    processing_time=time.time() - start_time,
                    method="third_party",
                    error_message=f"HTTP错误: {response.status_code}"
                )
                
        except Exception as e:
            self.logger.error(f"第三方服务识别失败: {e}")
            return CaptchaResult(
                success=False,
                result="",
                confidence=0.0,
                captcha_type=CaptchaType.UNKNOWN,
                processing_time=time.time() - start_time,
                method="third_party",
                error_message=str(e)
            )
    
    def solve_math_captcha(self, expression: str) -> str:
        """
        解决数学验证码
        
        Args:
            expression: 数学表达式
            
        Returns:
            str: 计算结果
        """
        try:
            # 安全的数学表达式求值
            # 只允许基本的数学运算
            allowed_chars = set('0123456789+-*/(). ')
            if not all(c in allowed_chars for c in expression):
                return ""
            
            # 替换可能的中文运算符
            expression = expression.replace('×', '*').replace('÷', '/')
            
            # 使用eval计算（在受控环境中）
            result = eval(expression)
            return str(int(result)) if isinstance(result, (int, float)) else ""
            
        except Exception as e:
            self.logger.error(f"数学验证码计算失败: {e}")
            return ""
    
    def get_cache_key(self, image_path: str) -> str:
        """生成缓存键"""
        try:
            with open(image_path, 'rb') as f:
                image_data = f.read()
            return hashlib.md5(image_data).hexdigest()
        except:
            return hashlib.md5(image_path.encode()).hexdigest()
    
    def get_cached_result(self, cache_key: str) -> Optional[CaptchaResult]:
        """获取缓存的识别结果"""
        if not self.cache_enabled:
            return None
        
        with self.cache_lock:
            if cache_key in self.cache:
                cached_data, timestamp = self.cache[cache_key]
                if time.time() - timestamp < self.cache_duration:
                    return cached_data
                else:
                    # 缓存过期，删除
                    del self.cache[cache_key]
        
        return None
    
    def cache_result(self, cache_key: str, result: CaptchaResult):
        """缓存识别结果"""
        if not self.cache_enabled:
            return
        
        with self.cache_lock:
            self.cache[cache_key] = (result, time.time())
    
    def recognize_captcha(self, image_path: str, methods: List[str] = None) -> CaptchaResult:
        """
        识别验证码（主要接口）
        
        Args:
            image_path: 图像文件路径
            methods: 识别方法列表，默认为['ocr', 'third_party']
            
        Returns:
            CaptchaResult: 识别结果
        """
        if methods is None:
            methods = ['ocr']
            if self.third_party_enabled:
                methods.append('third_party')
        
        # 检查文件是否存在
        if not os.path.exists(image_path):
            return CaptchaResult(
                success=False,
                result="",
                confidence=0.0,
                captcha_type=CaptchaType.UNKNOWN,
                processing_time=0.0,
                method="none",
                error_message="图像文件不存在"
            )
        
        # 检查缓存
        cache_key = self.get_cache_key(image_path)
        cached_result = self.get_cached_result(cache_key)
        if cached_result:
            self.logger.debug(f"使用缓存结果: {cached_result.result}")
            return cached_result
        
        # 检测验证码类型
        captcha_type = self.detect_captcha_type(image_path)
        
        # 尝试不同的识别方法
        best_result = None
        
        for method in methods:
            try:
                if method == 'ocr':
                    result = self.recognize_with_ocr(image_path, captcha_type)
                elif method == 'third_party':
                    result = self.recognize_with_third_party(image_path)
                else:
                    continue
                
                # 如果是数学验证码且识别成功，尝试计算结果
                if (result.success and captcha_type == CaptchaType.MATH and 
                    any(op in result.result for op in ['+', '-', '*', '/'])):
                    math_result = self.solve_math_captcha(result.result)
                    if math_result:
                        result.result = math_result
                        result.confidence = min(result.confidence + 0.1, 1.0)
                
                # 选择最佳结果
                if result.success and (best_result is None or result.confidence > best_result.confidence):
                    best_result = result
                
                # 如果置信度足够高，直接返回
                if result.success and result.confidence > 0.8:
                    break
                    
            except Exception as e:
                self.logger.error(f"识别方法 {method} 失败: {e}")
                continue
        
        # 如果没有成功的结果，返回最后一个尝试的结果
        if best_result is None:
            best_result = CaptchaResult(
                success=False,
                result="",
                confidence=0.0,
                captcha_type=captcha_type,
                processing_time=0.0,
                method="none",
                error_message="所有识别方法都失败"
            )
        
        # 缓存结果
        if best_result.success:
            self.cache_result(cache_key, best_result)
        
        # 更新统计信息
        self.update_stats(best_result)
        
        return best_result
    
    def update_stats(self, result: CaptchaResult):
        """更新统计信息"""
        with self.stats_lock:
            self.stats.total_attempts += 1
            
            if result.success:
                self.stats.successful_attempts += 1
            else:
                self.stats.failed_attempts += 1
            
            # 更新成功率
            self.stats.success_rate = self.stats.successful_attempts / self.stats.total_attempts
            
            # 更新平均处理时间
            total_time = (self.stats.average_processing_time * (self.stats.total_attempts - 1) + 
                         result.processing_time)
            self.stats.average_processing_time = total_time / self.stats.total_attempts
            
            # 更新方法统计
            if result.method not in self.stats.method_stats:
                self.stats.method_stats[result.method] = 0
            self.stats.method_stats[result.method] += 1
    
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        with self.stats_lock:
            return {
                'total_attempts': self.stats.total_attempts,
                'successful_attempts': self.stats.successful_attempts,
                'failed_attempts': self.stats.failed_attempts,
                'success_rate': self.stats.success_rate,
                'average_processing_time': self.stats.average_processing_time,
                'method_stats': self.stats.method_stats.copy(),
                'cache_size': len(self.cache) if self.cache_enabled else 0
            }
    
    def clear_cache(self):
        """清空缓存"""
        with self.cache_lock:
            self.cache.clear()
        self.logger.info("验证码识别缓存已清空")
    
    def reset_stats(self):
        """重置统计信息"""
        with self.stats_lock:
            self.stats = CaptchaStats()
        self.logger.info("验证码识别统计信息已重置")


# 全局验证码识别器实例
captcha_recognizer: Optional[CaptchaRecognizer] = None


def get_captcha_recognizer() -> CaptchaRecognizer:
    """获取全局验证码识别器实例"""
    global captcha_recognizer
    if captcha_recognizer is None:
        captcha_recognizer = CaptchaRecognizer()
    return captcha_recognizer


def recognize_captcha(image_path: str, methods: List[str] = None) -> CaptchaResult:
    """
    便捷的验证码识别函数
    
    Args:
        image_path: 图像文件路径
        methods: 识别方法列表
        
    Returns:
        CaptchaResult: 识别结果
    """
    recognizer = get_captcha_recognizer()
    return recognizer.recognize_captcha(image_path, methods)
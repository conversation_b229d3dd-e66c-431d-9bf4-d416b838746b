"""
行为模拟模块

基于研究报告中的HumanBehaviorSimulator类实现，提供人类操作行为模拟，
包括鼠标移动轨迹、键盘输入节奏、页面停留时间等功能。
"""

import time
import random
import math
import json
import threading
from typing import Dict, List, Tuple, Optional, Any, Callable
from dataclasses import dataclass, field
from enum import Enum
import numpy as np

# Selenium相关导入
try:
    from selenium import webdriver
    from selenium.webdriver.common.action_chains import ActionChains
    from selenium.webdriver.common.keys import Keys
    from selenium.webdriver.common.by import By
    from selenium.webdriver.support.ui import WebDriverWait
    from selenium.webdriver.support import expected_conditions as EC
    from selenium.common.exceptions import WebDriverException, TimeoutException
    SELENIUM_AVAILABLE = True
except ImportError:
    SELENIUM_AVAILABLE = False
    print("警告: Selenium不可用，浏览器自动化功能将被禁用")

try:
    from ..core.logger import get_logger
    from ..config.settings import config_manager
except ImportError:
    import logging
    def get_logger(name):
        return logging.getLogger(name)
    
    class SimpleConfig:
        def get(self, key, default=None):
            config_data = {
                'behavior.mouse_speed_range': (0.5, 2.0),
                'behavior.click_delay_range': (0.1, 0.3),
                'behavior.typing_speed_range': (0.05, 0.2),
                'behavior.page_stay_range': (2.0, 8.0),
                'behavior.scroll_speed_range': (0.1, 0.5),
                'behavior.enable_jitter': True,
                'behavior.jitter_range': (-2, 2),
                'behavior.bezier_steps_range': (10, 20),
                'behavior.learning_enabled': True,
                'behavior.adaptation_threshold': 10
            }
            return config_data.get(key, default)
    
    config_manager = SimpleConfig()


class ActionType(Enum):
    """操作类型枚举"""
    CLICK = "click"
    TYPE = "type"
    SCROLL = "scroll"
    HOVER = "hover"
    DRAG = "drag"
    WAIT = "wait"
    NAVIGATE = "navigate"


class BehaviorPattern(Enum):
    """行为模式枚举"""
    NORMAL = "normal"           # 正常用户
    CAUTIOUS = "cautious"       # 谨慎用户
    AGGRESSIVE = "aggressive"   # 激进用户
    RANDOM = "random"           # 随机模式


@dataclass
class MouseTrajectory:
    """鼠标轨迹数据"""
    points: List[Tuple[float, float]]
    timestamps: List[float]
    total_time: float
    distance: float


@dataclass
class TypingPattern:
    """打字模式数据"""
    text: str
    intervals: List[float]
    total_time: float
    wpm: float  # 每分钟字数


@dataclass
class BehaviorAction:
    """行为动作数据"""
    action_type: ActionType
    target: Optional[str] = None
    parameters: Dict[str, Any] = field(default_factory=dict)
    timestamp: float = field(default_factory=time.time)
    duration: float = 0.0
    success: bool = True
    error_message: Optional[str] = None


@dataclass
class BehaviorSession:
    """行为会话数据"""
    session_id: str
    start_time: float
    end_time: Optional[float] = None
    actions: List[BehaviorAction] = field(default_factory=list)
    pattern: BehaviorPattern = BehaviorPattern.NORMAL
    success_rate: float = 0.0
    total_actions: int = 0


class HumanBehaviorSimulator:
    """
    人类行为模拟器
    基于研究报告中的HumanBehaviorSimulator类实现
    """
    
    def __init__(self, driver=None, pattern: BehaviorPattern = BehaviorPattern.NORMAL):
        """
        初始化行为模拟器
        
        Args:
            driver: Selenium WebDriver实例
            pattern: 行为模式
        """
        self.logger = get_logger(__name__)
        self.driver = driver
        self.pattern = pattern
        
        # 行为参数配置
        self.mouse_speed_range = config_manager.get('behavior.mouse_speed_range', (0.5, 2.0))
        self.click_delay_range = config_manager.get('behavior.click_delay_range', (0.1, 0.3))
        self.typing_speed_range = config_manager.get('behavior.typing_speed_range', (0.05, 0.2))
        self.page_stay_range = config_manager.get('behavior.page_stay_range', (2.0, 8.0))
        self.scroll_speed_range = config_manager.get('behavior.scroll_speed_range', (0.1, 0.5))
        
        # 抖动参数
        self.enable_jitter = config_manager.get('behavior.enable_jitter', True)
        self.jitter_range = config_manager.get('behavior.jitter_range', (-2, 2))
        self.bezier_steps_range = config_manager.get('behavior.bezier_steps_range', (10, 20))
        
        # 学习和适应参数
        self.learning_enabled = config_manager.get('behavior.learning_enabled', True)
        self.adaptation_threshold = config_manager.get('behavior.adaptation_threshold', 10)
        
        # 行为历史和统计
        self.behavior_history: List[BehaviorSession] = []
        self.current_session: Optional[BehaviorSession] = None
        self.learned_patterns: Dict[str, Any] = {}
        
        # 线程安全
        self.lock = threading.RLock()
        
        # 初始化ActionChains
        if self.driver and SELENIUM_AVAILABLE:
            self.actions = ActionChains(self.driver)
        else:
            self.actions = None
        
        # 根据模式调整参数
        self._adjust_parameters_by_pattern()
        
        self.logger.info(f"行为模拟器初始化完成，模式: {pattern.value}")
    
    def _adjust_parameters_by_pattern(self):
        """根据行为模式调整参数"""
        if self.pattern == BehaviorPattern.CAUTIOUS:
            # 谨慎用户：动作较慢，停顿较长
            self.mouse_speed_range = (0.3, 1.0)
            self.click_delay_range = (0.2, 0.5)
            self.typing_speed_range = (0.1, 0.3)
            self.page_stay_range = (3.0, 10.0)
        elif self.pattern == BehaviorPattern.AGGRESSIVE:
            # 激进用户：动作较快，停顿较短
            self.mouse_speed_range = (1.0, 3.0)
            self.click_delay_range = (0.05, 0.15)
            self.typing_speed_range = (0.02, 0.1)
            self.page_stay_range = (1.0, 4.0)
        elif self.pattern == BehaviorPattern.RANDOM:
            # 随机模式：参数范围更大
            self.mouse_speed_range = (0.2, 4.0)
            self.click_delay_range = (0.05, 0.8)
            self.typing_speed_range = (0.01, 0.5)
            self.page_stay_range = (0.5, 15.0)
    
    def start_session(self, session_id: str = None) -> str:
        """开始新的行为会话"""
        if session_id is None:
            session_id = f"session_{int(time.time())}_{random.randint(1000, 9999)}"
        
        with self.lock:
            self.current_session = BehaviorSession(
                session_id=session_id,
                start_time=time.time(),
                pattern=self.pattern
            )
        
        self.logger.info(f"开始行为会话: {session_id}")
        return session_id
    
    def end_session(self) -> Optional[BehaviorSession]:
        """结束当前行为会话"""
        with self.lock:
            if self.current_session:
                self.current_session.end_time = time.time()
                self.current_session.total_actions = len(self.current_session.actions)
                
                # 计算成功率
                if self.current_session.total_actions > 0:
                    successful_actions = sum(1 for action in self.current_session.actions if action.success)
                    self.current_session.success_rate = successful_actions / self.current_session.total_actions
                
                # 保存到历史记录
                self.behavior_history.append(self.current_session)
                
                # 学习和适应
                if self.learning_enabled:
                    self._learn_from_session(self.current_session)
                
                session = self.current_session
                self.current_session = None
                
                self.logger.info(f"结束行为会话: {session.session_id}, 成功率: {session.success_rate:.2%}")
                return session
        
        return None
    
    def _record_action(self, action: BehaviorAction):
        """记录行为动作"""
        with self.lock:
            if self.current_session:
                self.current_session.actions.append(action)
    
    def generate_bezier_curve(self, start: Tuple[float, float], end: Tuple[float, float], 
                            control_points: int = 2) -> List[Tuple[float, float]]:
        """
        生成贝塞尔曲线路径
        
        Args:
            start: 起始点坐标
            end: 结束点坐标
            control_points: 控制点数量
            
        Returns:
            List[Tuple[float, float]]: 路径点列表
        """
        if control_points == 2:
            # 二次贝塞尔曲线
            # 生成随机控制点
            mid_x = (start[0] + end[0]) / 2 + random.uniform(-50, 50)
            mid_y = (start[1] + end[1]) / 2 + random.uniform(-50, 50)
            control = (mid_x, mid_y)
            
            steps = random.randint(*self.bezier_steps_range)
            points = []
            
            for i in range(steps + 1):
                t = i / steps
                # 二次贝塞尔曲线公式
                x = (1-t)**2 * start[0] + 2*(1-t)*t * control[0] + t**2 * end[0]
                y = (1-t)**2 * start[1] + 2*(1-t)*t * control[1] + t**2 * end[1]
                
                # 添加抖动
                if self.enable_jitter:
                    x += random.uniform(*self.jitter_range)
                    y += random.uniform(*self.jitter_range)
                
                points.append((x, y))
            
            return points
        else:
            # 简单线性插值
            steps = random.randint(*self.bezier_steps_range)
            points = []
            
            for i in range(steps + 1):
                t = i / steps
                x = start[0] + (end[0] - start[0]) * t
                y = start[1] + (end[1] - start[1]) * t
                
                if self.enable_jitter:
                    x += random.uniform(*self.jitter_range)
                    y += random.uniform(*self.jitter_range)
                
                points.append((x, y))
            
            return points
    
    def simulate_mouse_movement(self, target_element=None, target_pos: Tuple[float, float] = None) -> MouseTrajectory:
        """
        模拟鼠标移动轨迹
        
        Args:
            target_element: 目标元素
            target_pos: 目标位置坐标
            
        Returns:
            MouseTrajectory: 鼠标轨迹数据
        """
        start_time = time.time()
        
        try:
            if not self.driver or not SELENIUM_AVAILABLE:
                # 模拟轨迹数据
                if target_pos is None:
                    target_pos = (random.randint(100, 800), random.randint(100, 600))
                
                start_pos = (random.randint(50, 200), random.randint(50, 200))
                points = self.generate_bezier_curve(start_pos, target_pos)
                
                # 生成时间戳
                total_time = random.uniform(*self.mouse_speed_range)
                timestamps = [start_time + (total_time * i / len(points)) for i in range(len(points))]
                
                # 计算距离
                distance = math.sqrt((target_pos[0] - start_pos[0])**2 + (target_pos[1] - start_pos[1])**2)
                
                return MouseTrajectory(
                    points=points,
                    timestamps=timestamps,
                    total_time=total_time,
                    distance=distance
                )
            
            # 获取当前鼠标位置
            try:
                current_pos = self.driver.execute_script("return [window.mouseX || 0, window.mouseY || 0];")
                if not current_pos or len(current_pos) != 2:
                    current_pos = [0, 0]
            except:
                current_pos = [0, 0]
            
            # 获取目标位置
            if target_element:
                try:
                    target_location = target_element.location
                    target_size = target_element.size
                    # 点击元素中心
                    target_pos = (
                        target_location['x'] + target_size['width'] / 2,
                        target_location['y'] + target_size['height'] / 2
                    )
                except:
                    target_pos = (random.randint(100, 800), random.randint(100, 600))
            elif target_pos is None:
                target_pos = (random.randint(100, 800), random.randint(100, 600))
            
            # 生成贝塞尔曲线路径
            points = self.generate_bezier_curve((current_pos[0], current_pos[1]), target_pos)
            
            # 执行鼠标移动
            timestamps = []
            total_time = random.uniform(*self.mouse_speed_range)
            
            for i, (x, y) in enumerate(points):
                if i == 0:
                    continue  # 跳过起始点
                
                # 计算移动时间
                move_time = total_time * (i / len(points))
                timestamps.append(start_time + move_time)
                
                try:
                    # 移动到指定位置
                    self.actions.move_by_offset(x - current_pos[0], y - current_pos[1])
                    current_pos = [x, y]
                    
                    # 随机停顿
                    time.sleep(random.uniform(0.01, 0.03))
                except:
                    pass
            
            # 执行动作
            try:
                self.actions.perform()
                self.actions.reset_actions()  # 重置动作链
            except:
                pass
            
            # 计算距离
            distance = math.sqrt((target_pos[0] - points[0][0])**2 + (target_pos[1] - points[0][1])**2)
            
            return MouseTrajectory(
                points=points,
                timestamps=timestamps,
                total_time=time.time() - start_time,
                distance=distance
            )
            
        except Exception as e:
            self.logger.error(f"鼠标移动模拟失败: {e}")
            # 返回简单的轨迹数据
            return MouseTrajectory(
                points=[(0, 0), (100, 100)],
                timestamps=[start_time, start_time + 1],
                total_time=1.0,
                distance=141.42
            )
    
    def human_like_click(self, element=None, pos: Tuple[float, float] = None) -> BehaviorAction:
        """
        模拟人类点击行为
        
        Args:
            element: 要点击的元素
            pos: 点击位置坐标
            
        Returns:
            BehaviorAction: 行为动作记录
        """
        action = BehaviorAction(action_type=ActionType.CLICK)
        start_time = time.time()
        
        try:
            # 模拟鼠标移动轨迹
            trajectory = self.simulate_mouse_movement(element, pos)
            
            # 随机停顿
            pre_click_delay = random.uniform(*self.click_delay_range)
            time.sleep(pre_click_delay)
            
            # 执行点击
            if element and self.driver and SELENIUM_AVAILABLE:
                try:
                    element.click()
                    action.target = str(element)
                except Exception as e:
                    self.logger.error(f"元素点击失败: {e}")
                    action.success = False
                    action.error_message = str(e)
            elif pos and self.driver and SELENIUM_AVAILABLE:
                try:
                    self.actions.move_to_element_with_offset(self.driver.find_element(By.TAG_NAME, "body"), pos[0], pos[1])
                    self.actions.click()
                    self.actions.perform()
                    self.actions.reset_actions()
                    action.target = f"position_{pos[0]}_{pos[1]}"
                except Exception as e:
                    self.logger.error(f"位置点击失败: {e}")
                    action.success = False
                    action.error_message = str(e)
            else:
                # 模拟点击（无实际操作）
                action.target = "simulated_click"
            
            # 点击后停顿
            post_click_delay = random.uniform(0.5, 1.5)
            time.sleep(post_click_delay)
            
            action.duration = time.time() - start_time
            action.parameters = {
                'pre_click_delay': pre_click_delay,
                'post_click_delay': post_click_delay,
                'trajectory_points': len(trajectory.points),
                'trajectory_time': trajectory.total_time
            }
            
        except Exception as e:
            self.logger.error(f"人类点击模拟失败: {e}")
            action.success = False
            action.error_message = str(e)
            action.duration = time.time() - start_time
        
        self._record_action(action)
        return action
    
    def human_like_typing(self, element=None, text: str = "", clear_first: bool = True) -> BehaviorAction:
        """
        模拟人类打字行为
        
        Args:
            element: 输入元素
            text: 要输入的文本
            clear_first: 是否先清空输入框
            
        Returns:
            BehaviorAction: 行为动作记录
        """
        action = BehaviorAction(action_type=ActionType.TYPE)
        start_time = time.time()
        
        try:
            intervals = []
            
            # 先点击元素获得焦点
            if element and self.driver and SELENIUM_AVAILABLE:
                try:
                    element.click()
                    action.target = str(element)
                    
                    # 清空输入框
                    if clear_first:
                        element.clear()
                        time.sleep(random.uniform(0.1, 0.3))
                        
                except Exception as e:
                    self.logger.error(f"元素点击失败: {e}")
                    action.success = False
                    action.error_message = str(e)
                    self._record_action(action)
                    return action
            
            # 模拟逐字符输入
            for i, char in enumerate(text):
                # 计算打字间隔
                if char == ' ':
                    # 空格的间隔稍长
                    interval = random.uniform(0.1, 0.4)
                elif char in '.,!?;:':
                    # 标点符号后的间隔较长
                    interval = random.uniform(0.2, 0.6)
                else:
                    # 普通字符
                    interval = random.uniform(*self.typing_speed_range)
                
                # 偶尔有较长的停顿（思考时间）
                if random.random() < 0.05:  # 5%概率
                    interval += random.uniform(0.5, 2.0)
                
                intervals.append(interval)
                
                # 输入字符
                if element and self.driver and SELENIUM_AVAILABLE:
                    try:
                        element.send_keys(char)
                    except Exception as e:
                        self.logger.error(f"字符输入失败: {e}")
                        action.success = False
                        action.error_message = str(e)
                        break
                
                # 等待间隔
                time.sleep(interval)
            
            action.duration = time.time() - start_time
            
            # 计算WPM（每分钟字数）
            if action.duration > 0:
                words = len(text.split())
                wpm = (words / action.duration) * 60
            else:
                wpm = 0
            
            action.parameters = {
                'text_length': len(text),
                'total_intervals': len(intervals),
                'average_interval': sum(intervals) / len(intervals) if intervals else 0,
                'wpm': wpm,
                'clear_first': clear_first
            }
            
        except Exception as e:
            self.logger.error(f"人类打字模拟失败: {e}")
            action.success = False
            action.error_message = str(e)
            action.duration = time.time() - start_time
        
        self._record_action(action)
        return action
    
    def human_like_scroll(self, direction: str = "down", distance: int = None) -> BehaviorAction:
        """
        模拟人类滚动行为
        
        Args:
            direction: 滚动方向 ("up", "down", "left", "right")
            distance: 滚动距离（像素）
            
        Returns:
            BehaviorAction: 行为动作记录
        """
        action = BehaviorAction(action_type=ActionType.SCROLL)
        start_time = time.time()
        
        try:
            if distance is None:
                distance = random.randint(100, 500)
            
            # 分段滚动，模拟人类行为
            segments = random.randint(3, 8)
            segment_distance = distance // segments
            
            for i in range(segments):
                if self.driver and SELENIUM_AVAILABLE:
                    try:
                        if direction == "down":
                            self.driver.execute_script(f"window.scrollBy(0, {segment_distance});")
                        elif direction == "up":
                            self.driver.execute_script(f"window.scrollBy(0, -{segment_distance});")
                        elif direction == "right":
                            self.driver.execute_script(f"window.scrollBy({segment_distance}, 0);")
                        elif direction == "left":
                            self.driver.execute_script(f"window.scrollBy(-{segment_distance}, 0);")
                    except Exception as e:
                        self.logger.error(f"滚动执行失败: {e}")
                        action.success = False
                        action.error_message = str(e)
                        break
                
                # 滚动间隔
                scroll_delay = random.uniform(*self.scroll_speed_range)
                time.sleep(scroll_delay)
            
            action.duration = time.time() - start_time
            action.parameters = {
                'direction': direction,
                'total_distance': distance,
                'segments': segments,
                'segment_distance': segment_distance
            }
            
        except Exception as e:
            self.logger.error(f"人类滚动模拟失败: {e}")
            action.success = False
            action.error_message = str(e)
            action.duration = time.time() - start_time
        
        self._record_action(action)
        return action
    
    def human_like_wait(self, min_time: float = None, max_time: float = None) -> BehaviorAction:
        """
        模拟人类等待行为
        
        Args:
            min_time: 最小等待时间
            max_time: 最大等待时间
            
        Returns:
            BehaviorAction: 行为动作记录
        """
        action = BehaviorAction(action_type=ActionType.WAIT)
        start_time = time.time()
        
        try:
            if min_time is None or max_time is None:
                wait_time = random.uniform(*self.page_stay_range)
            else:
                wait_time = random.uniform(min_time, max_time)
            
            # 在等待期间偶尔有小动作
            elapsed = 0
            while elapsed < wait_time:
                sleep_time = min(random.uniform(0.5, 2.0), wait_time - elapsed)
                time.sleep(sleep_time)
                elapsed += sleep_time
                
                # 偶尔移动鼠标
                if random.random() < 0.3 and self.driver and SELENIUM_AVAILABLE:
                    try:
                        small_move_x = random.randint(-10, 10)
                        small_move_y = random.randint(-10, 10)
                        self.actions.move_by_offset(small_move_x, small_move_y)
                        self.actions.perform()
                        self.actions.reset_actions()
                    except:
                        pass
            
            action.duration = time.time() - start_time
            action.parameters = {
                'planned_wait_time': wait_time,
                'actual_wait_time': action.duration
            }
            
        except Exception as e:
            self.logger.error(f"人类等待模拟失败: {e}")
            action.success = False
            action.error_message = str(e)
            action.duration = time.time() - start_time
        
        self._record_action(action)
        return action
    
    def simulate_page_interaction(self, actions: List[Dict[str, Any]]) -> List[BehaviorAction]:
        """
        模拟页面交互序列
        
        Args:
            actions: 动作序列配置
            
        Returns:
            List[BehaviorAction]: 执行的动作列表
        """
        results = []
        
        for action_config in actions:
            action_type = action_config.get('type')
            params = action_config.get('params', {})
            
            try:
                if action_type == 'click':
                    element = params.get('element')
                    pos = params.get('position')
                    result = self.human_like_click(element, pos)
                elif action_type == 'type':
                    element = params.get('element')
                    text = params.get('text', '')
                    clear_first = params.get('clear_first', True)
                    result = self.human_like_typing(element, text, clear_first)
                elif action_type == 'scroll':
                    direction = params.get('direction', 'down')
                    distance = params.get('distance')
                    result = self.human_like_scroll(direction, distance)
                elif action_type == 'wait':
                    min_time = params.get('min_time')
                    max_time = params.get('max_time')
                    result = self.human_like_wait(min_time, max_time)
                else:
                    self.logger.warning(f"未知的动作类型: {action_type}")
                    continue
                
                results.append(result)
                
                # 动作间随机停顿
                if random.random() < 0.7:  # 70%概率
                    time.sleep(random.uniform(0.2, 1.0))
                    
            except Exception as e:
                self.logger.error(f"页面交互模拟失败: {e}")
                error_action = BehaviorAction(
                    action_type=ActionType.WAIT,
                    success=False,
                    error_message=str(e)
                )
                results.append(error_action)
        
        return results
    
    def _learn_from_session(self, session: BehaviorSession):
        """从会话中学习行为模式"""
        try:
            # 分析成功的动作模式
            successful_actions = [action for action in session.actions if action.success]
            
            if len(successful_actions) < 3:
                return
            
            # 提取特征
            features = {
                'avg_click_delay': 0,
                'avg_typing_speed': 0,
                'avg_scroll_speed': 0,
                'success_rate': session.success_rate,
                'total_time': session.end_time - session.start_time if session.end_time else 0
            }
            
            # 计算平均值
            click_delays = []
            typing_speeds = []
            scroll_speeds = []
            
            for action in successful_actions:
                if action.action_type == ActionType.CLICK and 'pre_click_delay' in action.parameters:
                    click_delays.append(action.parameters['pre_click_delay'])
                elif action.action_type == ActionType.TYPE and 'average_interval' in action.parameters:
                    typing_speeds.append(action.parameters['average_interval'])
                elif action.action_type == ActionType.SCROLL and action.duration > 0:
                    scroll_speeds.append(action.duration)
            
            if click_delays:
                features['avg_click_delay'] = sum(click_delays) / len(click_delays)
            if typing_speeds:
                features['avg_typing_speed'] = sum(typing_speeds) / len(typing_speeds)
            if scroll_speeds:
                features['avg_scroll_speed'] = sum(scroll_speeds) / len(scroll_speeds)
            
            # 保存学习到的模式
            pattern_key = f"{session.pattern.value}_{len(self.learned_patterns)}"
            self.learned_patterns[pattern_key] = features
            
            # 如果学习到足够多的模式，进行适应性调整
            if len(self.learned_patterns) >= self.adaptation_threshold:
                self._adapt_behavior_parameters()
            
        except Exception as e:
            self.logger.error(f"行为学习失败: {e}")
    
    def _adapt_behavior_parameters(self):
        """适应性调整行为参数"""
        try:
            if not self.learned_patterns:
                return
            
            # 计算平均值
            avg_features = {}
            for key in ['avg_click_delay', 'avg_typing_speed', 'avg_scroll_speed']:
                values = [pattern[key] for pattern in self.learned_patterns.values() if pattern[key] > 0]
                if values:
                    avg_features[key] = sum(values) / len(values)
            
            # 调整参数
            if 'avg_click_delay' in avg_features:
                base_delay = avg_features['avg_click_delay']
                self.click_delay_range = (base_delay * 0.7, base_delay * 1.3)
            
            if 'avg_typing_speed' in avg_features:
                base_speed = avg_features['avg_typing_speed']
                self.typing_speed_range = (base_speed * 0.8, base_speed * 1.2)
            
            self.logger.info("行为参数已适应性调整")
            
        except Exception as e:
            self.logger.error(f"行为参数适应失败: {e}")
    
    def get_behavior_stats(self) -> Dict[str, Any]:
        """获取行为统计信息"""
        with self.lock:
            if not self.behavior_history:
                return {
                    'total_sessions': 0,
                    'total_actions': 0,
                    'average_success_rate': 0.0,
                    'learned_patterns': len(self.learned_patterns)
                }
            
            total_actions = sum(session.total_actions for session in self.behavior_history)
            avg_success_rate = sum(session.success_rate for session in self.behavior_history) / len(self.behavior_history)
            
            # 按动作类型统计
            action_type_stats = {}
            for session in self.behavior_history:
                for action in session.actions:
                    action_type = action.action_type.value
                    if action_type not in action_type_stats:
                        action_type_stats[action_type] = {'count': 0, 'success': 0}
                    action_type_stats[action_type]['count'] += 1
                    if action.success:
                        action_type_stats[action_type]['success'] += 1
            
            return {
                'total_sessions': len(self.behavior_history),
                'total_actions': total_actions,
                'average_success_rate': avg_success_rate,
                'action_type_stats': action_type_stats,
                'learned_patterns': len(self.learned_patterns),
                'current_pattern': self.pattern.value,
                'adaptation_enabled': self.learning_enabled
            }
    
    def export_behavior_data(self, filepath: str):
        """导出行为数据"""
        try:
            data = {
                'sessions': [],
                'learned_patterns': self.learned_patterns,
                'configuration': {
                    'pattern': self.pattern.value,
                    'mouse_speed_range': self.mouse_speed_range,
                    'click_delay_range': self.click_delay_range,
                    'typing_speed_range': self.typing_speed_range,
                    'page_stay_range': self.page_stay_range
                }
            }
            
            # 导出会话数据
            for session in self.behavior_history:
                session_data = {
                    'session_id': session.session_id,
                    'start_time': session.start_time,
                    'end_time': session.end_time,
                    'pattern': session.pattern.value,
                    'success_rate': session.success_rate,
                    'total_actions': session.total_actions,
                    'actions': []
                }
                
                for action in session.actions:
                    action_data = {
                        'type': action.action_type.value,
                        'target': action.target,
                        'timestamp': action.timestamp,
                        'duration': action.duration,
                        'success': action.success,
                        'parameters': action.parameters
                    }
                    session_data['actions'].append(action_data)
                
                data['sessions'].append(session_data)
            
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
            
            self.logger.info(f"行为数据已导出到: {filepath}")
            
        except Exception as e:
            self.logger.error(f"行为数据导出失败: {e}")


# 全局行为模拟器实例
behavior_simulator: Optional[HumanBehaviorSimulator] = None


def get_behavior_simulator(driver=None, pattern: BehaviorPattern = BehaviorPattern.NORMAL) -> HumanBehaviorSimulator:
    """获取全局行为模拟器实例"""
    global behavior_simulator
    if behavior_simulator is None or behavior_simulator.driver != driver:
        behavior_simulator = HumanBehaviorSimulator(driver, pattern)
    return behavior_simulator


def simulate_human_click(element=None, pos: Tuple[float, float] = None, driver=None) -> BehaviorAction:
    """
    便捷的人类点击模拟函数
    
    Args:
        element: 要点击的元素
        pos: 点击位置坐标
        driver: WebDriver实例
        
    Returns:
        BehaviorAction: 行为动作记录
    """
    simulator = get_behavior_simulator(driver)
    return simulator.human_like_click(element, pos)


def simulate_human_typing(element=None, text: str = "", driver=None) -> BehaviorAction:
    """
    便捷的人类打字模拟函数
    
    Args:
        element: 输入元素
        text: 要输入的文本
        driver: WebDriver实例
        
    Returns:
        BehaviorAction: 行为动作记录
    """
    simulator = get_behavior_simulator(driver)
    return simulator.human_like_typing(element, text)
[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "ticket-grabber-test"
version = "1.0.0"
description = "Ticket Grabbing Test System for Defensive Security Testing"
readme = "README.md"
license = {text = "MIT"}
authors = [
    {name = "Security Research Team", email = "<EMAIL>"}
]
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Information Technology",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.8",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Topic :: Security",
    "Topic :: Software Development :: Testing",
]
requires-python = ">=3.8"
dependencies = [
    "aiohttp>=3.8.0",
    "requests>=2.28.0",
    "selenium>=4.10.0",
    "opencv-python>=4.7.0",
    "pytesseract>=0.3.10",
    "numpy>=1.24.0",
    "redis>=4.5.0",
    "click>=8.1.0",
    "rich>=13.3.0",
    "loguru>=0.7.0",
]
"""
具体攻击场景实现

实现各种类型的攻击场景，包括基础攻击、高级攻击、分布式攻击等。
"""

import time
import random
import asyncio
import threading
import requests
from typing import Dict, List, Any, Optional
from concurrent.futures import ThreadPoolExecutor, as_completed
import json

from .base_scenario import (
    BaseScenario, ScenarioConfig, AttackType, ScenarioResult,
    ScenarioStatus, ScenarioMetrics
)

try:
    from ..modules.http_client import TicketGrabber, AsyncTicketGrabber, RetryStrategy
    from ..modules.anti_detection import UserAgentManager, FingerprintSpoofing
    from ..modules.behavior_sim import HumanBehaviorSimulator, BehaviorPattern
    from ..modules.captcha_solver import CaptchaRecognizer
    from ..utils.proxy_pool import ProxyPool
    from ..core.logger import get_logger
except ImportError:
    import logging
    def get_logger(name):
        return logging.getLogger(name)


class BasicAttackScenario(BaseScenario):
    """
    基础攻击场景
    
    实现高频请求、IP轮换、User-Agent伪装等基础攻击手段
    """
    
    def __init__(self, config: ScenarioConfig):
        super().__init__(config)
        self.logger = get_logger(f"BasicAttack_{self.scenario_id[:8]}")
    
    def run_scenario(self):
        """执行基础攻击场景"""
        self.add_event("BASIC_ATTACK_START", "开始基础攻击场景")
        
        try:
            # 初始化HTTP客户端
            self.http_client = TicketGrabber()
            
            # 配置重试策略
            retry_strategy = RetryStrategy(
                max_retries=3,
                base_delay=1.0,
                max_delay=10.0,
                backoff_factor=2.0
            )
            
            # 执行高频请求攻击
            self._execute_high_frequency_attack()
            
            # 执行User-Agent轮换攻击
            if self.config.use_anti_detection:
                self._execute_ua_rotation_attack()
            
            # 执行IP轮换攻击
            if self.config.use_proxy:
                self._execute_ip_rotation_attack()
            
        except Exception as e:
            self.logger.error(f"基础攻击场景执行失败: {e}")
            raise
    
    def _execute_high_frequency_attack(self):
        """执行高频请求攻击"""
        self.add_event("HIGH_FREQ_START", "开始高频请求攻击")
        
        request_count = 0
        max_requests = self.config.max_requests
        interval = self.config.request_interval
        
        while request_count < max_requests and not self._check_stop():
            self._check_pause()
            
            try:
                start_time = time.time()
                
                # 发送请求
                response = self.http_client.get(
                    self.config.target_url,
                    timeout=self.config.timeout
                )
                
                response_time = time.time() - start_time
                
                # 更新指标
                self.metrics.total_requests += 1
                request_count += 1
                
                if response and response.status_code == 200:
                    self.metrics.successful_requests += 1
                    self.add_event("REQUEST_SUCCESS", f"请求成功: {response.status_code}")
                else:
                    self.metrics.failed_requests += 1
                    if response:
                        self.add_event("REQUEST_FAILED", f"请求失败: {response.status_code}")
                    else:
                        self.add_event("REQUEST_FAILED", "请求失败: 无响应")
                
                # 更新响应时间统计
                self.metrics.average_response_time = (
                    (self.metrics.average_response_time * (request_count - 1) + response_time) / request_count
                )
                self.metrics.min_response_time = min(self.metrics.min_response_time, response_time)
                self.metrics.max_response_time = max(self.metrics.max_response_time, response_time)
                
                # 检查是否被阻止
                if response and self._is_blocked_response(response):
                    self.metrics.blocked_requests += 1
                    self.add_event("REQUEST_BLOCKED", "请求被阻止", level="WARNING")
                
                # 调用进度回调
                if self.on_progress:
                    self.on_progress(self)
                
            except requests.exceptions.Timeout:
                self.metrics.timeout_count += 1
                self.metrics.failed_requests += 1
                self.add_event("REQUEST_TIMEOUT", "请求超时", level="WARNING")
                
            except Exception as e:
                self.metrics.error_count += 1
                self.metrics.failed_requests += 1
                self.add_event("REQUEST_ERROR", f"请求错误: {e}", level="ERROR")
            
            # 等待间隔
            if interval > 0:
                time.sleep(interval)
        
        self.add_event("HIGH_FREQ_END", f"高频请求攻击完成，共发送 {request_count} 个请求")
    
    def _execute_ua_rotation_attack(self):
        """执行User-Agent轮换攻击"""
        self.add_event("UA_ROTATION_START", "开始User-Agent轮换攻击")
        
        if not self.ua_manager:
            self.add_event("UA_ROTATION_SKIP", "User-Agent管理器不可用，跳过", level="WARNING")
            return
        
        request_count = 0
        max_requests = min(50, self.config.max_requests // 2)  # 限制请求数
        
        while request_count < max_requests and not self._check_stop():
            self._check_pause()
            
            try:
                # 轮换User-Agent
                ua = self.ua_manager.get_random_ua()
                headers = {'User-Agent': ua}
                
                start_time = time.time()
                response = self.http_client.get(
                    self.config.target_url,
                    headers=headers,
                    timeout=self.config.timeout
                )
                response_time = time.time() - start_time
                
                # 更新指标
                self.metrics.total_requests += 1
                request_count += 1
                
                if response and response.status_code == 200:
                    self.metrics.successful_requests += 1
                    self.add_event("UA_REQUEST_SUCCESS", f"UA轮换请求成功: {ua[:50]}...")
                else:
                    self.metrics.failed_requests += 1
                    self.add_event("UA_REQUEST_FAILED", f"UA轮换请求失败")
                
                # 更新响应时间
                total_requests = self.metrics.total_requests
                self.metrics.average_response_time = (
                    (self.metrics.average_response_time * (total_requests - 1) + response_time) / total_requests
                )
                
            except Exception as e:
                self.metrics.error_count += 1
                self.metrics.failed_requests += 1
                self.add_event("UA_REQUEST_ERROR", f"UA轮换请求错误: {e}", level="ERROR")
            
            time.sleep(self.config.request_interval * 2)  # 稍长间隔
        
        self.add_event("UA_ROTATION_END", f"User-Agent轮换攻击完成，共发送 {request_count} 个请求")
    
    def _execute_ip_rotation_attack(self):
        """执行IP轮换攻击"""
        self.add_event("IP_ROTATION_START", "开始IP轮换攻击")
        
        if not self.proxy_pool:
            self.add_event("IP_ROTATION_SKIP", "代理池不可用，跳过", level="WARNING")
            return
        
        request_count = 0
        max_requests = min(30, self.config.max_requests // 3)  # 限制请求数
        
        while request_count < max_requests and not self._check_stop():
            self._check_pause()
            
            try:
                # 获取代理
                proxy = self.proxy_pool.get_proxy()
                if not proxy:
                    self.add_event("IP_ROTATION_NO_PROXY", "无可用代理", level="WARNING")
                    break
                
                proxies = {
                    'http': f"http://{proxy['host']}:{proxy['port']}",
                    'https': f"http://{proxy['host']}:{proxy['port']}"
                }
                
                start_time = time.time()
                response = self.http_client.get(
                    self.config.target_url,
                    proxies=proxies,
                    timeout=self.config.timeout
                )
                response_time = time.time() - start_time
                
                # 更新指标
                self.metrics.total_requests += 1
                request_count += 1
                
                if response and response.status_code == 200:
                    self.metrics.successful_requests += 1
                    self.add_event("IP_REQUEST_SUCCESS", f"IP轮换请求成功: {proxy['host']}")
                    # 标记代理可用
                    self.proxy_pool.mark_proxy_success(proxy)
                else:
                    self.metrics.failed_requests += 1
                    self.add_event("IP_REQUEST_FAILED", f"IP轮换请求失败: {proxy['host']}")
                    # 标记代理失败
                    self.proxy_pool.mark_proxy_failure(proxy)
                
                # 更新响应时间
                total_requests = self.metrics.total_requests
                self.metrics.average_response_time = (
                    (self.metrics.average_response_time * (total_requests - 1) + response_time) / total_requests
                )
                
            except Exception as e:
                self.metrics.error_count += 1
                self.metrics.failed_requests += 1
                self.add_event("IP_REQUEST_ERROR", f"IP轮换请求错误: {e}", level="ERROR")
                if 'proxy' in locals():
                    self.proxy_pool.mark_proxy_failure(proxy)
            
            time.sleep(self.config.request_interval * 3)  # 更长间隔
        
        self.add_event("IP_ROTATION_END", f"IP轮换攻击完成，共发送 {request_count} 个请求")
    
    def _is_blocked_response(self, response) -> bool:
        """检查响应是否表示被阻止"""
        if not response:
            return False
        
        # 检查状态码
        if response.status_code in [403, 429, 503]:
            return True
        
        # 检查响应内容
        content = response.text.lower()
        blocked_keywords = ['blocked', 'forbidden', 'rate limit', 'too many requests', 'captcha']
        
        for keyword in blocked_keywords:
            if keyword in content:
                return True
        
        return False


class AdvancedAttackScenario(BaseScenario):
    """
    高级攻击场景
    
    实现分布式抢票、行为模拟、验证码绕过等高级攻击手段
    """
    
    def __init__(self, config: ScenarioConfig):
        super().__init__(config)
        self.logger = get_logger(f"AdvancedAttack_{self.scenario_id[:8]}")
    
    def run_scenario(self):
        """执行高级攻击场景"""
        self.add_event("ADVANCED_ATTACK_START", "开始高级攻击场景")
        
        try:
            # 执行并发攻击
            self._execute_concurrent_attack()
            
            # 执行行为模拟攻击
            if self.config.use_behavior_sim:
                self._execute_behavior_simulation_attack()
            
            # 执行验证码绕过攻击
            if self.config.use_captcha_solver:
                self._execute_captcha_bypass_attack()
            
        except Exception as e:
            self.logger.error(f"高级攻击场景执行失败: {e}")
            raise
    
    def _execute_concurrent_attack(self):
        """执行并发攻击"""
        self.add_event("CONCURRENT_START", "开始并发攻击")
        
        max_workers = self.config.concurrent_users
        requests_per_worker = self.config.max_requests // max_workers
        
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # 提交任务
            futures = []
            for worker_id in range(max_workers):
                future = executor.submit(self._worker_attack, worker_id, requests_per_worker)
                futures.append(future)
            
            # 等待完成
            for future in as_completed(futures):
                try:
                    worker_result = future.result()
                    self.add_event("WORKER_COMPLETE", f"工作线程完成: {worker_result}")
                except Exception as e:
                    self.add_event("WORKER_ERROR", f"工作线程错误: {e}", level="ERROR")
        
        self.add_event("CONCURRENT_END", "并发攻击完成")
    
    def _worker_attack(self, worker_id: int, max_requests: int) -> Dict[str, Any]:
        """工作线程攻击逻辑"""
        worker_stats = {
            'worker_id': worker_id,
            'requests': 0,
            'success': 0,
            'failed': 0,
            'errors': 0
        }
        
        # 创建独立的HTTP客户端
        http_client = TicketGrabber()
        
        for i in range(max_requests):
            if self._check_stop():
                break
            
            self._check_pause()
            
            try:
                # 添加随机延迟避免同时请求
                time.sleep(random.uniform(0, self.config.request_interval))
                
                start_time = time.time()
                response = http_client.get(
                    self.config.target_url,
                    timeout=self.config.timeout
                )
                response_time = time.time() - start_time
                
                # 更新统计
                worker_stats['requests'] += 1
                self.metrics.total_requests += 1
                
                if response and response.status_code == 200:
                    worker_stats['success'] += 1
                    self.metrics.successful_requests += 1
                else:
                    worker_stats['failed'] += 1
                    self.metrics.failed_requests += 1
                
                # 更新响应时间（线程安全）
                with threading.Lock():
                    total = self.metrics.total_requests
                    self.metrics.average_response_time = (
                        (self.metrics.average_response_time * (total - 1) + response_time) / total
                    )
                    self.metrics.min_response_time = min(self.metrics.min_response_time, response_time)
                    self.metrics.max_response_time = max(self.metrics.max_response_time, response_time)
                
            except Exception as e:
                worker_stats['errors'] += 1
                self.metrics.error_count += 1
                self.metrics.failed_requests += 1
        
        return worker_stats
    
    def _execute_behavior_simulation_attack(self):
        """执行行为模拟攻击"""
        self.add_event("BEHAVIOR_SIM_START", "开始行为模拟攻击")
        
        if not self.behavior_simulator:
            self.add_event("BEHAVIOR_SIM_SKIP", "行为模拟器不可用，跳过", level="WARNING")
            return
        
        # 开始行为会话
        session_id = self.behavior_simulator.start_session("attack_session")
        
        try:
            request_count = 0
            max_requests = min(20, self.config.max_requests // 5)  # 限制请求数
            
            while request_count < max_requests and not self._check_stop():
                self._check_pause()
                
                try:
                    # 模拟人类等待
                    wait_action = self.behavior_simulator.human_like_wait(
                        min_time=self.config.request_interval,
                        max_time=self.config.request_interval * 3
                    )
                    
                    # 发送请求
                    start_time = time.time()
                    response = self.http_client.get(
                        self.config.target_url,
                        timeout=self.config.timeout
                    )
                    response_time = time.time() - start_time
                    
                    # 更新指标
                    self.metrics.total_requests += 1
                    request_count += 1
                    
                    if response and response.status_code == 200:
                        self.metrics.successful_requests += 1
                        self.add_event("BEHAVIOR_REQUEST_SUCCESS", "行为模拟请求成功")
                    else:
                        self.metrics.failed_requests += 1
                        self.add_event("BEHAVIOR_REQUEST_FAILED", "行为模拟请求失败")
                    
                    # 更新响应时间
                    total_requests = self.metrics.total_requests
                    self.metrics.average_response_time = (
                        (self.metrics.average_response_time * (total_requests - 1) + response_time) / total_requests
                    )
                    
                    # 模拟随机行为
                    if random.random() < 0.3:  # 30%概率
                        # 模拟额外等待（思考时间）
                        think_action = self.behavior_simulator.human_like_wait(
                            min_time=1.0, max_time=5.0
                        )
                    
                except Exception as e:
                    self.metrics.error_count += 1
                    self.metrics.failed_requests += 1
                    self.add_event("BEHAVIOR_REQUEST_ERROR", f"行为模拟请求错误: {e}", level="ERROR")
            
        finally:
            # 结束行为会话
            session = self.behavior_simulator.end_session()
            if session:
                self.add_event("BEHAVIOR_SESSION_END", 
                             f"行为会话结束，成功率: {session.success_rate:.2%}")
        
        self.add_event("BEHAVIOR_SIM_END", f"行为模拟攻击完成，共发送 {request_count} 个请求")
    
    def _execute_captcha_bypass_attack(self):
        """执行验证码绕过攻击"""
        self.add_event("CAPTCHA_BYPASS_START", "开始验证码绕过攻击")
        
        if not self.captcha_solver:
            self.add_event("CAPTCHA_BYPASS_SKIP", "验证码识别器不可用，跳过", level="WARNING")
            return
        
        # 模拟验证码绕过场景
        request_count = 0
        max_requests = min(10, self.config.max_requests // 10)  # 限制请求数
        
        while request_count < max_requests and not self._check_stop():
            self._check_pause()
            
            try:
                # 发送请求获取验证码
                start_time = time.time()
                response = self.http_client.get(
                    self.config.target_url,
                    timeout=self.config.timeout
                )
                
                if response and 'captcha' in response.text.lower():
                    # 模拟验证码识别
                    self.metrics.captcha_count += 1
                    self.add_event("CAPTCHA_DETECTED", "检测到验证码")
                    
                    # 这里应该实际识别验证码，但为了演示，我们模拟成功
                    captcha_result = "simulated_result"
                    
                    if captcha_result:
                        self.add_event("CAPTCHA_SOLVED", "验证码识别成功")
                        # 模拟提交验证码结果
                        time.sleep(1)  # 模拟处理时间
                    else:
                        self.add_event("CAPTCHA_FAILED", "验证码识别失败", level="WARNING")
                
                response_time = time.time() - start_time
                
                # 更新指标
                self.metrics.total_requests += 1
                request_count += 1
                
                if response and response.status_code == 200:
                    self.metrics.successful_requests += 1
                else:
                    self.metrics.failed_requests += 1
                
                # 更新响应时间
                total_requests = self.metrics.total_requests
                self.metrics.average_response_time = (
                    (self.metrics.average_response_time * (total_requests - 1) + response_time) / total_requests
                )
                
            except Exception as e:
                self.metrics.error_count += 1
                self.metrics.failed_requests += 1
                self.add_event("CAPTCHA_REQUEST_ERROR", f"验证码绕过请求错误: {e}", level="ERROR")
            
            time.sleep(self.config.request_interval * 5)  # 较长间隔
        
        self.add_event("CAPTCHA_BYPASS_END", f"验证码绕过攻击完成，共发送 {request_count} 个请求")


class DistributedAttackScenario(BaseScenario):
    """
    分布式攻击场景
    
    模拟分布式抢票攻击，使用多个IP、多个会话等
    """
    
    def __init__(self, config: ScenarioConfig):
        super().__init__(config)
        self.logger = get_logger(f"DistributedAttack_{self.scenario_id[:8]}")
    
    def run_scenario(self):
        """执行分布式攻击场景"""
        self.add_event("DISTRIBUTED_ATTACK_START", "开始分布式攻击场景")
        
        try:
            # 执行多IP并发攻击
            if self.config.use_proxy:
                self._execute_multi_ip_attack()
            else:
                self.add_event("MULTI_IP_SKIP", "代理池不可用，跳过多IP攻击", level="WARNING")
            
            # 执行多会话攻击
            self._execute_multi_session_attack()
            
            # 执行协调攻击
            self._execute_coordinated_attack()
            
        except Exception as e:
            self.logger.error(f"分布式攻击场景执行失败: {e}")
            raise
    
    def _execute_multi_ip_attack(self):
        """执行多IP攻击"""
        self.add_event("MULTI_IP_START", "开始多IP攻击")
        
        if not self.proxy_pool:
            return
        
        # 获取多个代理
        proxy_count = min(5, self.config.concurrent_users)
        proxies = []
        
        for _ in range(proxy_count):
            proxy = self.proxy_pool.get_proxy()
            if proxy:
                proxies.append(proxy)
        
        if not proxies:
            self.add_event("MULTI_IP_NO_PROXY", "无可用代理", level="WARNING")
            return
        
        # 为每个代理创建攻击线程
        with ThreadPoolExecutor(max_workers=len(proxies)) as executor:
            futures = []
            requests_per_proxy = self.config.max_requests // len(proxies)
            
            for i, proxy in enumerate(proxies):
                future = executor.submit(self._proxy_attack_worker, i, proxy, requests_per_proxy)
                futures.append(future)
            
            # 等待完成
            for future in as_completed(futures):
                try:
                    result = future.result()
                    self.add_event("PROXY_WORKER_COMPLETE", f"代理工作线程完成: {result}")
                except Exception as e:
                    self.add_event("PROXY_WORKER_ERROR", f"代理工作线程错误: {e}", level="ERROR")
        
        self.add_event("MULTI_IP_END", "多IP攻击完成")
    
    def _proxy_attack_worker(self, worker_id: int, proxy: Dict[str, Any], max_requests: int) -> Dict[str, Any]:
        """代理攻击工作线程"""
        worker_stats = {
            'worker_id': worker_id,
            'proxy': f"{proxy['host']}:{proxy['port']}",
            'requests': 0,
            'success': 0,
            'failed': 0
        }
        
        proxies = {
            'http': f"http://{proxy['host']}:{proxy['port']}",
            'https': f"http://{proxy['host']}:{proxy['port']}"
        }
        
        http_client = TicketGrabber()
        
        for i in range(max_requests):
            if self._check_stop():
                break
            
            self._check_pause()
            
            try:
                start_time = time.time()
                response = http_client.get(
                    self.config.target_url,
                    proxies=proxies,
                    timeout=self.config.timeout
                )
                response_time = time.time() - start_time
                
                worker_stats['requests'] += 1
                self.metrics.total_requests += 1
                
                if response and response.status_code == 200:
                    worker_stats['success'] += 1
                    self.metrics.successful_requests += 1
                    self.proxy_pool.mark_proxy_success(proxy)
                else:
                    worker_stats['failed'] += 1
                    self.metrics.failed_requests += 1
                    self.proxy_pool.mark_proxy_failure(proxy)
                
                # 更新响应时间
                with threading.Lock():
                    total = self.metrics.total_requests
                    self.metrics.average_response_time = (
                        (self.metrics.average_response_time * (total - 1) + response_time) / total
                    )
                
                time.sleep(self.config.request_interval)
                
            except Exception as e:
                worker_stats['failed'] += 1
                self.metrics.error_count += 1
                self.metrics.failed_requests += 1
                self.proxy_pool.mark_proxy_failure(proxy)
        
        return worker_stats
    
    def _execute_multi_session_attack(self):
        """执行多会话攻击"""
        self.add_event("MULTI_SESSION_START", "开始多会话攻击")
        
        session_count = min(3, self.config.concurrent_users)
        
        with ThreadPoolExecutor(max_workers=session_count) as executor:
            futures = []
            requests_per_session = self.config.max_requests // session_count
            
            for session_id in range(session_count):
                future = executor.submit(self._session_attack_worker, session_id, requests_per_session)
                futures.append(future)
            
            # 等待完成
            for future in as_completed(futures):
                try:
                    result = future.result()
                    self.add_event("SESSION_WORKER_COMPLETE", f"会话工作线程完成: {result}")
                except Exception as e:
                    self.add_event("SESSION_WORKER_ERROR", f"会话工作线程错误: {e}", level="ERROR")
        
        self.add_event("MULTI_SESSION_END", "多会话攻击完成")
    
    def _session_attack_worker(self, session_id: int, max_requests: int) -> Dict[str, Any]:
        """会话攻击工作线程"""
        worker_stats = {
            'session_id': session_id,
            'requests': 0,
            'success': 0,
            'failed': 0
        }
        
        # 创建独立的HTTP客户端和会话
        http_client = TicketGrabber()
        session = requests.Session()
        
        # 设置会话特定的User-Agent
        if self.ua_manager:
            ua = self.ua_manager.get_random_ua()
            session.headers.update({'User-Agent': ua})
        
        for i in range(max_requests):
            if self._check_stop():
                break
            
            self._check_pause()
            
            try:
                start_time = time.time()
                response = session.get(
                    self.config.target_url,
                    timeout=self.config.timeout
                )
                response_time = time.time() - start_time
                
                worker_stats['requests'] += 1
                self.metrics.total_requests += 1
                
                if response and response.status_code == 200:
                    worker_stats['success'] += 1
                    self.metrics.successful_requests += 1
                else:
                    worker_stats['failed'] += 1
                    self.metrics.failed_requests += 1
                
                # 更新响应时间
                with threading.Lock():
                    total = self.metrics.total_requests
                    self.metrics.average_response_time = (
                        (self.metrics.average_response_time * (total - 1) + response_time) / total
                    )
                
                time.sleep(self.config.request_interval)
                
            except Exception as e:
                worker_stats['failed'] += 1
                self.metrics.error_count += 1
                self.metrics.failed_requests += 1
        
        session.close()
        return worker_stats
    
    def _execute_coordinated_attack(self):
        """执行协调攻击"""
        self.add_event("COORDINATED_START", "开始协调攻击")
        
        # 模拟协调攻击：多个客户端在特定时间同时发起请求
        coordination_points = 3  # 协调点数量
        requests_per_point = self.config.max_requests // coordination_points
        
        for point in range(coordination_points):
            if self._check_stop():
                break
            
            self.add_event("COORDINATION_POINT", f"协调点 {point + 1}")
            
            # 等待协调时间
            coordination_delay = random.uniform(2, 5)
            time.sleep(coordination_delay)
            
            # 同时发起多个请求
            with ThreadPoolExecutor(max_workers=self.config.concurrent_users) as executor:
                futures = []
                
                for i in range(self.config.concurrent_users):
                    future = executor.submit(self._coordinated_request)
                    futures.append(future)
                
                # 等待所有请求完成
                for future in as_completed(futures):
                    try:
                        future.result()
                    except Exception as e:
                        self.add_event("COORDINATED_ERROR", f"协调请求错误: {e}", level="ERROR")
        
        self.add_event("COORDINATED_END", "协调攻击完成")
    
    def _coordinated_request(self):
        """协调请求"""
        try:
            http_client = TicketGrabber()
            
            start_time = time.time()
            response = http_client.get(
                self.config.target_url,
                timeout=self.config.timeout
            )
            response_time = time.time() - start_time
            
            # 更新指标
            self.metrics.total_requests += 1
            
            if response and response.status_code == 200:
                self.metrics.successful_requests += 1
            else:
                self.metrics.failed_requests += 1
            
            # 更新响应时间
            with threading.Lock():
                total = self.metrics.total_requests
                self.metrics.average_response_time = (
                    (self.metrics.average_response_time * (total - 1) + response_time) / total
                )
            
        except Exception as e:
            self.metrics.error_count += 1
            self.metrics.failed_requests += 1
            raise


class AdversarialAttackScenario(BaseScenario):
    """
    对抗性攻击场景
    
    实现自适应反检测、队列绕过等对抗性攻击手段
    """
    
    def __init__(self, config: ScenarioConfig):
        super().__init__(config)
        self.logger = get_logger(f"AdversarialAttack_{self.scenario_id[:8]}")
        
        # 对抗性参数
        self.detection_threshold = 0.3  # 检测阈值
        self.adaptation_history = []
        self.current_strategy = "normal"
    
    def run_scenario(self):
        """执行对抗性攻击场景"""
        self.add_event("ADVERSARIAL_ATTACK_START", "开始对抗性攻击场景")
        
        try:
            # 执行自适应攻击
            self._execute_adaptive_attack()
            
            # 执行反检测攻击
            self._execute_anti_detection_attack()
            
            # 执行队列绕过攻击
            self._execute_queue_bypass_attack()
            
        except Exception as e:
            self.logger.error(f"对抗性攻击场景执行失败: {e}")
            raise
    
    def _execute_adaptive_attack(self):
        """执行自适应攻击"""
        self.add_event("ADAPTIVE_START", "开始自适应攻击")
        
        request_count = 0
        max_requests = self.config.max_requests
        
        while request_count < max_requests and not self._check_stop():
            self._check_pause()
            
            try:
                # 根据当前策略调整参数
                interval = self._get_adaptive_interval()
                headers = self._get_adaptive_headers()
                
                start_time = time.time()
                response = self.http_client.get(
                    self.config.target_url,
                    headers=headers,
                    timeout=self.config.timeout
                )
                response_time = time.time() - start_time
                
                # 分析响应并调整策略
                self._analyze_response_and_adapt(response)
                
                # 更新指标
                self.metrics.total_requests += 1
                request_count += 1
                
                if response and response.status_code == 200:
                    self.metrics.successful_requests += 1
                    self.add_event("ADAPTIVE_SUCCESS", f"自适应请求成功，策略: {self.current_strategy}")
                else:
                    self.metrics.failed_requests += 1
                    self.add_event("ADAPTIVE_FAILED", f"自适应请求失败，策略: {self.current_strategy}")
                
                # 更新响应时间
                total_requests = self.metrics.total_requests
                self.metrics.average_response_time = (
                    (self.metrics.average_response_time * (total_requests - 1) + response_time) / total_requests
                )
                
                time.sleep(interval)
                
            except Exception as e:
                self.metrics.error_count += 1
                self.metrics.failed_requests += 1
                self.add_event("ADAPTIVE_ERROR", f"自适应请求错误: {e}", level="ERROR")
        
        self.add_event("ADAPTIVE_END", f"自适应攻击完成，最终策略: {self.current_strategy}")
    
    def _get_adaptive_interval(self) -> float:
        """获取自适应间隔"""
        base_interval = self.config.request_interval
        
        if self.current_strategy == "aggressive":
            return base_interval * 0.5
        elif self.current_strategy == "cautious":
            return base_interval * 2.0
        elif self.current_strategy == "random":
            return random.uniform(base_interval * 0.2, base_interval * 3.0)
        else:
            return base_interval
    
    def _get_adaptive_headers(self) -> Dict[str, str]:
        """获取自适应请求头"""
        headers = {}
        
        if self.ua_manager:
            if self.current_strategy == "aggressive":
                # 使用固定UA
                headers['User-Agent'] = self.ua_manager.get_random_ua()
            elif self.current_strategy == "cautious":
                # 使用常见UA
                headers['User-Agent'] = self.ua_manager.get_random_ua()
            elif self.current_strategy == "random":
                # 随机UA
                headers['User-Agent'] = self.ua_manager.get_random_ua()
        
        # 添加其他自适应头部
        if self.current_strategy == "stealth":
            headers.update({
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
                'Accept-Language': 'en-US,en;q=0.5',
                'Accept-Encoding': 'gzip, deflate',
                'Connection': 'keep-alive',
                'Upgrade-Insecure-Requests': '1'
            })
        
        return headers
    
    def _analyze_response_and_adapt(self, response):
        """分析响应并调整策略"""
        if not response:
            return
        
        # 检测是否被阻止
        is_blocked = self._is_blocked_response(response)
        
        # 记录历史
        self.adaptation_history.append({
            'timestamp': time.time(),
            'strategy': self.current_strategy,
            'status_code': response.status_code,
            'blocked': is_blocked,
            'response_time': 0  # 简化
        })
        
        # 保持历史记录在合理范围内
        if len(self.adaptation_history) > 20:
            self.adaptation_history = self.adaptation_history[-20:]
        
        # 分析最近的结果
        recent_blocked_rate = sum(1 for h in self.adaptation_history[-5:] if h['blocked']) / min(5, len(self.adaptation_history))
        
        # 调整策略
        if recent_blocked_rate > self.detection_threshold:
            # 被检测率过高，切换到更谨慎的策略
            if self.current_strategy == "aggressive":
                self.current_strategy = "normal"
                self.add_event("STRATEGY_CHANGE", "策略调整: aggressive -> normal")
            elif self.current_strategy == "normal":
                self.current_strategy = "cautious"
                self.add_event("STRATEGY_CHANGE", "策略调整: normal -> cautious")
            elif self.current_strategy == "cautious":
                self.current_strategy = "stealth"
                self.add_event("STRATEGY_CHANGE", "策略调整: cautious -> stealth")
        elif recent_blocked_rate < 0.1:
            # 被检测率很低，可以更激进
            if self.current_strategy == "stealth":
                self.current_strategy = "cautious"
                self.add_event("STRATEGY_CHANGE", "策略调整: stealth -> cautious")
            elif self.current_strategy == "cautious":
                self.current_strategy = "normal"
                self.add_event("STRATEGY_CHANGE", "策略调整: cautious -> normal")
            elif self.current_strategy == "normal":
                self.current_strategy = "aggressive"
                self.add_event("STRATEGY_CHANGE", "策略调整: normal -> aggressive")
    
    def _execute_anti_detection_attack(self):
        """执行反检测攻击"""
        self.add_event("ANTI_DETECTION_START", "开始反检测攻击")
        
        if not self.fingerprint_spoofing:
            self.add_event("ANTI_DETECTION_SKIP", "指纹伪装组件不可用，跳过", level="WARNING")
            return
        
        request_count = 0
        max_requests = min(15, self.config.max_requests // 6)
        
        while request_count < max_requests and not self._check_stop():
            self._check_pause()
            
            try:
                # 生成伪装指纹
                fingerprint = self.fingerprint_spoofing.generate_fingerprint()
                
                # 构造请求头
                headers = {
                    'User-Agent': fingerprint.get('user_agent', ''),
                    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
                    'Accept-Language': fingerprint.get('language', 'en-US,en;q=0.5'),
                    'Accept-Encoding': 'gzip, deflate'
                }
                
                start_time = time.time()
                response = self.http_client.get(
                    self.config.target_url,
                    headers=headers,
                    timeout=self.config.timeout
                )
                response_time = time.time() - start_time
                
                # 更新指标
                self.metrics.total_requests += 1
                request_count += 1
                
                if response and response.status_code == 200:
                    self.metrics.successful_requests += 1
                    self.add_event("ANTI_DETECTION_SUCCESS", "反检测请求成功")
                else:
                    self.metrics.failed_requests += 1
                    self.add_event("ANTI_DETECTION_FAILED", "反检测请求失败")
                
                # 更新响应时间
                total_requests = self.metrics.total_requests
                self.metrics.average_response_time = (
                    (self.metrics.average_response_time * (total_requests - 1) + response_time) / total_requests
                )
                
                # 随机间隔
                time.sleep(random.uniform(self.config.request_interval, self.config.request_interval * 3))
                
            except Exception as e:
                self.metrics.error_count += 1
                self.metrics.failed_requests += 1
                self.add_event("ANTI_DETECTION_ERROR", f"反检测请求错误: {e}", level="ERROR")
        
        self.add_event("ANTI_DETECTION_END", f"反检测攻击完成，共发送 {request_count} 个请求")
    
    def _execute_queue_bypass_attack(self):
        """执行队列绕过攻击"""
        self.add_event("QUEUE_BYPASS_START", "开始队列绕过攻击")
        
        # 模拟队列绕过策略
        bypass_strategies = ["session_reuse", "cookie_manipulation", "header_spoofing"]
        
        for strategy in bypass_strategies:
            if self._check_stop():
                break
            
            self.add_event("QUEUE_BYPASS_STRATEGY", f"尝试队列绕过策略: {strategy}")
            
            try:
                if strategy == "session_reuse":
                    self._session_reuse_bypass()
                elif strategy == "cookie_manipulation":
                    self._cookie_manipulation_bypass()
                elif strategy == "header_spoofing":
                    self._header_spoofing_bypass()
                
            except Exception as e:
                self.add_event("QUEUE_BYPASS_ERROR", f"队列绕过策略 {strategy} 失败: {e}", level="ERROR")
        
        self.add_event("QUEUE_BYPASS_END", "队列绕过攻击完成")
    
    def _session_reuse_bypass(self):
        """会话重用绕过"""
        session = requests.Session()
        
        for i in range(5):
            if self._check_stop():
                break
            
            try:
                response = session.get(self.config.target_url, timeout=self.config.timeout)
                
                self.metrics.total_requests += 1
                
                if response and response.status_code == 200:
                    self.metrics.successful_requests += 1
                    self.add_event("SESSION_REUSE_SUCCESS", f"会话重用成功 {i+1}")
                else:
                    self.metrics.failed_requests += 1
                
                time.sleep(self.config.request_interval)
                
            except Exception as e:
                self.metrics.error_count += 1
                self.metrics.failed_requests += 1
        
        session.close()
    
    def _cookie_manipulation_bypass(self):
        """Cookie操作绕过"""
        for i in range(3):
            if self._check_stop():
                break
            
            try:
                # 模拟Cookie操作
                cookies = {'session_id': f'fake_session_{random.randint(1000, 9999)}'}
                
                response = self.http_client.get(
                    self.config.target_url,
                    cookies=cookies,
                    timeout=self.config.timeout
                )
                
                self.metrics.total_requests += 1
                
                if response and response.status_code == 200:
                    self.metrics.successful_requests += 1
                    self.add_event("COOKIE_BYPASS_SUCCESS", f"Cookie绕过成功 {i+1}")
                else:
                    self.metrics.failed_requests += 1
                
                time.sleep(self.config.request_interval * 2)
                
            except Exception as e:
                self.metrics.error_count += 1
                self.metrics.failed_requests += 1
    
    def _header_spoofing_bypass(self):
        """请求头伪装绕过"""
        for i in range(3):
            if self._check_stop():
                break
            
            try:
                # 模拟特殊请求头
                headers = {
                    'X-Forwarded-For': f'192.168.1.{random.randint(1, 254)}',
                    'X-Real-IP': f'10.0.0.{random.randint(1, 254)}',
                    'X-Requested-With': 'XMLHttpRequest'
                }
                
                response = self.http_client.get(
                    self.config.target_url,
                    headers=headers,
                    timeout=self.config.timeout
                )
                
                self.metrics.total_requests += 1
                
                if response and response.status_code == 200:
                    self.metrics.successful_requests += 1
                    self.add_event("HEADER_BYPASS_SUCCESS", f"请求头绕过成功 {i+1}")
                else:
                    self.metrics.failed_requests += 1
                
                time.sleep(self.config.request_interval * 2)
                
            except Exception as e:
                self.metrics.error_count += 1
                self.metrics.failed_requests += 1
    
    def _is_blocked_response(self, response) -> bool:
        """检查响应是否表示被阻止"""
        if not response:
            return True
        
        # 检查状态码
        if response.status_code in [403, 429, 503, 502]:
            return True
        
        # 检查响应内容
        content = response.text.lower()
        blocked_keywords = [
            'blocked', 'forbidden', 'rate limit', 'too many requests',
            'captcha', 'queue', 'waiting room', 'access denied'
        ]
        
        for keyword in blocked_keywords:
            if keyword in content:
                return True
        
        return False
# 验证码样本目录

此目录用于存放验证码测试样本，支持验证码识别模块的开发和测试。

## 目录结构

```
captcha_samples/
├── README.md           # 本说明文件
├── text/              # 文字验证码样本
├── math/              # 数学验证码样本
├── image/             # 图像识别验证码样本
├── slider/            # 滑块验证码样本
└── test_captcha.png   # 自动生成的测试验证码
```

## 支持的验证码类型

### 1. 文字验证码 (text/)
- 纯文字验证码
- 字母数字组合
- 大小写混合
- 常见干扰线和噪点

### 2. 数学验证码 (math/)
- 简单算术运算
- 加减乘除
- 括号运算
- 中文数字

### 3. 图像识别验证码 (image/)
- 物体识别
- 文字选择
- 图片分类
- 拼图验证码

### 4. 滑块验证码 (slider/)
- 滑块拼图
- 轨迹验证
- 行为分析

## 文件命名规范

### 文字验证码
- `text_[内容]_[难度].png`
- 例如: `text_ABC123_easy.png`

### 数学验证码
- `math_[表达式]_[答案].png`
- 例如: `math_1plus2_3.png`

### 图像验证码
- `image_[类型]_[描述].png`
- 例如: `image_select_car.png`

## 测试数据格式

每个验证码样本应该配有对应的答案文件：

```json
{
  "filename": "text_ABC123_easy.png",
  "type": "text",
  "answer": "ABC123",
  "difficulty": "easy",
  "description": "简单文字验证码",
  "created_at": "2024-01-01T00:00:00Z"
}
```

## 使用方法

### 1. 添加新样本
1. 将验证码图片放入对应类型目录
2. 创建对应的答案JSON文件
3. 运行测试脚本验证

### 2. 批量测试
```python
from modules.captcha_solver import get_captcha_recognizer

recognizer = get_captcha_recognizer()

# 测试单个文件
result = recognizer.recognize_captcha('data/captcha_samples/text/sample.png')
print(f"识别结果: {result.result}")

# 批量测试
import os
for filename in os.listdir('data/captcha_samples/text/'):
    if filename.endswith('.png'):
        result = recognizer.recognize_captcha(f'data/captcha_samples/text/{filename}')
        print(f"{filename}: {result.result}")
```

### 3. 性能评估
```python
# 获取识别统计
stats = recognizer.get_stats()
print(f"成功率: {stats['success_rate']:.2%}")
print(f"平均处理时间: {stats['average_processing_time']:.3f}s")
```

## 注意事项

1. **图片格式**: 支持 PNG, JPG, JPEG, BMP, TIFF, GIF
2. **图片大小**: 建议不超过 500KB
3. **分辨率**: 建议 50x20 到 300x100 像素
4. **清晰度**: 确保文字清晰可读
5. **版权**: 仅用于测试目的，注意版权问题

## 依赖库

验证码识别需要以下Python库：

```bash
# 基础图像处理
pip install Pillow

# OpenCV (可选，用于高级图像处理)
pip install opencv-python

# OCR引擎
pip install pytesseract

# 数值计算
pip install numpy
```

## Tesseract安装

### Windows
1. 下载 Tesseract: https://github.com/UB-Mannheim/tesseract/wiki
2. 安装到默认路径
3. 添加到系统PATH

### Linux
```bash
sudo apt-get install tesseract-ocr
sudo apt-get install tesseract-ocr-chi-sim  # 中文支持
```

### macOS
```bash
brew install tesseract
```

## 故障排除

### 常见问题

1. **Tesseract not found**
   - 确保Tesseract已正确安装
   - 检查PATH环境变量
   - 在代码中指定tesseract路径

2. **识别率低**
   - 检查图片质量
   - 调整预处理参数
   - 尝试不同的OCR配置

3. **处理速度慢**
   - 启用缓存功能
   - 优化图片大小
   - 使用多线程处理

### 调试模式

启用详细日志：

```python
import logging
logging.basicConfig(level=logging.DEBUG)

recognizer = get_captcha_recognizer()
result = recognizer.recognize_captcha('sample.png')
```

## 贡献指南

欢迎贡献更多验证码样本：

1. Fork项目
2. 添加新的验证码样本
3. 更新对应的答案文件
4. 提交Pull Request

请确保：
- 样本质量良好
- 答案准确无误
- 遵循命名规范
- 包含必要的元数据
"""
反检测技术模块

基于研究报告中的FingerprintSpoofing类实现，提供设备指纹伪造、User-Agent轮换等反检测技术。
"""

import random
import time
import json
import os
from typing import List, Dict, Optional, Any, Tuple
from dataclasses import dataclass
import threading

try:
    from selenium import webdriver
    from selenium.webdriver.chrome.options import Options as ChromeOptions
    from selenium.webdriver.firefox.options import Options as FirefoxOptions
    from selenium.webdriver.common.action_chains import ActionChains
    SELENIUM_AVAILABLE = True
except ImportError:
    SELENIUM_AVAILABLE = False

try:
    from ..core.logger import get_logger
    from ..config.settings import config_manager
    from ..utils.proxy_pool import get_proxy_pool, ProxyInfo
except ImportError:
    import logging
    def get_logger(name):
        return logging.getLogger(name)
    
    class SimpleConfig:
        def get(self, key, default=None):
            return default
    
    config_manager = SimpleConfig()
    
    @dataclass
    class ProxyInfo:
        proxy_url: str
        is_valid: bool = True


@dataclass
class DeviceFingerprint:
    """设备指纹信息"""
    user_agent: str
    screen_resolution: Tuple[int, int]
    viewport_size: Tuple[int, int]
    timezone: str
    language: str
    platform: str
    webgl_vendor: str
    webgl_renderer: str
    canvas_fingerprint: str
    audio_fingerprint: str
    fonts: List[str]
    plugins: List[str]


class UserAgentManager:
    """User-Agent管理器"""
    
    def __init__(self, ua_file: str = None):
        """
        初始化User-Agent管理器
        
        Args:
            ua_file: User-Agent文件路径
        """
        self.logger = get_logger(__name__)
        self.ua_file = ua_file or 'data/user_agents.txt'
        self.user_agents: List[str] = []
        self.current_index = 0
        self.lock = threading.RLock()
        
        # 默认User-Agent池
        self.default_user_agents = [
            # Chrome Windows
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/118.0.0.0 Safari/537.36',
            
            # Chrome macOS
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            
            # Chrome Linux
            'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            
            # Firefox Windows
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:121.0) Gecko/20100101 Firefox/121.0',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:120.0) Gecko/20100101 Firefox/120.0',
            
            # Firefox macOS
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:121.0) Gecko/20100101 Firefox/121.0',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:120.0) Gecko/20100101 Firefox/120.0',
            
            # Safari macOS
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Safari/605.1.15',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.0 Safari/605.1.15',
            
            # Edge Windows
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36 Edg/120.0.0.0',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********'
        ]
        
        self.load_user_agents()
        self.logger.info(f"User-Agent管理器初始化完成，共 {len(self.user_agents)} 个UA")
    
    def load_user_agents(self):
        """从文件加载User-Agent"""
        if os.path.exists(self.ua_file):
            try:
                with open(self.ua_file, 'r', encoding='utf-8') as f:
                    file_uas = [line.strip() for line in f.readlines() if line.strip()]
                self.user_agents = file_uas
                self.logger.info(f"从文件加载了 {len(file_uas)} 个User-Agent")
            except Exception as e:
                self.logger.warning(f"加载User-Agent文件失败: {e}，使用默认UA池")
                self.user_agents = self.default_user_agents.copy()
        else:
            self.user_agents = self.default_user_agents.copy()
            self.logger.info("使用默认User-Agent池")
    
    def get_random_ua(self) -> str:
        """获取随机User-Agent"""
        with self.lock:
            return random.choice(self.user_agents)
    
    def get_next_ua(self) -> str:
        """获取下一个User-Agent（轮询）"""
        with self.lock:
            ua = self.user_agents[self.current_index]
            self.current_index = (self.current_index + 1) % len(self.user_agents)
            return ua
    
    def get_ua_by_browser(self, browser: str) -> str:
        """
        根据浏览器类型获取User-Agent
        
        Args:
            browser: 浏览器类型 (chrome, firefox, safari, edge)
            
        Returns:
            str: User-Agent字符串
        """
        browser = browser.lower()
        filtered_uas = [ua for ua in self.user_agents if browser in ua.lower()]
        
        if filtered_uas:
            return random.choice(filtered_uas)
        else:
            return self.get_random_ua()
    
    def save_user_agents(self, filename: str = None):
        """保存User-Agent到文件"""
        filename = filename or self.ua_file
        
        try:
            os.makedirs(os.path.dirname(filename), exist_ok=True)
            
            with open(filename, 'w', encoding='utf-8') as f:
                for ua in self.user_agents:
                    f.write(f"{ua}\n")
            
            self.logger.info(f"已保存 {len(self.user_agents)} 个User-Agent到 {filename}")
            
        except Exception as e:
            self.logger.error(f"保存User-Agent文件失败: {e}")


class FingerprintSpoofing:
    """
    设备指纹伪造类
    基于研究报告中的FingerprintSpoofing类实现
    """
    
    def __init__(self):
        """初始化设备指纹伪造器"""
        self.logger = get_logger(__name__)
        self.ua_manager = UserAgentManager()
        
        # 屏幕分辨率池
        self.screen_resolutions = [
            (1920, 1080), (1366, 768), (1440, 900), (1536, 864),
            (1280, 720), (1600, 900), (1024, 768), (1280, 1024),
            (1680, 1050), (1920, 1200), (2560, 1440), (3840, 2160)
        ]
        
        # 时区池
        self.timezones = [
            'Asia/Shanghai', 'Asia/Tokyo', 'Asia/Seoul', 'Asia/Hong_Kong',
            'America/New_York', 'America/Los_Angeles', 'Europe/London',
            'Europe/Paris', 'Europe/Berlin', 'Australia/Sydney'
        ]
        
        # 语言池
        self.languages = [
            'zh-CN,zh;q=0.9,en;q=0.8',
            'en-US,en;q=0.9',
            'ja-JP,ja;q=0.9,en;q=0.8',
            'ko-KR,ko;q=0.9,en;q=0.8',
            'zh-TW,zh;q=0.9,en;q=0.8'
        ]
        
        # 平台池
        self.platforms = [
            'Win32', 'MacIntel', 'Linux x86_64', 'Linux i686'
        ]
        
        # WebGL信息池
        self.webgl_vendors = [
            'Google Inc.', 'Mozilla', 'Apple Inc.', 'Microsoft Corporation'
        ]
        
        self.webgl_renderers = [
            'ANGLE (NVIDIA GeForce GTX 1060 Direct3D11 vs_5_0 ps_5_0)',
            'ANGLE (Intel(R) HD Graphics 620 Direct3D11 vs_5_0 ps_5_0)',
            'ANGLE (AMD Radeon RX 580 Direct3D11 vs_5_0 ps_5_0)',
            'Apple GPU',
            'Mesa DRI Intel(R) UHD Graphics 620'
        ]
        
        # 字体池
        self.common_fonts = [
            'Arial', 'Helvetica', 'Times New Roman', 'Courier New',
            'Verdana', 'Georgia', 'Palatino', 'Garamond', 'Bookman',
            'Comic Sans MS', 'Trebuchet MS', 'Arial Black', 'Impact'
        ]
        
        # 插件池
        self.common_plugins = [
            'Chrome PDF Plugin', 'Chrome PDF Viewer', 'Native Client',
            'Widevine Content Decryption Module', 'Flash Player'
        ]
        
        self.logger.info("设备指纹伪造器初始化完成")
    
    def generate_random_fingerprint(self) -> DeviceFingerprint:
        """生成随机设备指纹"""
        screen_res = random.choice(self.screen_resolutions)
        viewport_width = screen_res[0] - random.randint(0, 100)
        viewport_height = screen_res[1] - random.randint(100, 200)
        
        return DeviceFingerprint(
            user_agent=self.ua_manager.get_random_ua(),
            screen_resolution=screen_res,
            viewport_size=(viewport_width, viewport_height),
            timezone=random.choice(self.timezones),
            language=random.choice(self.languages),
            platform=random.choice(self.platforms),
            webgl_vendor=random.choice(self.webgl_vendors),
            webgl_renderer=random.choice(self.webgl_renderers),
            canvas_fingerprint=self._generate_canvas_fingerprint(),
            audio_fingerprint=self._generate_audio_fingerprint(),
            fonts=random.sample(self.common_fonts, random.randint(8, 12)),
            plugins=random.sample(self.common_plugins, random.randint(2, 4))
        )
    
    def _generate_canvas_fingerprint(self) -> str:
        """生成Canvas指纹"""
        # 模拟Canvas指纹生成
        import hashlib
        data = f"{random.random()}{time.time()}"
        return hashlib.md5(data.encode()).hexdigest()[:16]
    
    def _generate_audio_fingerprint(self) -> str:
        """生成音频指纹"""
        # 模拟音频指纹生成
        import hashlib
        data = f"{random.random()}{time.time()}"
        return hashlib.sha1(data.encode()).hexdigest()[:20]
    
    def setup_spoofed_browser(self, browser_type: str = 'chrome', 
                             proxy_info: ProxyInfo = None) -> Optional[Any]:
        """
        设置伪造指纹的浏览器
        
        Args:
            browser_type: 浏览器类型 ('chrome' 或 'firefox')
            proxy_info: 代理信息
            
        Returns:
            WebDriver实例，如果Selenium不可用返回None
        """
        if not SELENIUM_AVAILABLE:
            self.logger.warning("Selenium不可用，无法创建浏览器实例")
            return None
        
        fingerprint = self.generate_random_fingerprint()
        
        try:
            if browser_type.lower() == 'chrome':
                return self._setup_chrome_browser(fingerprint, proxy_info)
            elif browser_type.lower() == 'firefox':
                return self._setup_firefox_browser(fingerprint, proxy_info)
            else:
                self.logger.error(f"不支持的浏览器类型: {browser_type}")
                return None
                
        except Exception as e:
            self.logger.error(f"设置浏览器失败: {e}")
            return None
    
    def _setup_chrome_browser(self, fingerprint: DeviceFingerprint, 
                             proxy_info: ProxyInfo = None) -> Any:
        """设置Chrome浏览器"""
        chrome_options = ChromeOptions()
        
        # 基础反检测设置
        chrome_options.add_argument('--disable-blink-features=AutomationControlled')
        chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
        chrome_options.add_experimental_option('useAutomationExtension', False)
        
        # 设置User-Agent
        chrome_options.add_argument(f'--user-agent={fingerprint.user_agent}')
        
        # 设置窗口大小
        chrome_options.add_argument(f'--window-size={fingerprint.viewport_size[0]},{fingerprint.viewport_size[1]}')
        
        # 设置语言
        chrome_options.add_argument(f'--lang={fingerprint.language.split(",")[0]}')
        
        # 设置代理
        if proxy_info and proxy_info.is_valid:
            chrome_options.add_argument(f'--proxy-server={proxy_info.proxy_url}')
        
        # 其他反检测设置
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--disable-dev-shm-usage')
        chrome_options.add_argument('--disable-gpu')
        chrome_options.add_argument('--disable-extensions')
        chrome_options.add_argument('--disable-plugins')
        chrome_options.add_argument('--disable-images')
        chrome_options.add_argument('--disable-javascript')
        
        # 创建浏览器实例
        driver = webdriver.Chrome(options=chrome_options)
        
        # 执行JavaScript来修改更多指纹信息
        self._inject_fingerprint_scripts(driver, fingerprint)
        
        return driver
    
    def _setup_firefox_browser(self, fingerprint: DeviceFingerprint, 
                              proxy_info: ProxyInfo = None) -> Any:
        """设置Firefox浏览器"""
        firefox_options = FirefoxOptions()
        
        # 设置User-Agent
        firefox_options.set_preference("general.useragent.override", fingerprint.user_agent)
        
        # 设置语言
        firefox_options.set_preference("intl.accept_languages", fingerprint.language)
        
        # 设置时区
        firefox_options.set_preference("privacy.timezone", fingerprint.timezone)
        
        # 设置代理
        if proxy_info and proxy_info.is_valid:
            from urllib.parse import urlparse
            parsed = urlparse(proxy_info.proxy_url)
            firefox_options.set_preference("network.proxy.type", 1)
            firefox_options.set_preference("network.proxy.http", parsed.hostname)
            firefox_options.set_preference("network.proxy.http_port", parsed.port)
            firefox_options.set_preference("network.proxy.ssl", parsed.hostname)
            firefox_options.set_preference("network.proxy.ssl_port", parsed.port)
        
        # 其他设置
        firefox_options.set_preference("dom.webdriver.enabled", False)
        firefox_options.set_preference("useAutomationExtension", False)
        
        # 创建浏览器实例
        driver = webdriver.Firefox(options=firefox_options)
        
        # 设置窗口大小
        driver.set_window_size(fingerprint.viewport_size[0], fingerprint.viewport_size[1])
        
        # 执行JavaScript来修改更多指纹信息
        self._inject_fingerprint_scripts(driver, fingerprint)
        
        return driver
    
    def _inject_fingerprint_scripts(self, driver: Any, fingerprint: DeviceFingerprint):
        """注入指纹伪造脚本"""
        try:
            # 基础反检测脚本
            driver.execute_script("""
                Object.defineProperty(navigator, 'webdriver', {
                    get: () => undefined,
                });
            """)
            
            # 修改语言设置
            driver.execute_script(f"""
                Object.defineProperty(navigator, 'languages', {{
                    get: () => {json.dumps(fingerprint.language.split(','))},
                }});
            """)
            
            # 修改平台信息
            driver.execute_script(f"""
                Object.defineProperty(navigator, 'platform', {{
                    get: () => '{fingerprint.platform}',
                }});
            """)
            
            # 修改屏幕信息
            driver.execute_script(f"""
                Object.defineProperty(screen, 'width', {{
                    get: () => {fingerprint.screen_resolution[0]},
                }});
                Object.defineProperty(screen, 'height', {{
                    get: () => {fingerprint.screen_resolution[1]},
                }});
            """)
            
            # 修改WebGL信息
            driver.execute_script(f"""
                const getParameter = WebGLRenderingContext.prototype.getParameter;
                WebGLRenderingContext.prototype.getParameter = function(parameter) {{
                    if (parameter === 37445) {{
                        return '{fingerprint.webgl_vendor}';
                    }}
                    if (parameter === 37446) {{
                        return '{fingerprint.webgl_renderer}';
                    }}
                    return getParameter.call(this, parameter);
                }};
            """)
            
            self.logger.debug("指纹伪造脚本注入完成")
            
        except Exception as e:
            self.logger.warning(f"注入指纹伪造脚本失败: {e}")


class HumanBehaviorSimulator:
    """
    人类行为模拟器
    基于研究报告中的HumanBehaviorSimulator类实现
    """
    
    def __init__(self, driver: Any = None):
        """
        初始化人类行为模拟器
        
        Args:
            driver: WebDriver实例
        """
        self.driver = driver
        self.logger = get_logger(__name__)
        
        if driver and SELENIUM_AVAILABLE:
            self.actions = ActionChains(driver)
        else:
            self.actions = None
    
    def human_like_click(self, element: Any):
        """
        模拟人类点击行为
        
        Args:
            element: 要点击的元素
        """
        if not self.actions:
            self.logger.warning("ActionChains不可用，无法模拟点击")
            return
        
        try:
            # 模拟鼠标移动轨迹
            self.simulate_mouse_movement(element)
            
            # 随机停顿
            time.sleep(random.uniform(0.1, 0.3))
            
            # 执行点击
            element.click()
            
            # 点击后停顿
            time.sleep(random.uniform(0.5, 1.5))
            
            self.logger.debug("模拟人类点击完成")
            
        except Exception as e:
            self.logger.error(f"模拟点击失败: {e}")
    
    def simulate_mouse_movement(self, target_element: Any):
        """
        模拟鼠标移动轨迹
        
        Args:
            target_element: 目标元素
        """
        if not self.actions:
            return
        
        try:
            # 获取元素位置
            location = target_element.location
            size = target_element.size
            
            # 计算目标点
            target_x = location['x'] + size['width'] // 2
            target_y = location['y'] + size['height'] // 2
            
            # 生成贝塞尔曲线路径
            steps = random.randint(5, 10)
            for i in range(steps):
                # 添加随机偏移
                offset_x = random.randint(-5, 5)
                offset_y = random.randint(-5, 5)
                
                # 移动鼠标
                self.actions.move_by_offset(offset_x, offset_y)
                time.sleep(random.uniform(0.01, 0.05))
            
            # 最终移动到目标
            self.actions.move_to_element(target_element)
            self.actions.perform()
            
        except Exception as e:
            self.logger.error(f"模拟鼠标移动失败: {e}")
    
    def human_like_typing(self, element: Any, text: str):
        """
        模拟人类输入行为
        
        Args:
            element: 输入元素
            text: 要输入的文本
        """
        try:
            element.clear()
            
            for char in text:
                element.send_keys(char)
                # 模拟打字间隔
                time.sleep(random.uniform(0.05, 0.2))
            
            # 输入完成后停顿
            time.sleep(random.uniform(0.5, 1.0))
            
            self.logger.debug(f"模拟人类输入完成: {text}")
            
        except Exception as e:
            self.logger.error(f"模拟输入失败: {e}")
    
    def random_scroll(self):
        """随机滚动页面"""
        if not self.driver:
            return
        
        try:
            # 随机滚动方向和距离
            scroll_direction = random.choice(['up', 'down'])
            scroll_distance = random.randint(100, 500)
            
            if scroll_direction == 'down':
                self.driver.execute_script(f"window.scrollBy(0, {scroll_distance});")
            else:
                self.driver.execute_script(f"window.scrollBy(0, -{scroll_distance});")
            
            # 滚动后停顿
            time.sleep(random.uniform(0.5, 2.0))
            
        except Exception as e:
            self.logger.error(f"随机滚动失败: {e}")
    
    def random_page_interaction(self):
        """随机页面交互"""
        if not self.driver:
            return
        
        try:
            # 随机选择交互类型
            interaction_type = random.choice(['scroll', 'move_mouse', 'wait'])
            
            if interaction_type == 'scroll':
                self.random_scroll()
            elif interaction_type == 'move_mouse':
                # 随机移动鼠标
                if self.actions:
                    self.actions.move_by_offset(
                        random.randint(-50, 50),
                        random.randint(-50, 50)
                    ).perform()
            elif interaction_type == 'wait':
                # 随机等待
                time.sleep(random.uniform(1.0, 3.0))
            
        except Exception as e:
            self.logger.error(f"随机页面交互失败: {e}")


# 全局实例
ua_manager: Optional[UserAgentManager] = None
fingerprint_spoofer: Optional[FingerprintSpoofing] = None


def get_ua_manager() -> UserAgentManager:
    """获取全局User-Agent管理器实例"""
    global ua_manager
    if ua_manager is None:
        ua_manager = UserAgentManager()
    return ua_manager


def get_fingerprint_spoofer() -> FingerprintSpoofing:
    """获取全局设备指纹伪造器实例"""
    global fingerprint_spoofer
    if fingerprint_spoofer is None:
        fingerprint_spoofer = FingerprintSpoofing()
    return fingerprint_spoofer
#!/usr/bin/env python3
"""
核心系统测试脚本

测试配置管理器、日志系统和主引擎的基本功能。
"""

import sys
import time
from pathlib import Path

# 添加项目路径
sys.path.insert(0, str(Path(__file__).parent))

from config.settings import config_manager
from core.logger import initialize_logger, get_logger
from core.engine import initialize_engine, get_engine


def test_config_manager():
    """测试配置管理器"""
    print("=== 测试配置管理器 ===")
    
    # 测试配置加载
    system_name = config_manager.get('system.name')
    print(f"系统名称: {system_name}")
    
    # 测试配置验证
    is_valid = config_manager.validate_config()
    print(f"配置验证: {'通过' if is_valid else '失败'}")
    
    # 测试嵌套配置获取
    log_level = config_manager.get('logging.level')
    print(f"日志级别: {log_level}")
    
    # 测试默认值
    non_existent = config_manager.get('non.existent.key', 'default_value')
    print(f"不存在的配置项: {non_existent}")
    
    # 测试配置设置
    config_manager.set('test.key', 'test_value')
    test_value = config_manager.get('test.key')
    print(f"测试配置设置: {test_value}")
    
    print("配置管理器测试完成\n")


def test_logger_system():
    """测试日志系统"""
    print("=== 测试日志系统 ===")
    
    # 初始化日志系统
    log_config = config_manager.get('logging', {})
    initialize_logger(log_config)
    
    # 获取日志记录器
    logger = get_logger('test')
    
    # 测试不同级别的日志
    logger.debug("这是一条调试日志")
    logger.info("这是一条信息日志")
    logger.warning("这是一条警告日志")
    logger.error("这是一条错误日志")
    
    print("日志系统测试完成\n")


def test_engine():
    """测试主引擎"""
    print("=== 测试主引擎 ===")
    
    # 初始化引擎
    success = initialize_engine()
    print(f"引擎初始化: {'成功' if success else '失败'}")
    
    if not success:
        return
    
    # 获取引擎实例
    engine = get_engine()
    
    # 测试任务提交
    def test_task(task_name: str, duration: float = 1.0):
        """测试任务函数"""
        print(f"执行任务: {task_name}")
        time.sleep(duration)
        return f"任务 {task_name} 完成"
    
    # 提交几个测试任务
    task_ids = []
    for i in range(3):
        task_id = engine.submit_task(
            test_task, 
            f"测试任务_{i+1}", 
            0.5,
            description=f"这是第{i+1}个测试任务"
        )
        task_ids.append(task_id)
        print(f"提交任务: {task_id}")
    
    # 等待任务完成
    print("等待任务完成...")
    time.sleep(2)
    
    # 检查任务状态
    for task_id in task_ids:
        status = engine.get_task_status(task_id)
        if status:
            print(f"任务 {task_id}: {status['status']}")
    
    # 获取性能指标
    metrics = engine.get_performance_metrics()
    print(f"性能指标: {metrics}")
    
    # 关闭引擎
    engine.shutdown()
    print("引擎已关闭")
    
    print("主引擎测试完成\n")


def main():
    """主测试函数"""
    print("开始核心系统测试...\n")
    
    try:
        # 测试配置管理器
        test_config_manager()
        
        # 测试日志系统
        test_logger_system()
        
        # 测试主引擎
        test_engine()
        
        print("=== 所有测试完成 ===")
        print("核心系统功能正常！")
        
    except Exception as e:
        print(f"测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
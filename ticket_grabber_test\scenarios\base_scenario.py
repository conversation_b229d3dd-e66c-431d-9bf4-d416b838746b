"""
基础测试场景抽象类

定义测试场景的基础接口和通用功能，为各种攻击场景提供统一的框架。
"""

import time
import json
import asyncio
import threading
from abc import ABC, abstractmethod
from typing import Dict, List, Any, Optional, Callable, Union
from dataclasses import dataclass, field
from enum import Enum
import uuid
from datetime import datetime

try:
    from ..core.logger import get_logger
    from ..config.settings import config_manager
    from ..modules.http_client import TicketGrabber, AsyncTicketGrabber
    from ..modules.anti_detection import UserAgentManager, FingerprintSpoofing
    from ..modules.behavior_sim import HumanBehaviorSimulator, BehaviorPattern
    from ..modules.captcha_solver import CaptchaRecognizer
    from ..utils.proxy_pool import ProxyPool
except ImportError:
    import logging
    def get_logger(name):
        return logging.getLogger(name)
    
    class SimpleConfig:
        def get(self, key, default=None):
            return default
    
    config_manager = SimpleConfig()


class ScenarioStatus(Enum):
    """场景状态枚举"""
    PENDING = "pending"         # 等待执行
    RUNNING = "running"         # 正在执行
    PAUSED = "paused"          # 已暂停
    COMPLETED = "completed"     # 已完成
    FAILED = "failed"          # 执行失败
    CANCELLED = "cancelled"     # 已取消


class AttackType(Enum):
    """攻击类型枚举"""
    BASIC = "basic"                    # 基础攻击
    ADVANCED = "advanced"              # 高级攻击
    DISTRIBUTED = "distributed"       # 分布式攻击
    BEHAVIORAL = "behavioral"          # 行为模拟攻击
    ADVERSARIAL = "adversarial"        # 对抗性攻击
    HYBRID = "hybrid"                  # 混合攻击


class ScenarioResult(Enum):
    """场景结果枚举"""
    SUCCESS = "success"         # 成功
    PARTIAL = "partial"         # 部分成功
    BLOCKED = "blocked"         # 被阻止
    ERROR = "error"            # 错误
    TIMEOUT = "timeout"        # 超时


@dataclass
class ScenarioConfig:
    """场景配置数据类"""
    name: str
    description: str
    attack_type: AttackType
    target_url: str
    
    # 执行参数
    max_requests: int = 100
    concurrent_users: int = 10
    request_interval: float = 1.0
    timeout: float = 30.0
    
    # 高级配置
    use_proxy: bool = False
    use_captcha_solver: bool = False
    use_behavior_sim: bool = False
    use_anti_detection: bool = True
    
    # 自定义参数
    custom_params: Dict[str, Any] = field(default_factory=dict)
    
    # 验证配置
    success_criteria: Dict[str, Any] = field(default_factory=dict)
    failure_criteria: Dict[str, Any] = field(default_factory=dict)


@dataclass
class ScenarioMetrics:
    """场景执行指标"""
    scenario_id: str
    start_time: float
    end_time: Optional[float] = None
    
    # 请求统计
    total_requests: int = 0
    successful_requests: int = 0
    failed_requests: int = 0
    blocked_requests: int = 0
    
    # 性能指标
    average_response_time: float = 0.0
    min_response_time: float = float('inf')
    max_response_time: float = 0.0
    
    # 错误统计
    error_count: int = 0
    timeout_count: int = 0
    captcha_count: int = 0
    
    # 自定义指标
    custom_metrics: Dict[str, Any] = field(default_factory=dict)
    
    @property
    def success_rate(self) -> float:
        """成功率"""
        if self.total_requests == 0:
            return 0.0
        return self.successful_requests / self.total_requests
    
    @property
    def duration(self) -> float:
        """执行时长"""
        if self.end_time is None:
            return time.time() - self.start_time
        return self.end_time - self.start_time
    
    @property
    def requests_per_second(self) -> float:
        """每秒请求数"""
        duration = self.duration
        if duration == 0:
            return 0.0
        return self.total_requests / duration


@dataclass
class ScenarioEvent:
    """场景事件数据"""
    timestamp: float
    event_type: str
    message: str
    data: Optional[Dict[str, Any]] = None
    level: str = "INFO"  # DEBUG, INFO, WARNING, ERROR


class BaseScenario(ABC):
    """
    基础测试场景抽象类
    
    定义所有测试场景必须实现的接口和提供通用功能。
    """
    
    def __init__(self, config: ScenarioConfig):
        """
        初始化基础场景
        
        Args:
            config: 场景配置
        """
        self.config = config
        self.scenario_id = str(uuid.uuid4())
        self.logger = get_logger(f"{self.__class__.__name__}_{self.scenario_id[:8]}")
        
        # 状态管理
        self.status = ScenarioStatus.PENDING
        self.result = None
        self.error_message = None
        
        # 执行控制
        self._stop_event = threading.Event()
        self._pause_event = threading.Event()
        self._execution_thread = None
        
        # 指标和事件
        self.metrics = ScenarioMetrics(
            scenario_id=self.scenario_id,
            start_time=time.time()
        )
        self.events: List[ScenarioEvent] = []
        
        # 回调函数
        self.on_start: Optional[Callable] = None
        self.on_complete: Optional[Callable] = None
        self.on_error: Optional[Callable] = None
        self.on_progress: Optional[Callable] = None
        
        # 组件初始化
        self._init_components()
        
        self.logger.info(f"场景初始化完成: {config.name}")
    
    def _init_components(self):
        """初始化组件"""
        try:
            # HTTP客户端
            self.http_client = None
            self.async_http_client = None
            
            # 代理池
            self.proxy_pool = None
            if self.config.use_proxy:
                try:
                    self.proxy_pool = ProxyPool()
                except:
                    self.logger.warning("代理池初始化失败")
            
            # 验证码识别器
            self.captcha_solver = None
            if self.config.use_captcha_solver:
                try:
                    self.captcha_solver = CaptchaRecognizer()
                except:
                    self.logger.warning("验证码识别器初始化失败")
            
            # 反检测组件
            self.ua_manager = None
            self.fingerprint_spoofing = None
            if self.config.use_anti_detection:
                try:
                    self.ua_manager = UserAgentManager()
                    self.fingerprint_spoofing = FingerprintSpoofing()
                except:
                    self.logger.warning("反检测组件初始化失败")
            
            # 行为模拟器
            self.behavior_simulator = None
            if self.config.use_behavior_sim:
                try:
                    pattern = BehaviorPattern.NORMAL
                    self.behavior_simulator = HumanBehaviorSimulator(pattern=pattern)
                except:
                    self.logger.warning("行为模拟器初始化失败")
            
        except Exception as e:
            self.logger.error(f"组件初始化失败: {e}")
    
    def add_event(self, event_type: str, message: str, data: Dict[str, Any] = None, level: str = "INFO"):
        """添加事件记录"""
        event = ScenarioEvent(
            timestamp=time.time(),
            event_type=event_type,
            message=message,
            data=data,
            level=level
        )
        self.events.append(event)
        
        # 记录到日志
        log_method = getattr(self.logger, level.lower(), self.logger.info)
        log_method(f"[{event_type}] {message}")
    
    def update_metrics(self, **kwargs):
        """更新指标"""
        for key, value in kwargs.items():
            if hasattr(self.metrics, key):
                setattr(self.metrics, key, value)
            else:
                self.metrics.custom_metrics[key] = value
    
    def execute(self, blocking: bool = True) -> bool:
        """
        执行场景
        
        Args:
            blocking: 是否阻塞执行
            
        Returns:
            bool: 是否成功启动
        """
        if self.status != ScenarioStatus.PENDING:
            self.logger.error(f"场景状态错误: {self.status}")
            return False
        
        try:
            self.status = ScenarioStatus.RUNNING
            self.metrics.start_time = time.time()
            self.add_event("EXECUTION_START", f"开始执行场景: {self.config.name}")
            
            if self.on_start:
                self.on_start(self)
            
            if blocking:
                # 同步执行
                self._execute_scenario()
            else:
                # 异步执行
                self._execution_thread = threading.Thread(target=self._execute_scenario)
                self._execution_thread.start()
            
            return True
            
        except Exception as e:
            self.logger.error(f"场景执行启动失败: {e}")
            self.status = ScenarioStatus.FAILED
            self.error_message = str(e)
            self.add_event("EXECUTION_ERROR", f"执行启动失败: {e}", level="ERROR")
            return False
    
    def _execute_scenario(self):
        """内部执行方法"""
        try:
            # 执行具体场景逻辑
            self.run_scenario()
            
            # 检查结果
            if self._stop_event.is_set():
                self.status = ScenarioStatus.CANCELLED
                self.result = ScenarioResult.ERROR
                self.add_event("EXECUTION_CANCELLED", "场景执行被取消")
            else:
                self.status = ScenarioStatus.COMPLETED
                self.result = self._evaluate_result()
                self.add_event("EXECUTION_COMPLETE", f"场景执行完成: {self.result.value}")
            
        except Exception as e:
            self.logger.error(f"场景执行失败: {e}")
            self.status = ScenarioStatus.FAILED
            self.result = ScenarioResult.ERROR
            self.error_message = str(e)
            self.add_event("EXECUTION_ERROR", f"执行失败: {e}", level="ERROR")
        
        finally:
            self.metrics.end_time = time.time()
            
            # 调用完成回调
            if self.on_complete:
                self.on_complete(self)
            
            # 调用错误回调
            if self.status == ScenarioStatus.FAILED and self.on_error:
                self.on_error(self, self.error_message)
    
    @abstractmethod
    def run_scenario(self):
        """
        执行具体的场景逻辑
        
        子类必须实现此方法
        """
        pass
    
    def _evaluate_result(self) -> ScenarioResult:
        """评估执行结果"""
        try:
            # 检查成功标准
            success_criteria = self.config.success_criteria
            if success_criteria:
                if not self._check_criteria(success_criteria):
                    return ScenarioResult.BLOCKED
            
            # 检查失败标准
            failure_criteria = self.config.failure_criteria
            if failure_criteria:
                if self._check_criteria(failure_criteria):
                    return ScenarioResult.BLOCKED
            
            # 基于成功率判断
            if self.metrics.success_rate >= 0.8:
                return ScenarioResult.SUCCESS
            elif self.metrics.success_rate >= 0.3:
                return ScenarioResult.PARTIAL
            else:
                return ScenarioResult.BLOCKED
                
        except Exception as e:
            self.logger.error(f"结果评估失败: {e}")
            return ScenarioResult.ERROR
    
    def _check_criteria(self, criteria: Dict[str, Any]) -> bool:
        """检查标准是否满足"""
        try:
            for key, expected_value in criteria.items():
                if key == "min_success_rate":
                    if self.metrics.success_rate < expected_value:
                        return False
                elif key == "max_error_rate":
                    error_rate = self.metrics.error_count / max(self.metrics.total_requests, 1)
                    if error_rate > expected_value:
                        return False
                elif key == "min_requests":
                    if self.metrics.total_requests < expected_value:
                        return False
                # 可以添加更多标准
            
            return True
            
        except Exception as e:
            self.logger.error(f"标准检查失败: {e}")
            return False
    
    def pause(self):
        """暂停执行"""
        if self.status == ScenarioStatus.RUNNING:
            self._pause_event.set()
            self.status = ScenarioStatus.PAUSED
            self.add_event("EXECUTION_PAUSED", "场景执行已暂停")
            self.logger.info("场景执行已暂停")
    
    def resume(self):
        """恢复执行"""
        if self.status == ScenarioStatus.PAUSED:
            self._pause_event.clear()
            self.status = ScenarioStatus.RUNNING
            self.add_event("EXECUTION_RESUMED", "场景执行已恢复")
            self.logger.info("场景执行已恢复")
    
    def stop(self):
        """停止执行"""
        if self.status in [ScenarioStatus.RUNNING, ScenarioStatus.PAUSED]:
            self._stop_event.set()
            self._pause_event.clear()
            self.add_event("EXECUTION_STOP", "场景执行停止请求")
            self.logger.info("场景执行停止请求")
    
    def wait_for_completion(self, timeout: float = None) -> bool:
        """
        等待执行完成
        
        Args:
            timeout: 超时时间（秒）
            
        Returns:
            bool: 是否在超时前完成
        """
        if self._execution_thread and self._execution_thread.is_alive():
            self._execution_thread.join(timeout)
            return not self._execution_thread.is_alive()
        return True
    
    def get_progress(self) -> Dict[str, Any]:
        """获取执行进度"""
        progress = {
            'scenario_id': self.scenario_id,
            'name': self.config.name,
            'status': self.status.value,
            'result': self.result.value if self.result else None,
            'progress_percent': 0.0,
            'metrics': {
                'total_requests': self.metrics.total_requests,
                'successful_requests': self.metrics.successful_requests,
                'failed_requests': self.metrics.failed_requests,
                'success_rate': self.metrics.success_rate,
                'duration': self.metrics.duration,
                'requests_per_second': self.metrics.requests_per_second
            }
        }
        
        # 计算进度百分比
        if self.config.max_requests > 0:
            progress['progress_percent'] = min(
                (self.metrics.total_requests / self.config.max_requests) * 100,
                100.0
            )
        
        return progress
    
    def get_summary(self) -> Dict[str, Any]:
        """获取执行摘要"""
        return {
            'scenario_id': self.scenario_id,
            'config': {
                'name': self.config.name,
                'description': self.config.description,
                'attack_type': self.config.attack_type.value,
                'target_url': self.config.target_url,
                'max_requests': self.config.max_requests,
                'concurrent_users': self.config.concurrent_users
            },
            'status': self.status.value,
            'result': self.result.value if self.result else None,
            'error_message': self.error_message,
            'metrics': {
                'duration': self.metrics.duration,
                'total_requests': self.metrics.total_requests,
                'successful_requests': self.metrics.successful_requests,
                'failed_requests': self.metrics.failed_requests,
                'blocked_requests': self.metrics.blocked_requests,
                'success_rate': self.metrics.success_rate,
                'average_response_time': self.metrics.average_response_time,
                'requests_per_second': self.metrics.requests_per_second,
                'error_count': self.metrics.error_count,
                'timeout_count': self.metrics.timeout_count,
                'captcha_count': self.metrics.captcha_count
            },
            'events_count': len(self.events),
            'start_time': datetime.fromtimestamp(self.metrics.start_time).isoformat(),
            'end_time': datetime.fromtimestamp(self.metrics.end_time).isoformat() if self.metrics.end_time else None
        }
    
    def export_results(self, filepath: str):
        """导出执行结果"""
        try:
            results = {
                'summary': self.get_summary(),
                'events': [
                    {
                        'timestamp': datetime.fromtimestamp(event.timestamp).isoformat(),
                        'type': event.event_type,
                        'message': event.message,
                        'data': event.data,
                        'level': event.level
                    }
                    for event in self.events
                ],
                'metrics': {
                    'scenario_id': self.metrics.scenario_id,
                    'start_time': self.metrics.start_time,
                    'end_time': self.metrics.end_time,
                    'total_requests': self.metrics.total_requests,
                    'successful_requests': self.metrics.successful_requests,
                    'failed_requests': self.metrics.failed_requests,
                    'blocked_requests': self.metrics.blocked_requests,
                    'average_response_time': self.metrics.average_response_time,
                    'min_response_time': self.metrics.min_response_time,
                    'max_response_time': self.metrics.max_response_time,
                    'error_count': self.metrics.error_count,
                    'timeout_count': self.metrics.timeout_count,
                    'captcha_count': self.metrics.captcha_count,
                    'custom_metrics': self.metrics.custom_metrics
                }
            }
            
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(results, f, indent=2, ensure_ascii=False)
            
            self.logger.info(f"执行结果已导出到: {filepath}")
            
        except Exception as e:
            self.logger.error(f"结果导出失败: {e}")
    
    def _check_pause(self):
        """检查是否需要暂停"""
        if self._pause_event.is_set():
            self.logger.info("场景执行已暂停，等待恢复...")
            while self._pause_event.is_set() and not self._stop_event.is_set():
                time.sleep(0.1)
    
    def _check_stop(self) -> bool:
        """检查是否需要停止"""
        return self._stop_event.is_set()
    
    def __str__(self) -> str:
        return f"Scenario({self.config.name}, {self.status.value})"
    
    def __repr__(self) -> str:
        return f"<{self.__class__.__name__}(id={self.scenario_id[:8]}, name='{self.config.name}', status={self.status.value})>"
"""
配置向导模块

提供交互式配置设置向导，帮助用户快速配置系统参数
"""

import json
import os
from typing import Dict, Any, List, Optional, Union
from pathlib import Path

try:
    from ..core.logger import get_logger
except ImportError:
    import logging
    def get_logger(name):
        return logging.getLogger(name)


class ConfigWizard:
    """
    配置向导类
    
    提供交互式配置设置功能
    """
    
    def __init__(self):
        """初始化配置向导"""
        self.logger = get_logger("ConfigWizard")
        self.config_data = {}
        
        # 配置模板
        self.config_templates = {
            "basic": {
                "name": "基础配置",
                "description": "适合初次使用的基本配置",
                "config": {
                    "target": {
                        "url": "http://localhost:8080",
                        "timeout": 30,
                        "retry_count": 3
                    },
                    "execution": {
                        "concurrent_users": 10,
                        "total_requests": 100,
                        "request_interval": 1.0
                    },
                    "logging": {
                        "level": "INFO",
                        "file_enabled": True,
                        "console_enabled": True
                    }
                }
            },
            "advanced": {
                "name": "高级配置",
                "description": "适合有经验用户的详细配置",
                "config": {
                    "target": {
                        "url": "http://localhost:8080",
                        "timeout": 60,
                        "retry_count": 5,
                        "headers": {
                            "User-Agent": "TicketGrabberTest/1.0"
                        }
                    },
                    "execution": {
                        "concurrent_users": 50,
                        "total_requests": 1000,
                        "request_interval": 0.5,
                        "ramp_up_time": 30,
                        "think_time": 2.0
                    },
                    "proxy": {
                        "enabled": False,
                        "pool_size": 10,
                        "rotation_interval": 60
                    },
                    "captcha": {
                        "enabled": True,
                        "solver_type": "ocr",
                        "timeout": 30
                    },
                    "anti_detection": {
                        "user_agent_rotation": True,
                        "fingerprint_spoofing": True,
                        "behavior_simulation": True
                    },
                    "logging": {
                        "level": "DEBUG",
                        "file_enabled": True,
                        "console_enabled": True,
                        "max_file_size": "10MB",
                        "backup_count": 5
                    }
                }
            },
            "performance": {
                "name": "性能测试配置",
                "description": "适合高并发性能测试的配置",
                "config": {
                    "target": {
                        "url": "http://localhost:8080",
                        "timeout": 30,
                        "retry_count": 2
                    },
                    "execution": {
                        "concurrent_users": 200,
                        "total_requests": 10000,
                        "request_interval": 0.1,
                        "ramp_up_time": 60
                    },
                    "performance": {
                        "connection_pool_size": 100,
                        "keep_alive": True,
                        "tcp_nodelay": True
                    },
                    "monitoring": {
                        "enabled": True,
                        "interval": 5,
                        "metrics": ["cpu", "memory", "network"]
                    },
                    "logging": {
                        "level": "WARNING",
                        "file_enabled": True,
                        "console_enabled": False
                    }
                }
            }
        }
    
    def run_wizard(self) -> Dict[str, Any]:
        """
        运行配置向导
        
        Returns:
            Dict[str, Any]: 配置数据
        """
        self.logger.info("启动配置向导")
        
        print("\n" + "="*80)
        print("🧙‍♂️ 配置向导 - 帮助您快速设置系统参数")
        print("="*80)
        
        try:
            # 选择配置模式
            config_mode = self._select_config_mode()
            
            if config_mode == "template":
                # 使用模板配置
                self.config_data = self._select_template()
            elif config_mode == "custom":
                # 自定义配置
                self.config_data = self._create_custom_config()
            elif config_mode == "import":
                # 导入现有配置
                self.config_data = self._import_existing_config()
            else:
                print("❌ 无效的配置模式")
                return {}
            
            # 确认配置
            if self._confirm_config():
                # 保存配置
                config_file = self._save_config()
                print(f"✅ 配置已保存到: {config_file}")
                return self.config_data
            else:
                print("❌ 配置向导已取消")
                return {}
                
        except KeyboardInterrupt:
            print("\n❌ 配置向导被用户中断")
            return {}
        except Exception as e:
            self.logger.error(f"配置向导执行失败: {e}")
            print(f"❌ 配置向导执行失败: {e}")
            return {}
    
    def _select_config_mode(self) -> str:
        """
        选择配置模式
        
        Returns:
            str: 配置模式
        """
        print("\n📋 请选择配置模式:")
        print("1. 使用预设模板 (推荐)")
        print("2. 自定义配置")
        print("3. 导入现有配置")
        print("0. 取消")
        
        while True:
            choice = input("\n请选择 (1-3): ").strip()
            
            if choice == "1":
                return "template"
            elif choice == "2":
                return "custom"
            elif choice == "3":
                return "import"
            elif choice == "0":
                return "cancel"
            else:
                print("❌ 无效选择，请重新输入")
    
    def _select_template(self) -> Dict[str, Any]:
        """
        选择配置模板
        
        Returns:
            Dict[str, Any]: 模板配置
        """
        print("\n📋 可用配置模板:")
        
        templates = list(self.config_templates.keys())
        for i, template_key in enumerate(templates, 1):
            template = self.config_templates[template_key]
            print(f"{i}. {template['name']}")
            print(f"   {template['description']}")
        
        while True:
            try:
                choice = int(input(f"\n请选择模板 (1-{len(templates)}): ")) - 1
                if 0 <= choice < len(templates):
                    template_key = templates[choice]
                    selected_template = self.config_templates[template_key]
                    
                    print(f"\n✅ 已选择模板: {selected_template['name']}")
                    
                    # 复制模板配置
                    config = selected_template['config'].copy()
                    
                    # 允许用户修改关键参数
                    config = self._customize_template(config)
                    
                    return config
                else:
                    print("❌ 无效选择")
            except ValueError:
                print("❌ 请输入有效数字")
    
    def _customize_template(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """
        自定义模板配置
        
        Args:
            config: 模板配置
            
        Returns:
            Dict[str, Any]: 自定义后的配置
        """
        print("\n🔧 自定义关键参数 (直接回车保持默认值):")
        
        # 目标URL
        current_url = config.get('target', {}).get('url', '')
        new_url = input(f"目标URL [{current_url}]: ").strip()
        if new_url:
            if 'target' not in config:
                config['target'] = {}
            config['target']['url'] = new_url
        
        # 并发用户数
        current_users = config.get('execution', {}).get('concurrent_users', 10)
        new_users = input(f"并发用户数 [{current_users}]: ").strip()
        if new_users:
            try:
                users = int(new_users)
                if users > 0:
                    if 'execution' not in config:
                        config['execution'] = {}
                    config['execution']['concurrent_users'] = users
                else:
                    print("⚠️  并发用户数必须大于0，保持默认值")
            except ValueError:
                print("⚠️  无效数字，保持默认值")
        
        # 总请求数
        current_requests = config.get('execution', {}).get('total_requests', 100)
        new_requests = input(f"总请求数 [{current_requests}]: ").strip()
        if new_requests:
            try:
                requests = int(new_requests)
                if requests > 0:
                    if 'execution' not in config:
                        config['execution'] = {}
                    config['execution']['total_requests'] = requests
                else:
                    print("⚠️  总请求数必须大于0，保持默认值")
            except ValueError:
                print("⚠️  无效数字，保持默认值")
        
        # 请求超时时间
        current_timeout = config.get('target', {}).get('timeout', 30)
        new_timeout = input(f"请求超时时间/秒 [{current_timeout}]: ").strip()
        if new_timeout:
            try:
                timeout = int(new_timeout)
                if timeout > 0:
                    if 'target' not in config:
                        config['target'] = {}
                    config['target']['timeout'] = timeout
                else:
                    print("⚠️  超时时间必须大于0，保持默认值")
            except ValueError:
                print("⚠️  无效数字，保持默认值")
        
        return config
    
    def _create_custom_config(self) -> Dict[str, Any]:
        """
        创建自定义配置
        
        Returns:
            Dict[str, Any]: 自定义配置
        """
        print("\n🔧 创建自定义配置")
        print("请按提示输入各项配置参数:")
        
        config = {}
        
        # 目标配置
        print("\n📍 目标配置:")
        target_url = input("目标URL: ").strip()
        if not target_url:
            print("❌ 目标URL不能为空")
            return {}
        
        timeout = self._get_int_input("请求超时时间/秒", 30, 1, 300)
        retry_count = self._get_int_input("重试次数", 3, 0, 10)
        
        config['target'] = {
            'url': target_url,
            'timeout': timeout,
            'retry_count': retry_count
        }
        
        # 执行配置
        print("\n⚡ 执行配置:")
        concurrent_users = self._get_int_input("并发用户数", 10, 1, 1000)
        total_requests = self._get_int_input("总请求数", 100, 1, 100000)
        request_interval = self._get_float_input("请求间隔/秒", 1.0, 0.1, 60.0)
        
        config['execution'] = {
            'concurrent_users': concurrent_users,
            'total_requests': total_requests,
            'request_interval': request_interval
        }
        
        # 高级功能配置
        if self._confirm_action("是否配置高级功能"):
            config.update(self._configure_advanced_features())
        
        # 日志配置
        print("\n📝 日志配置:")
        log_level = self._select_log_level()
        file_enabled = self._confirm_action("启用文件日志")
        console_enabled = self._confirm_action("启用控制台日志")
        
        config['logging'] = {
            'level': log_level,
            'file_enabled': file_enabled,
            'console_enabled': console_enabled
        }
        
        return config
    
    def _configure_advanced_features(self) -> Dict[str, Any]:
        """
        配置高级功能
        
        Returns:
            Dict[str, Any]: 高级功能配置
        """
        advanced_config = {}
        
        # 代理配置
        if self._confirm_action("启用代理池"):
            print("\n🌐 代理配置:")
            pool_size = self._get_int_input("代理池大小", 10, 1, 100)
            rotation_interval = self._get_int_input("代理轮换间隔/秒", 60, 10, 3600)
            
            advanced_config['proxy'] = {
                'enabled': True,
                'pool_size': pool_size,
                'rotation_interval': rotation_interval
            }
        
        # 验证码配置
        if self._confirm_action("启用验证码识别"):
            print("\n🔍 验证码配置:")
            solver_types = ['ocr', 'manual', 'service']
            print("可用识别方式:")
            for i, solver_type in enumerate(solver_types, 1):
                print(f"{i}. {solver_type}")
            
            solver_choice = self._get_int_input("选择识别方式", 1, 1, len(solver_types)) - 1
            solver_type = solver_types[solver_choice]
            
            captcha_timeout = self._get_int_input("验证码超时时间/秒", 30, 5, 120)
            
            advanced_config['captcha'] = {
                'enabled': True,
                'solver_type': solver_type,
                'timeout': captcha_timeout
            }
        
        # 反检测配置
        if self._confirm_action("启用反检测功能"):
            print("\n🕵️ 反检测配置:")
            ua_rotation = self._confirm_action("启用User-Agent轮换")
            fingerprint_spoofing = self._confirm_action("启用指纹伪造")
            behavior_simulation = self._confirm_action("启用行为模拟")
            
            advanced_config['anti_detection'] = {
                'user_agent_rotation': ua_rotation,
                'fingerprint_spoofing': fingerprint_spoofing,
                'behavior_simulation': behavior_simulation
            }
        
        return advanced_config
    
    def _import_existing_config(self) -> Dict[str, Any]:
        """
        导入现有配置
        
        Returns:
            Dict[str, Any]: 导入的配置
        """
        print("\n📁 导入现有配置")
        
        config_file = input("请输入配置文件路径: ").strip()
        
        if not config_file:
            print("❌ 配置文件路径不能为空")
            return {}
        
        config_path = Path(config_file)
        
        if not config_path.exists():
            print(f"❌ 配置文件不存在: {config_path}")
            return {}
        
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            print(f"✅ 成功导入配置文件: {config_path}")
            
            # 显示配置摘要
            self._show_config_summary(config)
            
            # 询问是否需要修改
            if self._confirm_action("是否需要修改配置"):
                config = self._modify_imported_config(config)
            
            return config
            
        except json.JSONDecodeError as e:
            print(f"❌ 配置文件格式错误: {e}")
            return {}
        except Exception as e:
            print(f"❌ 导入配置失败: {e}")
            return {}
    
    def _modify_imported_config(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """
        修改导入的配置
        
        Args:
            config: 导入的配置
            
        Returns:
            Dict[str, Any]: 修改后的配置
        """
        print("\n🔧 修改导入的配置:")
        
        # 显示可修改的配置项
        modifiable_items = [
            ('target.url', '目标URL'),
            ('execution.concurrent_users', '并发用户数'),
            ('execution.total_requests', '总请求数'),
            ('target.timeout', '请求超时时间'),
            ('logging.level', '日志级别')
        ]
        
        for item_path, item_name in modifiable_items:
            current_value = self._get_nested_value(config, item_path)
            if current_value is not None:
                new_value = input(f"{item_name} [{current_value}]: ").strip()
                if new_value:
                    self._set_nested_value(config, item_path, new_value)
        
        return config
    
    def _get_nested_value(self, data: Dict[str, Any], path: str) -> Any:
        """
        获取嵌套字典的值
        
        Args:
            data: 字典数据
            path: 路径，用点分隔
            
        Returns:
            Any: 值
        """
        keys = path.split('.')
        current = data
        
        for key in keys:
            if isinstance(current, dict) and key in current:
                current = current[key]
            else:
                return None
        
        return current
    
    def _set_nested_value(self, data: Dict[str, Any], path: str, value: Any):
        """
        设置嵌套字典的值
        
        Args:
            data: 字典数据
            path: 路径，用点分隔
            value: 值
        """
        keys = path.split('.')
        current = data
        
        for key in keys[:-1]:
            if key not in current:
                current[key] = {}
            current = current[key]
        
        # 尝试转换数据类型
        final_key = keys[-1]
        if isinstance(current.get(final_key), int):
            try:
                value = int(value)
            except ValueError:
                pass
        elif isinstance(current.get(final_key), float):
            try:
                value = float(value)
            except ValueError:
                pass
        elif isinstance(current.get(final_key), bool):
            value = value.lower() in ['true', '1', 'yes', 'on']
        
        current[final_key] = value
    
    def _get_int_input(self, prompt: str, default: int, min_val: int = None, max_val: int = None) -> int:
        """
        获取整数输入
        
        Args:
            prompt: 提示信息
            default: 默认值
            min_val: 最小值
            max_val: 最大值
            
        Returns:
            int: 输入的整数
        """
        while True:
            try:
                value_str = input(f"{prompt} [{default}]: ").strip()
                if not value_str:
                    return default
                
                value = int(value_str)
                
                if min_val is not None and value < min_val:
                    print(f"❌ 值不能小于 {min_val}")
                    continue
                
                if max_val is not None and value > max_val:
                    print(f"❌ 值不能大于 {max_val}")
                    continue
                
                return value
                
            except ValueError:
                print("❌ 请输入有效的整数")
    
    def _get_float_input(self, prompt: str, default: float, min_val: float = None, max_val: float = None) -> float:
        """
        获取浮点数输入
        
        Args:
            prompt: 提示信息
            default: 默认值
            min_val: 最小值
            max_val: 最大值
            
        Returns:
            float: 输入的浮点数
        """
        while True:
            try:
                value_str = input(f"{prompt} [{default}]: ").strip()
                if not value_str:
                    return default
                
                value = float(value_str)
                
                if min_val is not None and value < min_val:
                    print(f"❌ 值不能小于 {min_val}")
                    continue
                
                if max_val is not None and value > max_val:
                    print(f"❌ 值不能大于 {max_val}")
                    continue
                
                return value
                
            except ValueError:
                print("❌ 请输入有效的数字")
    
    def _select_log_level(self) -> str:
        """
        选择日志级别
        
        Returns:
            str: 日志级别
        """
        levels = ['DEBUG', 'INFO', 'WARNING', 'ERROR']
        
        print("可用日志级别:")
        for i, level in enumerate(levels, 1):
            print(f"{i}. {level}")
        
        choice = self._get_int_input("选择日志级别", 2, 1, len(levels)) - 1
        return levels[choice]
    
    def _confirm_action(self, message: str) -> bool:
        """
        确认操作
        
        Args:
            message: 确认信息
            
        Returns:
            bool: 确认结果
        """
        while True:
            response = input(f"{message} (y/N): ").strip().lower()
            if response in ['y', 'yes']:
                return True
            elif response in ['n', 'no', '']:
                return False
            else:
                print("请输入 y 或 n")
    
    def _show_config_summary(self, config: Dict[str, Any]):
        """
        显示配置摘要
        
        Args:
            config: 配置数据
        """
        print("\n📋 配置摘要:")
        print("-" * 40)
        
        # 目标配置
        target = config.get('target', {})
        if target:
            print(f"目标URL: {target.get('url', 'N/A')}")
            print(f"超时时间: {target.get('timeout', 'N/A')} 秒")
        
        # 执行配置
        execution = config.get('execution', {})
        if execution:
            print(f"并发用户: {execution.get('concurrent_users', 'N/A')}")
            print(f"总请求数: {execution.get('total_requests', 'N/A')}")
        
        # 高级功能
        if config.get('proxy', {}).get('enabled'):
            print("代理池: 已启用")
        
        if config.get('captcha', {}).get('enabled'):
            print("验证码识别: 已启用")
        
        if config.get('anti_detection'):
            print("反检测功能: 已启用")
        
        # 日志配置
        logging_config = config.get('logging', {})
        if logging_config:
            print(f"日志级别: {logging_config.get('level', 'N/A')}")
    
    def _confirm_config(self) -> bool:
        """
        确认配置
        
        Returns:
            bool: 确认结果
        """
        print("\n" + "="*60)
        print("📋 最终配置预览:")
        print("="*60)
        
        self._show_config_summary(self.config_data)
        
        print("\n" + "="*60)
        
        return self._confirm_action("确认保存此配置")
    
    def _save_config(self) -> str:
        """
        保存配置
        
        Returns:
            str: 配置文件路径
        """
        # 默认配置文件路径
        default_path = "config/wizard_config.json"
        
        config_file = input(f"\n配置文件保存路径 [{default_path}]: ").strip()
        if not config_file:
            config_file = default_path
        
        config_path = Path(config_file)
        
        # 创建目录
        config_path.parent.mkdir(parents=True, exist_ok=True)
        
        # 保存配置
        with open(config_path, 'w', encoding='utf-8') as f:
            json.dump(self.config_data, f, ensure_ascii=False, indent=2)
        
        self.logger.info(f"配置已保存到: {config_path}")
        
        return str(config_path)


# 便捷函数
def run_config_wizard() -> Dict[str, Any]:
    """
    运行配置向导
    
    Returns:
        Dict[str, Any]: 配置数据
    """
    wizard = ConfigWizard()
    return wizard.run_wizard()


def create_config_from_template(template_name: str) -> Dict[str, Any]:
    """
    从模板创建配置
    
    Args:
        template_name: 模板名称
        
    Returns:
        Dict[str, Any]: 配置数据
    """
    wizard = ConfigWizard()
    
    if template_name in wizard.config_templates:
        return wizard.config_templates[template_name]['config'].copy()
    else:
        raise ValueError(f"未知模板: {template_name}")


def get_available_templates() -> List[str]:
    """
    获取可用模板列表
    
    Returns:
        List[str]: 模板名称列表
    """
    wizard = ConfigWizard()
    return list(wizard.config_templates.keys())

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>抢票测试报告</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background-color: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header { text-align: center; border-bottom: 2px solid #007bff; padding-bottom: 20px; margin-bottom: 30px; }
        .section { margin-bottom: 30px; }
        .section h2 { color: #007bff; border-left: 4px solid #007bff; padding-left: 10px; }
        .metrics-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; }
        .metric-card { background: #f8f9fa; padding: 15px; border-radius: 6px; border-left: 4px solid #28a745; }
        .metric-value { font-size: 24px; font-weight: bold; color: #007bff; }
        .metric-label { color: #666; font-size: 14px; }
        .scenario-table { width: 100%; border-collapse: collapse; margin-top: 15px; }
        .scenario-table th, .scenario-table td { padding: 10px; text-align: left; border-bottom: 1px solid #ddd; }
        .scenario-table th { background-color: #007bff; color: white; }
        .status-success { color: #28a745; font-weight: bold; }
        .status-failed { color: #dc3545; font-weight: bold; }
        .status-partial { color: #ffc107; font-weight: bold; }
        .suggestions { background: #e7f3ff; padding: 15px; border-radius: 6px; border-left: 4px solid #007bff; }
        .suggestions ul { margin: 10px 0; padding-left: 20px; }
        .chart-placeholder { background: #f8f9fa; padding: 40px; text-align: center; color: #666; border: 2px dashed #ddd; border-radius: 6px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>抢票系统测试报告</h1>
            <p>生成时间: 2025-07-30 18:20:25</p>
        </div>
        
        <div class="section">
            <h2>测试概览</h2>
            <div class="metrics-grid">
                <div class="metric-card">
                    <div class="metric-value">2</div>
                    <div class="metric-label">总场景数</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value">100.0%</div>
                    <div class="metric-label">场景成功率</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value">250</div>
                    <div class="metric-label">总请求数</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value">68.0%</div>
                    <div class="metric-label">请求成功率</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value">2.05s</div>
                    <div class="metric-label">平均响应时间</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value">60.0s</div>
                    <div class="metric-label">总测试时长</div>
                </div>
            </div>
        </div>
        
        <div class="section">
            <h2>场景执行结果</h2>
            <table class="scenario-table">
                <thead>
                    <tr>
                        <th>场景名称</th>
                        <th>攻击类型</th>
                        <th>状态</th>
                        <th>请求数</th>
                        <th>成功率</th>
                        <th>响应时间</th>
                        <th>持续时间</th>
                    </tr>
                </thead>
                <tbody>
                    
                <tr>
                    <td>测试场景 1</td>
                    <td>basic</td>
                    <td class="status-success">success</td>
                    <td>100</td>
                    <td>80.0%</td>
                    <td>2.01s</td>
                    <td>30.0s</td>
                </tr>
            
                <tr>
                    <td>测试场景 2</td>
                    <td>advanced</td>
                    <td class="status-partial">partial</td>
                    <td>150</td>
                    <td>60.0%</td>
                    <td>2.09s</td>
                    <td>30.0s</td>
                </tr>
            
                </tbody>
            </table>
        </div>
        
        <div class="section">
            <h2>防护效果分析</h2>
            <div class="metrics-grid">
                <div class="metric-card">
                    <div class="metric-value">5</div>
                    <div class="metric-label">验证码触发次数</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value">60.0%</div>
                    <div class="metric-label">验证码识别成功率</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value">2</div>
                    <div class="metric-label">IP封禁检测</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value">3</div>
                    <div class="metric-label">频率限制触发</div>
                </div>
            </div>
        </div>
        
        <div class="section">
            <h2>性能分析</h2>
            <div class="chart-placeholder">
                <p>性能图表占位符</p>
                <p>可集成 Chart.js 或其他图表库显示性能趋势</p>
            </div>
        </div>
        
        <div class="section">
            <h2>改进建议</h2>
            <div class="suggestions">
                <h3>系统优化建议</h3>
                <ul>
                    <li>验证码识别成功率较低，建议改进验证码处理模块</li>
                </ul>
            </div>
        </div>
    </div>
</body>
</html>
        
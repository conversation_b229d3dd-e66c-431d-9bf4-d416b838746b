"""
进度显示模块

提供实时进度显示、状态监控和用户反馈功能
"""

import sys
import time
import threading
from typing import Dict, Any, Optional, Callable, List
from dataclasses import dataclass
from datetime import datetime, timedelta

try:
    from ..core.logger import get_logger
except ImportError:
    import logging
    def get_logger(name):
        return logging.getLogger(name)


@dataclass
class ProgressInfo:
    """进度信息数据类"""
    current: int = 0
    total: int = 100
    percentage: float = 0.0
    elapsed_time: float = 0.0
    estimated_time: float = 0.0
    rate: float = 0.0
    status: str = "运行中"
    message: str = ""


@dataclass
class TaskStatus:
    """任务状态数据类"""
    task_id: str
    name: str
    status: str  # pending, running, completed, failed
    progress: ProgressInfo
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    error_message: str = ""


class ProgressDisplay:
    """
    进度显示类
    
    提供多种进度显示方式和实时状态监控
    """
    
    def __init__(self):
        """初始化进度显示"""
        self.logger = get_logger("ProgressDisplay")
        
        # 显示状态
        self.is_running = False
        self.display_thread = None
        self.update_interval = 0.5  # 更新间隔（秒）
        
        # 进度数据
        self.current_task = None
        self.task_history = []
        self.global_progress = ProgressInfo()
        
        # 显示配置
        self.show_percentage = True
        self.show_rate = True
        self.show_eta = True
        self.show_elapsed = True
        self.bar_width = 40
        self.use_colors = True
        
        # 回调函数
        self.callbacks = {}
        
        # 控制台相关
        self.last_line_length = 0
        
        # 状态符号
        self.status_symbols = {
            'pending': '⏳',
            'running': '🔄',
            'completed': '✅',
            'failed': '❌',
            'paused': '⏸️',
            'cancelled': '🚫'
        }
        
        # 颜色代码（ANSI）
        self.colors = {
            'reset': '\033[0m',
            'red': '\033[31m',
            'green': '\033[32m',
            'yellow': '\033[33m',
            'blue': '\033[34m',
            'magenta': '\033[35m',
            'cyan': '\033[36m',
            'white': '\033[37m',
            'bold': '\033[1m'
        }
    
    def start_display(self):
        """启动进度显示"""
        if self.is_running:
            return
        
        self.is_running = True
        self.display_thread = threading.Thread(target=self._display_loop, daemon=True)
        self.display_thread.start()
        
        self.logger.debug("进度显示已启动")
    
    def stop_display(self):
        """停止进度显示"""
        if not self.is_running:
            return
        
        self.is_running = False
        
        if self.display_thread and self.display_thread.is_alive():
            self.display_thread.join(timeout=1.0)
        
        # 清除最后一行显示
        self._clear_current_line()
        
        self.logger.debug("进度显示已停止")
    
    def update_progress(self, current: int, total: int, message: str = ""):
        """
        更新进度
        
        Args:
            current: 当前进度
            total: 总进度
            message: 状态消息
        """
        if total <= 0:
            return
        
        now = time.time()
        
        # 计算进度信息
        percentage = (current / total) * 100
        
        # 计算速率和预估时间
        if hasattr(self, '_start_time') and self._start_time:
            elapsed = now - self._start_time
            if elapsed > 0 and current > 0:
                rate = current / elapsed
                if rate > 0:
                    remaining = (total - current) / rate
                    estimated_time = elapsed + remaining
                else:
                    estimated_time = 0
            else:
                rate = 0
                estimated_time = 0
        else:
            self._start_time = now
            elapsed = 0
            rate = 0
            estimated_time = 0
        
        # 更新进度信息
        self.global_progress = ProgressInfo(
            current=current,
            total=total,
            percentage=percentage,
            elapsed_time=elapsed,
            estimated_time=estimated_time,
            rate=rate,
            status="运行中",
            message=message
        )
    
    def set_task_status(self, task_id: str, name: str, status: str, progress: Optional[ProgressInfo] = None):
        """
        设置任务状态
        
        Args:
            task_id: 任务ID
            name: 任务名称
            status: 任务状态
            progress: 进度信息
        """
        now = datetime.now()
        
        # 查找现有任务
        existing_task = None
        for task in self.task_history:
            if task.task_id == task_id:
                existing_task = task
                break
        
        if existing_task:
            # 更新现有任务
            existing_task.status = status
            if progress:
                existing_task.progress = progress
            
            if status == 'running' and not existing_task.start_time:
                existing_task.start_time = now
            elif status in ['completed', 'failed', 'cancelled']:
                existing_task.end_time = now
        else:
            # 创建新任务
            task_status = TaskStatus(
                task_id=task_id,
                name=name,
                status=status,
                progress=progress or ProgressInfo(),
                start_time=now if status == 'running' else None
            )
            self.task_history.append(task_status)
            
            if status == 'running':
                self.current_task = task_status
    
    def add_callback(self, event: str, callback: Callable):
        """
        添加回调函数
        
        Args:
            event: 事件名称
            callback: 回调函数
        """
        if event not in self.callbacks:
            self.callbacks[event] = []
        self.callbacks[event].append(callback)
    
    def _display_loop(self):
        """显示循环"""
        while self.is_running:
            try:
                self._update_display()
                time.sleep(self.update_interval)
            except Exception as e:
                self.logger.error(f"显示循环错误: {e}")
                break
    
    def _update_display(self):
        """更新显示"""
        # 构建显示内容
        display_lines = []
        
        # 全局进度条
        if self.global_progress.total > 0:
            progress_line = self._build_progress_bar(self.global_progress)
            display_lines.append(progress_line)
        
        # 当前任务状态
        if self.current_task:
            task_line = self._build_task_status(self.current_task)
            display_lines.append(task_line)
        
        # 显示内容
        if display_lines:
            self._display_lines(display_lines)
    
    def _build_progress_bar(self, progress: ProgressInfo) -> str:
        """
        构建进度条
        
        Args:
            progress: 进度信息
            
        Returns:
            str: 进度条字符串
        """
        # 计算进度条填充
        filled_width = int((progress.percentage / 100) * self.bar_width)
        empty_width = self.bar_width - filled_width
        
        # 构建进度条
        bar = '█' * filled_width + '░' * empty_width
        
        # 添加颜色
        if self.use_colors:
            if progress.percentage >= 100:
                bar_color = self.colors['green']
            elif progress.percentage >= 75:
                bar_color = self.colors['cyan']
            elif progress.percentage >= 50:
                bar_color = self.colors['yellow']
            else:
                bar_color = self.colors['red']
            
            bar = f"{bar_color}{bar}{self.colors['reset']}"
        
        # 构建完整显示
        parts = [f"[{bar}]"]
        
        if self.show_percentage:
            parts.append(f"{progress.percentage:6.1f}%")
        
        if self.show_rate and progress.rate > 0:
            parts.append(f"{progress.rate:.1f}/s")
        
        if self.show_elapsed:
            elapsed_str = self._format_time(progress.elapsed_time)
            parts.append(f"已用时: {elapsed_str}")
        
        if self.show_eta and progress.estimated_time > progress.elapsed_time:
            remaining = progress.estimated_time - progress.elapsed_time
            eta_str = self._format_time(remaining)
            parts.append(f"剩余: {eta_str}")
        
        if progress.message:
            parts.append(progress.message)
        
        return " | ".join(parts)
    
    def _build_task_status(self, task: TaskStatus) -> str:
        """
        构建任务状态显示
        
        Args:
            task: 任务状态
            
        Returns:
            str: 任务状态字符串
        """
        symbol = self.status_symbols.get(task.status, '❓')
        
        parts = [f"{symbol} {task.name}"]
        
        if task.status == 'running' and task.progress.total > 0:
            progress_text = f"({task.progress.current}/{task.progress.total})"
            parts.append(progress_text)
        
        if task.start_time:
            if task.end_time:
                duration = (task.end_time - task.start_time).total_seconds()
                duration_str = self._format_time(duration)
                parts.append(f"耗时: {duration_str}")
            else:
                elapsed = (datetime.now() - task.start_time).total_seconds()
                elapsed_str = self._format_time(elapsed)
                parts.append(f"运行: {elapsed_str}")
        
        if task.error_message:
            parts.append(f"错误: {task.error_message}")
        
        return " | ".join(parts)
    
    def _display_lines(self, lines: List[str]):
        """
        显示多行内容
        
        Args:
            lines: 显示行列表
        """
        # 清除之前的显示
        self._clear_previous_lines(len(lines))
        
        # 显示新内容
        for i, line in enumerate(lines):
            if i > 0:
                print()  # 换行
            print(line, end='', flush=True)
        
        # 记录行数
        self._displayed_lines = len(lines)
    
    def _clear_current_line(self):
        """清除当前行"""
        if self.last_line_length > 0:
            print('\r' + ' ' * self.last_line_length + '\r', end='', flush=True)
            self.last_line_length = 0
    
    def _clear_previous_lines(self, line_count: int):
        """
        清除之前的显示行
        
        Args:
            line_count: 行数
        """
        if hasattr(self, '_displayed_lines') and self._displayed_lines > 0:
            # 移动光标到开始位置并清除
            for _ in range(self._displayed_lines):
                print('\033[A\033[K', end='')  # 上移一行并清除
    
    def _format_time(self, seconds: float) -> str:
        """
        格式化时间显示
        
        Args:
            seconds: 秒数
            
        Returns:
            str: 格式化的时间字符串
        """
        if seconds < 60:
            return f"{seconds:.1f}s"
        elif seconds < 3600:
            minutes = int(seconds // 60)
            secs = int(seconds % 60)
            return f"{minutes}m{secs}s"
        else:
            hours = int(seconds // 3600)
            minutes = int((seconds % 3600) // 60)
            return f"{hours}h{minutes}m"
    
    def show_summary(self):
        """显示执行摘要"""
        if not self.task_history:
            print("📋 没有执行记录")
            return
        
        print("\n" + "="*80)
        print("📊 执行摘要")
        print("="*80)
        
        # 统计信息
        total_tasks = len(self.task_history)
        completed_tasks = sum(1 for task in self.task_history if task.status == 'completed')
        failed_tasks = sum(1 for task in self.task_history if task.status == 'failed')
        
        print(f"总任务数: {total_tasks}")
        print(f"成功任务: {completed_tasks}")
        print(f"失败任务: {failed_tasks}")
        print(f"成功率: {(completed_tasks / max(total_tasks, 1)) * 100:.1f}%")
        
        # 任务详情
        print(f"\n📋 任务详情:")
        print("-" * 80)
        
        for task in self.task_history:
            symbol = self.status_symbols.get(task.status, '❓')
            
            duration_str = ""
            if task.start_time and task.end_time:
                duration = (task.end_time - task.start_time).total_seconds()
                duration_str = f" ({self._format_time(duration)})"
            
            print(f"{symbol} {task.name} - {task.status.upper()}{duration_str}")
            
            if task.error_message:
                print(f"    错误: {task.error_message}")
        
        print("="*80)
    
    def create_simple_progress_bar(self, total: int, prefix: str = "", suffix: str = "") -> Callable:
        """
        创建简单进度条更新函数
        
        Args:
            total: 总数
            prefix: 前缀
            suffix: 后缀
            
        Returns:
            Callable: 更新函数
        """
        def update_func(current: int, message: str = ""):
            full_message = f"{prefix} {message} {suffix}".strip()
            self.update_progress(current, total, full_message)
        
        return update_func


class SimpleProgressBar:
    """
    简单进度条类
    
    用于不需要复杂功能的场景
    """
    
    def __init__(self, total: int, width: int = 50, prefix: str = "Progress"):
        """
        初始化简单进度条
        
        Args:
            total: 总数
            width: 进度条宽度
            prefix: 前缀文本
        """
        self.total = total
        self.width = width
        self.prefix = prefix
        self.current = 0
        self.start_time = time.time()
    
    def update(self, current: int = None, message: str = ""):
        """
        更新进度
        
        Args:
            current: 当前进度（如果为None则自增1）
            message: 状态消息
        """
        if current is not None:
            self.current = current
        else:
            self.current += 1
        
        # 计算百分比
        percentage = (self.current / self.total) * 100
        
        # 计算进度条
        filled_width = int((self.current / self.total) * self.width)
        bar = '█' * filled_width + '░' * (self.width - filled_width)
        
        # 计算速率和预估时间
        elapsed = time.time() - self.start_time
        if elapsed > 0 and self.current > 0:
            rate = self.current / elapsed
            if rate > 0:
                eta = (self.total - self.current) / rate
                eta_str = f"ETA: {eta:.1f}s"
            else:
                eta_str = "ETA: --"
        else:
            eta_str = "ETA: --"
        
        # 构建显示字符串
        display = f"\r{self.prefix}: [{bar}] {percentage:6.1f}% ({self.current}/{self.total}) {eta_str}"
        
        if message:
            display += f" | {message}"
        
        # 显示
        print(display, end='', flush=True)
        
        # 完成时换行
        if self.current >= self.total:
            print()
    
    def finish(self, message: str = "完成"):
        """
        完成进度条
        
        Args:
            message: 完成消息
        """
        self.update(self.total, message)


# 便捷函数
def create_progress_display() -> ProgressDisplay:
    """创建进度显示实例"""
    return ProgressDisplay()


def create_simple_progress_bar(total: int, prefix: str = "Progress") -> SimpleProgressBar:
    """
    创建简单进度条实例
    
    Args:
        total: 总数
        prefix: 前缀
        
    Returns:
        SimpleProgressBar: 进度条实例
    """
    return SimpleProgressBar(total, prefix=prefix)


def show_spinner(message: str = "处理中", duration: float = None) -> Callable:
    """
    显示旋转指示器
    
    Args:
        message: 显示消息
        duration: 持续时间（秒），None表示手动停止
        
    Returns:
        Callable: 停止函数
    """
    spinner_chars = ['⠋', '⠙', '⠹', '⠸', '⠼', '⠴', '⠦', '⠧', '⠇', '⠏']
    running = [True]  # 使用列表以便在闭包中修改
    
    def spinner_loop():
        i = 0
        start_time = time.time()
        
        while running[0]:
            if duration and (time.time() - start_time) >= duration:
                break
            
            char = spinner_chars[i % len(spinner_chars)]
            print(f'\r{char} {message}', end='', flush=True)
            
            time.sleep(0.1)
            i += 1
        
        print('\r' + ' ' * (len(message) + 3) + '\r', end='', flush=True)
    
    thread = threading.Thread(target=spinner_loop, daemon=True)
    thread.start()
    
    def stop_spinner():
        running[0] = False
        thread.join(timeout=0.5)
    
    if duration:
        # 自动停止
        threading.Timer(duration, stop_spinner).start()
    
    return stop_spinner
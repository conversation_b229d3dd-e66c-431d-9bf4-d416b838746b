# 代理服务器列表
# 格式: protocol://ip:port 或 ip:port (默认http)
# 支持认证: ***************************:port

# 示例免费代理 (仅供测试，实际使用请替换为有效代理)
# HTTP代理
http://***************:80
http://**************:53281
http://**************:80
http://************:8080
http://*************:83
http://***************:80
http://**************:53281
http://**************:80

# HTTPS代理
https://***************:443
https://**************:8443
https://**************:443

# SOCKS5代理
socks5://***************:1080
socks5://**************:1080
socks5://**************:1080

# 带认证的代理示例
# http://user:<EMAIL>:8080
# socks5://user:<EMAIL>:1080

# 注意：
# 1. 以上代理仅为示例，可能无效或不稳定
# 2. 生产环境请使用付费的高质量代理服务
# 3. 建议定期更新代理列表
# 4. 使用前请验证代理的有效性和匿名性
# 5. 遵守相关法律法规，仅用于合法测试目的
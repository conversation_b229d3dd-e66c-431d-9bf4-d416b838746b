"""
交互式界面模块

提供用户友好的交互式操作界面
"""

import os
import sys
import time
from typing import Dict, List, Optional, Any, Callable
from pathlib import Path

try:
    from ..core.logger import get_logger
except ImportError:
    import logging
    def get_logger(name):
        return logging.getLogger(name)


class InteractiveInterface:
    """
    交互式界面类
    
    提供菜单导航、用户输入处理、结果显示等功能
    """
    
    def __init__(self):
        """初始化交互式界面"""
        self.logger = get_logger("InteractiveInterface")
        self.current_menu = "main"
        self.menu_stack = []
        self.running = True
        
        # 菜单定义
        self.menus = {
            "main": {
                "title": "🎯 抢票系统安全测试工具 - 主菜单",
                "options": [
                    ("1", "运行测试场景", self._menu_run_scenarios),
                    ("2", "查看场景配置", self._menu_view_scenarios),
                    ("3", "生成测试报告", self._menu_generate_reports),
                    ("4", "系统管理", self._menu_system_management),
                    ("5", "配置管理", self._menu_config_management),
                    ("6", "帮助信息", self._menu_help),
                    ("0", "退出程序", self._menu_exit)
                ]
            },
            "run_scenarios": {
                "title": "🚀 运行测试场景",
                "options": [
                    ("1", "运行单个场景", self._run_single_scenario),
                    ("2", "运行场景组", self._run_scenario_group),
                    ("3", "批量运行所有场景", self._run_batch_scenarios),
                    ("4", "自定义场景执行", self._run_custom_scenario),
                    ("0", "返回主菜单", self._back_to_main)
                ]
            },
            "view_scenarios": {
                "title": "📋 查看场景配置",
                "options": [
                    ("1", "列出所有场景", self._list_all_scenarios),
                    ("2", "查看场景详情", self._view_scenario_details),
                    ("3", "查看场景组", self._view_scenario_groups),
                    ("4", "搜索场景", self._search_scenarios),
                    ("0", "返回主菜单", self._back_to_main)
                ]
            },
            "generate_reports": {
                "title": "📊 生成测试报告",
                "options": [
                    ("1", "从最新结果生成报告", self._generate_latest_report),
                    ("2", "从指定文件生成报告", self._generate_custom_report),
                    ("3", "批量生成报告", self._generate_batch_reports),
                    ("4", "查看历史报告", self._view_historical_reports),
                    ("0", "返回主菜单", self._back_to_main)
                ]
            },
            "system_management": {
                "title": "⚙️ 系统管理",
                "options": [
                    ("1", "系统状态检查", self._check_system_status),
                    ("2", "功能模块状态", self._check_module_status),
                    ("3", "清理临时文件", self._cleanup_temp_files),
                    ("4", "更新系统组件", self._update_components),
                    ("5", "诊断系统问题", self._diagnose_system),
                    ("0", "返回主菜单", self._back_to_main)
                ]
            },
            "config_management": {
                "title": "🔧 配置管理",
                "options": [
                    ("1", "查看当前配置", self._view_current_config),
                    ("2", "修改配置项", self._modify_config_item),
                    ("3", "重置配置", self._reset_config),
                    ("4", "导入配置", self._import_config),
                    ("5", "导出配置", self._export_config),
                    ("6", "配置向导", self._config_wizard),
                    ("0", "返回主菜单", self._back_to_main)
                ]
            }
        }
        
        # 回调函数存储
        self.callbacks = {}
    
    def register_callback(self, name: str, callback: Callable):
        """
        注册回调函数
        
        Args:
            name: 回调函数名称
            callback: 回调函数
        """
        self.callbacks[name] = callback
        self.logger.debug(f"注册回调函数: {name}")
    
    def run(self):
        """运行交互式界面"""
        self.logger.info("启动交互式界面")
        
        # 显示欢迎信息
        self._show_welcome()
        
        # 主循环
        while self.running:
            try:
                self._show_current_menu()
                choice = self._get_user_input("请选择操作")
                self._handle_menu_choice(choice)
                
            except KeyboardInterrupt:
                print("\n")
                if self._confirm_exit():
                    break
            except Exception as e:
                self.logger.error(f"界面操作错误: {e}")
                self._show_error(f"操作失败: {e}")
                self._pause()
        
        self._show_goodbye()
    
    def _show_welcome(self):
        """显示欢迎信息"""
        welcome_text = """
╔══════════════════════════════════════════════════════════════════════════════╗
║                     🎯 抢票系统安全测试工具 v1.0.0                            ║
╠══════════════════════════════════════════════════════════════════════════════╣
║                                                                              ║
║  欢迎使用抢票系统安全测试工具！                                               ║
║                                                                              ║
║  本工具专为安全研究和防护测试设计，帮助您：                                   ║
║  • 评估票务系统的安全防护能力                                                 ║
║  • 识别潜在的安全漏洞和风险点                                                 ║
║  • 测试各种攻击场景和防护策略                                                 ║
║  • 生成详细的安全评估报告                                                     ║
║                                                                              ║
║  ⚠️  请确保仅在授权环境中使用本工具！                                         ║
║                                                                              ║
╚══════════════════════════════════════════════════════════════════════════════╝
        """
        print(welcome_text)
        self._pause("按回车键继续...")
    
    def _show_current_menu(self):
        """显示当前菜单"""
        self._clear_screen()
        
        menu = self.menus.get(self.current_menu, self.menus["main"])
        
        print("=" * 80)
        print(menu["title"])
        print("=" * 80)
        print()
        
        for option_key, option_text, _ in menu["options"]:
            print(f"{option_key}. {option_text}")
        
        print()
    
    def _get_user_input(self, prompt: str) -> str:
        """
        获取用户输入
        
        Args:
            prompt: 提示信息
            
        Returns:
            str: 用户输入
        """
        try:
            return input(f"{prompt}: ").strip()
        except EOFError:
            return "0"  # 默认退出
    
    def _handle_menu_choice(self, choice: str):
        """
        处理菜单选择
        
        Args:
            choice: 用户选择
        """
        menu = self.menus.get(self.current_menu, self.menus["main"])
        
        for option_key, _, handler in menu["options"]:
            if choice == option_key:
                try:
                    handler()
                except Exception as e:
                    self.logger.error(f"菜单处理错误: {e}")
                    self._show_error(f"操作失败: {e}")
                    self._pause()
                return
        
        self._show_error("无效选择，请重新输入")
        self._pause()
    
    def _navigate_to_menu(self, menu_name: str):
        """
        导航到指定菜单
        
        Args:
            menu_name: 菜单名称
        """
        if menu_name in self.menus:
            self.menu_stack.append(self.current_menu)
            self.current_menu = menu_name
        else:
            self.logger.warning(f"未知菜单: {menu_name}")
    
    def _back_to_previous_menu(self):
        """返回上一级菜单"""
        if self.menu_stack:
            self.current_menu = self.menu_stack.pop()
        else:
            self.current_menu = "main"
    
    def _back_to_main(self):
        """返回主菜单"""
        self.current_menu = "main"
        self.menu_stack.clear()
    
    def _clear_screen(self):
        """清屏"""
        os.system('cls' if os.name == 'nt' else 'clear')
    
    def _pause(self, message: str = "按回车键继续..."):
        """
        暂停等待用户输入
        
        Args:
            message: 提示信息
        """
        try:
            input(f"\n{message}")
        except KeyboardInterrupt:
            pass
    
    def _show_error(self, message: str):
        """
        显示错误信息
        
        Args:
            message: 错误信息
        """
        print(f"\n❌ {message}")
    
    def _show_success(self, message: str):
        """
        显示成功信息
        
        Args:
            message: 成功信息
        """
        print(f"\n✅ {message}")
    
    def _show_info(self, message: str):
        """
        显示信息
        
        Args:
            message: 信息内容
        """
        print(f"\nℹ️  {message}")
    
    def _show_warning(self, message: str):
        """
        显示警告信息
        
        Args:
            message: 警告信息
        """
        print(f"\n⚠️  {message}")
    
    def _confirm_action(self, message: str) -> bool:
        """
        确认操作
        
        Args:
            message: 确认信息
            
        Returns:
            bool: 用户确认结果
        """
        while True:
            response = input(f"\n{message} (y/N): ").strip().lower()
            if response in ['y', 'yes']:
                return True
            elif response in ['n', 'no', '']:
                return False
            else:
                print("请输入 y 或 n")
    
    def _confirm_exit(self) -> bool:
        """确认退出"""
        return self._confirm_action("确认要退出程序吗？")
    
    def _show_goodbye(self):
        """显示告别信息"""
        goodbye_text = """
╔══════════════════════════════════════════════════════════════════════════════╗
║                              👋 感谢使用！                                    ║
╠══════════════════════════════════════════════════════════════════════════════╣
║                                                                              ║
║  感谢您使用抢票系统安全测试工具！                                             ║
║                                                                              ║
║  如果您在使用过程中遇到任何问题，请联系技术支持团队。                         ║
║  请记住：安全测试应当负责任地进行，仅在授权环境中使用。                       ║
║                                                                              ║
║  祝您工作顺利！                                                               ║
║                                                                              ║
╚══════════════════════════════════════════════════════════════════════════════╝
        """
        print(goodbye_text)
    
    # 菜单处理函数
    def _menu_run_scenarios(self):
        """运行测试场景菜单"""
        self._navigate_to_menu("run_scenarios")
    
    def _menu_view_scenarios(self):
        """查看场景配置菜单"""
        self._navigate_to_menu("view_scenarios")
    
    def _menu_generate_reports(self):
        """生成测试报告菜单"""
        self._navigate_to_menu("generate_reports")
    
    def _menu_system_management(self):
        """系统管理菜单"""
        self._navigate_to_menu("system_management")
    
    def _menu_config_management(self):
        """配置管理菜单"""
        self._navigate_to_menu("config_management")
    
    def _menu_help(self):
        """帮助信息"""
        help_text = """
╔══════════════════════════════════════════════════════════════════════════════╗
║                                📖 帮助信息                                    ║
╠══════════════════════════════════════════════════════════════════════════════╣
║                                                                              ║
║  🎯 主要功能：                                                                ║
║                                                                              ║
║  1. 运行测试场景                                                              ║
║     • 单个场景执行：测试特定攻击模式                                          ║
║     • 场景组执行：批量测试相关场景                                            ║
║     • 批量执行：运行所有可用场景                                              ║
║                                                                              ║
║  2. 查看场景配置                                                              ║
║     • 浏览所有可用测试场景                                                    ║
║     • 查看场景详细配置信息                                                    ║
║     • 搜索特定类型的场景                                                      ║
║                                                                              ║
║  3. 生成测试报告                                                              ║
║     • 自动生成多格式报告（JSON、HTML、PDF）                                   ║
║     • 包含详细的统计分析和图表                                                ║
║     • 提供安全建议和改进方案                                                  ║
║                                                                              ║
║  4. 系统管理                                                                  ║
║     • 检查系统状态和模块可用性                                                ║
║     • 清理临时文件和缓存                                                      ║
║     • 诊断和解决常见问题                                                      ║
║                                                                              ║
║  5. 配置管理                                                                  ║
║     • 查看和修改系统配置                                                      ║
║     • 导入导出配置文件                                                        ║
║     • 配置向导帮助设置                                                        ║
║                                                                              ║
║  💡 使用提示：                                                                ║
║     • 首次使用建议先运行系统状态检查                                          ║
║     • 执行测试前请确认目标系统已授权                                          ║
║     • 建议定期备份配置和测试结果                                              ║
║                                                                              ║
║  ⚠️  安全提醒：                                                               ║
║     • 仅在授权环境中使用本工具                                                ║
║     • 遵守相关法律法规和道德准则                                              ║
║     • 负责任地进行安全测试                                                    ║
║                                                                              ║
╚══════════════════════════════════════════════════════════════════════════════╝
        """
        print(help_text)
        self._pause()
    
    def _menu_exit(self):
        """退出程序"""
        if self._confirm_exit():
            self.running = False
    
    # 场景执行相关函数
    def _run_single_scenario(self):
        """运行单个场景"""
        callback = self.callbacks.get('run_single_scenario')
        if callback:
            callback()
        else:
            self._show_error("功能暂未实现")
        self._pause()
    
    def _run_scenario_group(self):
        """运行场景组"""
        callback = self.callbacks.get('run_scenario_group')
        if callback:
            callback()
        else:
            self._show_error("功能暂未实现")
        self._pause()
    
    def _run_batch_scenarios(self):
        """批量运行场景"""
        if self._confirm_action("确认要执行所有测试场景吗？这可能需要较长时间"):
            callback = self.callbacks.get('run_batch_scenarios')
            if callback:
                callback()
            else:
                self._show_error("功能暂未实现")
        self._pause()
    
    def _run_custom_scenario(self):
        """自定义场景执行"""
        print("\n🔧 自定义场景执行")
        print("-" * 40)
        
        # 获取用户输入的参数
        target_url = input("目标URL (可选): ").strip()
        concurrent_users = input("并发用户数 (默认10): ").strip()
        total_requests = input("总请求数 (默认100): ").strip()
        timeout = input("请求超时时间/秒 (默认30): ").strip()
        
        # 构建自定义配置
        custom_config = {}
        if target_url:
            custom_config['target_url'] = target_url
        if concurrent_users:
            try:
                custom_config['concurrent_users'] = int(concurrent_users)
            except ValueError:
                self._show_error("并发用户数必须是数字")
                return
        if total_requests:
            try:
                custom_config['total_requests'] = int(total_requests)
            except ValueError:
                self._show_error("总请求数必须是数字")
                return
        if timeout:
            try:
                custom_config['timeout'] = int(timeout)
            except ValueError:
                self._show_error("超时时间必须是数字")
                return
        
        callback = self.callbacks.get('run_custom_scenario')
        if callback:
            callback(custom_config)
        else:
            self._show_error("功能暂未实现")
        
        self._pause()
    
    # 场景查看相关函数
    def _list_all_scenarios(self):
        """列出所有场景"""
        callback = self.callbacks.get('list_all_scenarios')
        if callback:
            callback()
        else:
            self._show_error("功能暂未实现")
        self._pause()
    
    def _view_scenario_details(self):
        """查看场景详情"""
        scenario_id = input("\n请输入场景ID: ").strip()
        if scenario_id:
            callback = self.callbacks.get('view_scenario_details')
            if callback:
                callback(scenario_id)
            else:
                self._show_error("功能暂未实现")
        else:
            self._show_error("场景ID不能为空")
        self._pause()
    
    def _view_scenario_groups(self):
        """查看场景组"""
        callback = self.callbacks.get('view_scenario_groups')
        if callback:
            callback()
        else:
            self._show_error("功能暂未实现")
        self._pause()
    
    def _search_scenarios(self):
        """搜索场景"""
        keyword = input("\n请输入搜索关键词: ").strip()
        if keyword:
            callback = self.callbacks.get('search_scenarios')
            if callback:
                callback(keyword)
            else:
                self._show_error("功能暂未实现")
        else:
            self._show_error("搜索关键词不能为空")
        self._pause()
    
    # 报告生成相关函数
    def _generate_latest_report(self):
        """生成最新报告"""
        callback = self.callbacks.get('generate_latest_report')
        if callback:
            callback()
        else:
            self._show_error("功能暂未实现")
        self._pause()
    
    def _generate_custom_report(self):
        """生成自定义报告"""
        file_path = input("\n请输入结果文件路径: ").strip()
        if file_path and Path(file_path).exists():
            callback = self.callbacks.get('generate_custom_report')
            if callback:
                callback(file_path)
            else:
                self._show_error("功能暂未实现")
        else:
            self._show_error("文件路径无效或文件不存在")
        self._pause()
    
    def _generate_batch_reports(self):
        """批量生成报告"""
        callback = self.callbacks.get('generate_batch_reports')
        if callback:
            callback()
        else:
            self._show_error("功能暂未实现")
        self._pause()
    
    def _view_historical_reports(self):
        """查看历史报告"""
        callback = self.callbacks.get('view_historical_reports')
        if callback:
            callback()
        else:
            self._show_error("功能暂未实现")
        self._pause()
    
    # 系统管理相关函数
    def _check_system_status(self):
        """检查系统状态"""
        callback = self.callbacks.get('check_system_status')
        if callback:
            callback()
        else:
            self._show_error("功能暂未实现")
        self._pause()
    
    def _check_module_status(self):
        """检查模块状态"""
        callback = self.callbacks.get('check_module_status')
        if callback:
            callback()
        else:
            self._show_error("功能暂未实现")
        self._pause()
    
    def _cleanup_temp_files(self):
        """清理临时文件"""
        if self._confirm_action("确认要清理临时文件吗？"):
            callback = self.callbacks.get('cleanup_temp_files')
            if callback:
                callback()
            else:
                self._show_error("功能暂未实现")
        self._pause()
    
    def _update_components(self):
        """更新系统组件"""
        if self._confirm_action("确认要更新系统组件吗？"):
            callback = self.callbacks.get('update_components')
            if callback:
                callback()
            else:
                self._show_error("功能暂未实现")
        self._pause()
    
    def _diagnose_system(self):
        """诊断系统问题"""
        callback = self.callbacks.get('diagnose_system')
        if callback:
            callback()
        else:
            self._show_error("功能暂未实现")
        self._pause()
    
    # 配置管理相关函数
    def _view_current_config(self):
        """查看当前配置"""
        callback = self.callbacks.get('view_current_config')
        if callback:
            callback()
        else:
            self._show_error("功能暂未实现")
        self._pause()
    
    def _modify_config_item(self):
        """修改配置项"""
        callback = self.callbacks.get('modify_config_item')
        if callback:
            callback()
        else:
            self._show_error("功能暂未实现")
        self._pause()
    
    def _reset_config(self):
        """重置配置"""
        if self._confirm_action("确认要重置所有配置吗？这将恢复默认设置"):
            callback = self.callbacks.get('reset_config')
            if callback:
                callback()
            else:
                self._show_error("功能暂未实现")
        self._pause()
    
    def _import_config(self):
        """导入配置"""
        file_path = input("\n请输入配置文件路径: ").strip()
        if file_path and Path(file_path).exists():
            callback = self.callbacks.get('import_config')
            if callback:
                callback(file_path)
            else:
                self._show_error("功能暂未实现")
        else:
            self._show_error("文件路径无效或文件不存在")
        self._pause()
    
    def _export_config(self):
        """导出配置"""
        file_path = input("\n请输入导出文件路径 (默认: config_export.json): ").strip()
        if not file_path:
            file_path = "config_export.json"
        
        callback = self.callbacks.get('export_config')
        if callback:
            callback(file_path)
        else:
            self._show_error("功能暂未实现")
        self._pause()
    
    def _config_wizard(self):
        """配置向导"""
        callback = self.callbacks.get('config_wizard')
        if callback:
            callback()
        else:
            self._show_error("功能暂未实现")
        self._pause()


# 便捷函数
def create_interactive_interface() -> InteractiveInterface:
    """创建交互式界面实例"""
    return InteractiveInterface()


def run_interactive_mode(callbacks: Dict[str, Callable] = None):
    """
    运行交互式模式
    
    Args:
        callbacks: 回调函数字典
    """
    interface = create_interactive_interface()
    
    # 注册回调函数
    if callbacks:
        for name, callback in callbacks.items():
            interface.register_callback(name, callback)
    
    # 运行界面
    interface.run()
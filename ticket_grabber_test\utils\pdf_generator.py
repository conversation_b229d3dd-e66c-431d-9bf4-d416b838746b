"""
PDF报告生成器

生成专业的PDF格式测试报告
"""

import os
from typing import Dict, List, Any, Optional
from pathlib import Path
from datetime import datetime

try:
    from reportlab.lib.pagesizes import letter, A4
    from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
    from reportlab.lib.units import inch
    from reportlab.lib import colors
    from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle, PageBreak, Image
    from reportlab.platypus.flowables import HRFlowable
    from reportlab.lib.enums import TA_CENTER, TA_LEFT, TA_RIGHT
    REPORTLAB_AVAILABLE = True
except ImportError:
    REPORTLAB_AVAILABLE = False

try:
    from ..core.logger import get_logger
except ImportError:
    import logging
    def get_logger(name):
        return logging.getLogger(name)


class PDFReportGenerator:
    """
    PDF报告生成器
    
    生成专业的PDF格式测试报告
    """
    
    def __init__(self, output_dir: str = "reports"):
        """
        初始化PDF报告生成器
        
        Args:
            output_dir: 输出目录
        """
        self.logger = get_logger("PDFReportGenerator")
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        self.can_generate_pdf = REPORTLAB_AVAILABLE
        
        if not self.can_generate_pdf:
            self.logger.warning("reportlab不可用，PDF生成功能将被禁用")
        
        # 样式配置
        if self.can_generate_pdf:
            self.styles = getSampleStyleSheet()
            self._setup_custom_styles()
        
        self.logger.info(f"PDF报告生成器初始化完成，输出目录: {self.output_dir}")
    
    def _setup_custom_styles(self):
        """设置自定义样式"""
        # 标题样式
        self.styles.add(ParagraphStyle(
            name='CustomTitle',
            parent=self.styles['Title'],
            fontSize=24,
            spaceAfter=30,
            alignment=TA_CENTER,
            textColor=colors.HexColor('#2c3e50')
        ))
        
        # 章节标题样式
        self.styles.add(ParagraphStyle(
            name='SectionTitle',
            parent=self.styles['Heading1'],
            fontSize=16,
            spaceAfter=12,
            spaceBefore=20,
            textColor=colors.HexColor('#34495e'),
            borderWidth=1,
            borderColor=colors.HexColor('#3498db'),
            borderPadding=5,
            backColor=colors.HexColor('#ecf0f1')
        ))
        
        # 子标题样式
        self.styles.add(ParagraphStyle(
            name='SubTitle',
            parent=self.styles['Heading2'],
            fontSize=14,
            spaceAfter=8,
            spaceBefore=12,
            textColor=colors.HexColor('#2c3e50')
        ))
        
        # 指标样式
        self.styles.add(ParagraphStyle(
            name='MetricStyle',
            parent=self.styles['Normal'],
            fontSize=12,
            spaceAfter=6,
            leftIndent=20
        ))
        
        # 警告样式
        self.styles.add(ParagraphStyle(
            name='WarningStyle',
            parent=self.styles['Normal'],
            fontSize=11,
            textColor=colors.HexColor('#e74c3c'),
            backColor=colors.HexColor('#fdf2f2'),
            borderWidth=1,
            borderColor=colors.HexColor('#e74c3c'),
            borderPadding=8
        ))
        
        # 成功样式
        self.styles.add(ParagraphStyle(
            name='SuccessStyle',
            parent=self.styles['Normal'],
            fontSize=11,
            textColor=colors.HexColor('#27ae60'),
            backColor=colors.HexColor('#f0f9f0'),
            borderWidth=1,
            borderColor=colors.HexColor('#27ae60'),
            borderPadding=8
        ))
    
    def generate_pdf_report(self, report_data: Dict[str, Any], filename: str = None, 
                           include_charts: bool = True, chart_paths: Dict[str, str] = None) -> str:
        """
        生成PDF报告
        
        Args:
            report_data: 报告数据
            filename: 输出文件名
            include_charts: 是否包含图表
            chart_paths: 图表文件路径字典
            
        Returns:
            str: PDF文件路径
        """
        if not self.can_generate_pdf:
            return self._generate_text_report(report_data, filename)
        
        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"test_report_{timestamp}.pdf"
        
        filepath = self.output_dir / filename
        
        try:
            # 创建PDF文档
            doc = SimpleDocTemplate(
                str(filepath),
                pagesize=A4,
                rightMargin=72,
                leftMargin=72,
                topMargin=72,
                bottomMargin=18
            )
            
            # 构建内容
            story = []
            
            # 添加标题页
            self._add_title_page(story, report_data)
            
            # 添加执行摘要
            self._add_executive_summary(story, report_data)
            
            # 添加测试概览
            self._add_test_overview(story, report_data)
            
            # 添加场景详情
            self._add_scenario_details(story, report_data)
            
            # 添加性能分析
            self._add_performance_analysis(story, report_data)
            
            # 添加防护分析
            self._add_defense_analysis(story, report_data)
            
            # 添加图表（如果可用）
            if include_charts and chart_paths:
                self._add_charts(story, chart_paths)
            
            # 添加建议和结论
            self._add_recommendations(story, report_data)
            
            # 生成PDF
            doc.build(story)
            
            self.logger.info(f"PDF报告生成成功: {filepath}")
            return str(filepath)
            
        except Exception as e:
            self.logger.error(f"PDF报告生成失败: {e}")
            # 回退到文本报告
            return self._generate_text_report(report_data, filename.replace('.pdf', '.txt'))
    
    def _add_title_page(self, story: List, report_data: Dict[str, Any]):
        """添加标题页"""
        # 主标题
        title = Paragraph("抢票系统安全测试报告", self.styles['CustomTitle'])
        story.append(title)
        story.append(Spacer(1, 0.5*inch))
        
        # 副标题
        subtitle = Paragraph("Ticket Grabbing System Security Test Report", self.styles['Heading2'])
        story.append(subtitle)
        story.append(Spacer(1, 0.3*inch))
        
        # 报告信息表格
        metadata = report_data.get('metadata', {})
        overview = report_data.get('summary', {}).get('overview', {})
        
        report_info = [
            ['生成时间', metadata.get('generated_at', datetime.now().isoformat())],
            ['报告版本', metadata.get('generator_version', '1.0.0')],
            ['测试场景数', str(overview.get('total_scenarios', 0))],
            ['总测试时长', f"{overview.get('total_duration', 0):.1f} 秒"],
            ['报告类型', '综合安全测试报告']
        ]
        
        info_table = Table(report_info, colWidths=[2*inch, 3*inch])
        info_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (0, -1), colors.HexColor('#3498db')),
            ('TEXTCOLOR', (0, 0), (0, -1), colors.whitesmoke),
            ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
            ('FONTNAME', (0, 0), (-1, -1), 'Helvetica'),
            ('FONTSIZE', (0, 0), (-1, -1), 11),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 12),
            ('BACKGROUND', (1, 0), (1, -1), colors.HexColor('#ecf0f1')),
            ('GRID', (0, 0), (-1, -1), 1, colors.black)
        ]))
        
        story.append(info_table)
        story.append(Spacer(1, 0.5*inch))
        
        # 免责声明
        disclaimer = """
        <b>重要声明：</b><br/>
        本报告仅用于安全研究和防护测试目的。所有测试均在受控环境中进行，
        旨在评估和改进系统的安全防护能力。请勿将本报告中的技术用于非法用途。
        """
        
        disclaimer_para = Paragraph(disclaimer, self.styles['WarningStyle'])
        story.append(disclaimer_para)
        
        story.append(PageBreak())
    
    def _add_executive_summary(self, story: List, report_data: Dict[str, Any]):
        """添加执行摘要"""
        story.append(Paragraph("执行摘要", self.styles['SectionTitle']))
        
        summary = report_data.get('summary', {})
        overview = summary.get('overview', {})
        statistics = summary.get('statistics', {})
        
        # 关键发现
        key_findings = f"""
        本次安全测试共执行了 {overview.get('total_scenarios', 0)} 个测试场景，
        发送了 {statistics.get('total_requests', 0):,} 个请求，
        整体成功率为 {statistics.get('success_rate', 0):.1%}。
        
        测试结果显示：
        • 场景成功率：{overview.get('scenario_success_rate', 0):.1%}
        • 平均响应时间：{statistics.get('avg_response_time', 0):.2f} 秒
        • 请求阻断率：{statistics.get('block_rate', 0):.1%}
        • 系统防护有效性：{'良好' if statistics.get('block_rate', 0) > 0.3 else '需要改进'}
        """
        
        story.append(Paragraph(key_findings, self.styles['Normal']))
        story.append(Spacer(1, 0.2*inch))
        
        # 风险评估
        block_rate = statistics.get('block_rate', 0)
        if block_rate > 0.5:
            risk_level = "低风险"
            risk_color = self.styles['SuccessStyle']
        elif block_rate > 0.3:
            risk_level = "中等风险"
            risk_color = self.styles['Normal']
        else:
            risk_level = "高风险"
            risk_color = self.styles['WarningStyle']
        
        risk_assessment = f"<b>风险评估：{risk_level}</b><br/>基于当前测试结果，系统面临{risk_level}的自动化攻击威胁。"
        story.append(Paragraph(risk_assessment, risk_color))
        
        story.append(Spacer(1, 0.3*inch))
    
    def _add_test_overview(self, story: List, report_data: Dict[str, Any]):
        """添加测试概览"""
        story.append(Paragraph("测试概览", self.styles['SectionTitle']))
        
        overview = report_data.get('summary', {}).get('overview', {})
        statistics = report_data.get('summary', {}).get('statistics', {})
        
        # 测试统计表格
        stats_data = [
            ['指标', '数值', '说明'],
            ['总场景数', str(overview.get('total_scenarios', 0)), '执行的测试场景总数'],
            ['成功场景', str(overview.get('successful_scenarios', 0)), '完全成功的场景数'],
            ['失败场景', str(overview.get('failed_scenarios', 0)), '执行失败的场景数'],
            ['总请求数', f"{statistics.get('total_requests', 0):,}", '发送的HTTP请求总数'],
            ['成功请求', f"{statistics.get('successful_requests', 0):,}", '成功响应的请求数'],
            ['失败请求', f"{statistics.get('failed_requests', 0):,}", '失败的请求数'],
            ['被阻请求', f"{statistics.get('blocked_requests', 0):,}", '被系统阻断的请求数'],
            ['平均响应时间', f"{statistics.get('avg_response_time', 0):.2f}s", '所有请求的平均响应时间'],
            ['测试总时长', f"{overview.get('total_duration', 0):.1f}s", '所有场景的总执行时间']
        ]
        
        stats_table = Table(stats_data, colWidths=[2*inch, 1.5*inch, 2.5*inch])
        stats_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), colors.HexColor('#3498db')),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
            ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTNAME', (0, 1), (-1, -1), 'Helvetica'),
            ('FONTSIZE', (0, 0), (-1, -1), 10),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 8),
            ('BACKGROUND', (0, 1), (-1, -1), colors.HexColor('#f8f9fa')),
            ('GRID', (0, 0), (-1, -1), 1, colors.black),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE')
        ]))
        
        story.append(stats_table)
        story.append(Spacer(1, 0.3*inch))
    
    def _add_scenario_details(self, story: List, report_data: Dict[str, Any]):
        """添加场景详情"""
        story.append(Paragraph("场景执行详情", self.styles['SectionTitle']))
        
        scenarios = report_data.get('scenarios', [])
        
        if not scenarios:
            story.append(Paragraph("无场景数据", self.styles['Normal']))
            return
        
        # 场景汇总表格
        scenario_data = [['场景名称', '攻击类型', '状态', '请求数', '成功率', '响应时间']]
        
        for scenario in scenarios:
            basic_info = scenario.get('basic_info', {})
            statistics = scenario.get('statistics', {})
            
            success_rate = statistics.get('success_rate', 0) * 100
            avg_response_time = statistics.get('avg_response_time', 0)
            
            scenario_data.append([
                basic_info.get('name', 'Unknown')[:20],
                basic_info.get('attack_type', 'unknown'),
                basic_info.get('result', 'unknown'),
                str(statistics.get('total_requests', 0)),
                f"{success_rate:.1f}%",
                f"{avg_response_time:.2f}s"
            ])
        
        scenario_table = Table(scenario_data, colWidths=[2*inch, 1*inch, 0.8*inch, 0.8*inch, 0.8*inch, 1*inch])
        scenario_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), colors.HexColor('#2c3e50')),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTNAME', (0, 1), (-1, -1), 'Helvetica'),
            ('FONTSIZE', (0, 0), (-1, -1), 9),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 6),
            ('BACKGROUND', (0, 1), (-1, -1), colors.HexColor('#f8f9fa')),
            ('GRID', (0, 0), (-1, -1), 1, colors.black),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE')
        ]))
        
        story.append(scenario_table)
        story.append(Spacer(1, 0.2*inch))
        
        # 详细场景分析
        story.append(Paragraph("详细分析", self.styles['SubTitle']))
        
        for i, scenario in enumerate(scenarios[:3]):  # 只显示前3个场景的详细信息
            basic_info = scenario.get('basic_info', {})
            statistics = scenario.get('statistics', {})
            issues = scenario.get('issues', {})
            
            scenario_name = basic_info.get('name', f'场景 {i+1}')
            
            detail_text = f"""
            <b>{scenario_name}</b><br/>
            攻击类型：{basic_info.get('attack_type', 'unknown')}<br/>
            执行时间：{basic_info.get('duration', 0):.1f} 秒<br/>
            成功率：{statistics.get('success_rate', 0):.1%}<br/>
            """
            
            if issues.get('errors'):
                detail_text += f"错误：{len(issues['errors'])} 个<br/>"
            if issues.get('warnings'):
                detail_text += f"警告：{len(issues['warnings'])} 个<br/>"
            
            story.append(Paragraph(detail_text, self.styles['MetricStyle']))
        
        if len(scenarios) > 3:
            story.append(Paragraph(f"... 还有 {len(scenarios) - 3} 个场景的详细信息请参考原始数据", self.styles['Normal']))
        
        story.append(Spacer(1, 0.3*inch))
    
    def _add_performance_analysis(self, story: List, report_data: Dict[str, Any]):
        """添加性能分析"""
        story.append(Paragraph("性能分析", self.styles['SectionTitle']))
        
        performance = report_data.get('summary', {}).get('performance', {})
        statistics = report_data.get('summary', {}).get('statistics', {})
        
        # 性能指标
        perf_text = f"""
        <b>关键性能指标：</b><br/>
        • 平均响应时间：{statistics.get('avg_response_time', 0):.2f} 秒<br/>
        • 最小响应时间：{statistics.get('min_response_time', 0):.2f} 秒<br/>
        • 最大响应时间：{statistics.get('max_response_time', 0):.2f} 秒<br/>
        • 请求处理速率：{statistics.get('requests_per_second', 0):.1f} RPS<br/>
        • 并发用户数：{performance.get('concurrent_users', 0)}<br/>
        • 峰值并发请求：{performance.get('peak_concurrent_requests', 0)}<br/>
        """
        
        story.append(Paragraph(perf_text, self.styles['Normal']))
        
        # 性能评估
        avg_response_time = statistics.get('avg_response_time', 0)
        if avg_response_time < 1.0:
            perf_assessment = "系统响应性能优秀，能够快速处理请求。"
            perf_style = self.styles['SuccessStyle']
        elif avg_response_time < 3.0:
            perf_assessment = "系统响应性能良好，在可接受范围内。"
            perf_style = self.styles['Normal']
        else:
            perf_assessment = "系统响应较慢，可能存在性能瓶颈。"
            perf_style = self.styles['WarningStyle']
        
        story.append(Paragraph(f"<b>性能评估：</b>{perf_assessment}", perf_style))
        story.append(Spacer(1, 0.3*inch))
    
    def _add_defense_analysis(self, story: List, report_data: Dict[str, Any]):
        """添加防护分析"""
        story.append(Paragraph("防护效果分析", self.styles['SectionTitle']))
        
        defense = report_data.get('summary', {}).get('defense_analysis', {})
        
        # 防护统计表格
        defense_data = [
            ['防护机制', '触发次数', '成功率', '效果评估'],
            [
                '验证码防护',
                str(defense.get('captcha_triggered', 0)),
                f"{defense.get('captcha_success_rate', 0):.1%}",
                '有效' if defense.get('captcha_triggered', 0) > 0 else '未触发'
            ],
            [
                'IP封禁',
                str(defense.get('ip_blocks_detected', 0)),
                'N/A',
                '有效' if defense.get('ip_blocks_detected', 0) > 0 else '未检测到'
            ],
            [
                '频率限制',
                str(defense.get('rate_limits_hit', 0)),
                'N/A',
                '有效' if defense.get('rate_limits_hit', 0) > 0 else '未触发'
            ],
            [
                '反检测技术',
                str(defense.get('anti_detection_bypassed', 0) + defense.get('anti_detection_blocked', 0)),
                f"{defense.get('anti_detection_success_rate', 0):.1%}",
                '需要改进' if defense.get('anti_detection_success_rate', 0) > 0.5 else '有效'
            ]
        ]
        
        defense_table = Table(defense_data, colWidths=[2*inch, 1.2*inch, 1*inch, 1.5*inch])
        defense_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), colors.HexColor('#e74c3c')),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTNAME', (0, 1), (-1, -1), 'Helvetica'),
            ('FONTSIZE', (0, 0), (-1, -1), 10),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 8),
            ('BACKGROUND', (0, 1), (-1, -1), colors.HexColor('#f8f9fa')),
            ('GRID', (0, 0), (-1, -1), 1, colors.black),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE')
        ]))
        
        story.append(defense_table)
        story.append(Spacer(1, 0.2*inch))
        
        # 防护效果总结
        total_blocks = defense.get('ip_blocks_detected', 0) + defense.get('rate_limits_hit', 0) + defense.get('captcha_triggered', 0)
        
        if total_blocks > 0:
            defense_summary = f"系统防护机制有效，共触发 {total_blocks} 次防护措施。"
            defense_style = self.styles['SuccessStyle']
        else:
            defense_summary = "系统防护机制未被有效触发，建议加强防护策略。"
            defense_style = self.styles['WarningStyle']
        
        story.append(Paragraph(f"<b>防护总结：</b>{defense_summary}", defense_style))
        story.append(Spacer(1, 0.3*inch))
    
    def _add_charts(self, story: List, chart_paths: Dict[str, str]):
        """添加图表"""
        story.append(PageBreak())
        story.append(Paragraph("图表分析", self.styles['SectionTitle']))
        
        chart_titles = {
            'success_rate': '成功率对比图',
            'response_time': '响应时间分析图',
            'defense_analysis': '防护效果分析图',
            'timeline': '执行时间线图',
            'performance': '性能监控图',
            'dashboard': '汇总仪表板'
        }
        
        for chart_key, chart_path in chart_paths.items():
            if os.path.exists(chart_path):
                try:
                    # 添加图表标题
                    title = chart_titles.get(chart_key, f'{chart_key} 图表')
                    story.append(Paragraph(title, self.styles['SubTitle']))
                    
                    # 添加图片
                    img = Image(chart_path, width=6*inch, height=4*inch)
                    story.append(img)
                    story.append(Spacer(1, 0.2*inch))
                    
                except Exception as e:
                    self.logger.warning(f"无法添加图表 {chart_key}: {e}")
                    story.append(Paragraph(f"图表 {title} 无法显示", self.styles['Normal']))
                    story.append(Spacer(1, 0.1*inch))
    
    def _add_recommendations(self, story: List, report_data: Dict[str, Any]):
        """添加建议和结论"""
        story.append(PageBreak())
        story.append(Paragraph("建议与结论", self.styles['SectionTitle']))
        
        trends = report_data.get('summary', {}).get('trends', {})
        suggestions = trends.get('improvement_suggestions', [])
        
        # 改进建议
        story.append(Paragraph("改进建议", self.styles['SubTitle']))
        
        if suggestions:
            for i, suggestion in enumerate(suggestions, 1):
                story.append(Paragraph(f"{i}. {suggestion}", self.styles['Normal']))
        else:
            story.append(Paragraph("基于当前测试结果，系统运行正常，暂无特殊改进建议。", self.styles['SuccessStyle']))
        
        story.append(Spacer(1, 0.2*inch))
        
        # 安全建议
        story.append(Paragraph("安全防护建议", self.styles['SubTitle']))
        
        security_recommendations = [
            "定期更新和调整验证码策略，提高自动识别难度",
            "实施动态IP封禁策略，根据行为模式调整封禁阈值",
            "加强用户行为分析，识别异常访问模式",
            "部署多层防护体系，包括CDN、WAF等安全设备",
            "建立实时监控和告警机制，及时发现攻击行为",
            "定期进行安全测试，评估防护效果并持续改进"
        ]
        
        for i, recommendation in enumerate(security_recommendations, 1):
            story.append(Paragraph(f"{i}. {recommendation}", self.styles['Normal']))
        
        story.append(Spacer(1, 0.3*inch))
        
        # 结论
        story.append(Paragraph("测试结论", self.styles['SubTitle']))
        
        statistics = report_data.get('summary', {}).get('statistics', {})
        block_rate = statistics.get('block_rate', 0)
        
        if block_rate > 0.5:
            conclusion = "系统具备良好的安全防护能力，能够有效抵御自动化攻击。建议继续保持并定期评估防护效果。"
        elif block_rate > 0.3:
            conclusion = "系统具备基本的安全防护能力，但仍有改进空间。建议加强部分防护措施。"
        else:
            conclusion = "系统的安全防护能力有待提升，建议尽快实施更严格的防护策略。"
        
        story.append(Paragraph(conclusion, self.styles['Normal']))
        
        # 免责声明
        story.append(Spacer(1, 0.3*inch))
        story.append(HRFlowable(width="100%"))
        story.append(Spacer(1, 0.1*inch))
        
        disclaimer = """
        <b>免责声明：</b>本报告仅基于特定测试环境和条件下的结果，
        实际生产环境可能存在差异。建议结合实际业务场景进行综合评估。
        本报告内容仅供参考，不构成任何法律建议或保证。
        """
        
        story.append(Paragraph(disclaimer, self.styles['Normal']))
    
    def _generate_text_report(self, report_data: Dict[str, Any], filename: str) -> str:
        """生成文本格式报告（当PDF不可用时）"""
        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"test_report_{timestamp}.txt"
        
        filepath = self.output_dir / filename
        
        try:
            with open(filepath, 'w', encoding='utf-8') as f:
                f.write("=" * 80 + "\n")
                f.write("抢票系统安全测试报告\n")
                f.write("=" * 80 + "\n\n")
                
                # 基本信息
                metadata = report_data.get('metadata', {})
                f.write(f"生成时间: {metadata.get('generated_at', 'Unknown')}\n")
                f.write(f"报告版本: {metadata.get('generator_version', '1.0.0')}\n\n")
                
                # 测试概览
                overview = report_data.get('summary', {}).get('overview', {})
                statistics = report_data.get('summary', {}).get('statistics', {})
                
                f.write("测试概览:\n")
                f.write("-" * 40 + "\n")
                f.write(f"总场景数: {overview.get('total_scenarios', 0)}\n")
                f.write(f"成功场景: {overview.get('successful_scenarios', 0)}\n")
                f.write(f"失败场景: {overview.get('failed_scenarios', 0)}\n")
                f.write(f"总请求数: {statistics.get('total_requests', 0):,}\n")
                f.write(f"成功率: {statistics.get('success_rate', 0):.1%}\n")
                f.write(f"平均响应时间: {statistics.get('avg_response_time', 0):.2f}s\n")
                f.write(f"测试总时长: {overview.get('total_duration', 0):.1f}s\n\n")
                
                # 防护分析
                defense = report_data.get('summary', {}).get('defense_analysis', {})
                f.write("防护分析:\n")
                f.write("-" * 40 + "\n")
                f.write(f"验证码触发: {defense.get('captcha_triggered', 0)}\n")
                f.write(f"IP封禁检测: {defense.get('ip_blocks_detected', 0)}\n")
                f.write(f"频率限制: {defense.get('rate_limits_hit', 0)}\n")
                f.write(f"反检测成功率: {defense.get('anti_detection_success_rate', 0):.1%}\n\n")
                
                # 建议
                trends = report_data.get('summary', {}).get('trends', {})
                suggestions = trends.get('improvement_suggestions', [])
                
                if suggestions:
                    f.write("改进建议:\n")
                    f.write("-" * 40 + "\n")
                    for i, suggestion in enumerate(suggestions, 1):
                        f.write(f"{i}. {suggestion}\n")
                
                f.write("\n" + "=" * 80 + "\n")
                f.write("注意: 此为简化版报告，完整PDF报告需要安装 reportlab 库\n")
                f.write("安装命令: pip install reportlab\n")
            
            self.logger.info(f"文本报告生成成功: {filepath}")
            return str(filepath)
            
        except Exception as e:
            self.logger.error(f"文本报告生成失败: {e}")
            raise


# 便捷函数
def create_pdf_generator(output_dir: str = "reports") -> PDFReportGenerator:
    """创建PDF报告生成器"""
    return PDFReportGenerator(output_dir)


def generate_quick_pdf(report_data: Dict[str, Any], output_dir: str = "reports", 
                      include_charts: bool = True, chart_paths: Dict[str, str] = None) -> str:
    """快速生成PDF报告"""
    generator = PDFReportGenerator(output_dir)
    return generator.generate_pdf_report(report_data, include_charts=include_charts, chart_paths=chart_paths)
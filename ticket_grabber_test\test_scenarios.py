#!/usr/bin/env python3
"""
测试场景框架验证脚本

验证测试场景框架的各个组件是否正常工作
"""

import sys
import time
import json
from pathlib import Path

# 添加项目根目录到路径
sys.path.insert(0, str(Path(__file__).parent))

def test_imports():
    """测试模块导入"""
    print("=== 测试模块导入 ===")
    
    try:
        from scenarios import (
            BaseScenario, ScenarioConfig, AttackType, ScenarioStatus,
            BasicAttackScenario, AdvancedAttackScenario,
            ScenarioManager, ScenarioExecutor,
            create_scenario_manager, QuickTest
        )
        print("✅ 所有模块导入成功")
        return True
    except ImportError as e:
        print(f"❌ 模块导入失败: {e}")
        return False


def test_scenario_config():
    """测试场景配置"""
    print("\n=== 测试场景配置 ===")
    
    try:
        from scenarios import ScenarioConfig, AttackType
        
        config = ScenarioConfig(
            name="测试场景",
            description="这是一个测试场景",
            attack_type=AttackType.BASIC,
            target_url="http://httpbin.org/get",
            max_requests=5,
            concurrent_users=1,
            request_interval=1.0,
            timeout=10.0
        )
        
        print(f"✅ 场景配置创建成功: {config.name}")
        print(f"   攻击类型: {config.attack_type.value}")
        print(f"   目标URL: {config.target_url}")
        print(f"   最大请求: {config.max_requests}")
        
        return True
    except Exception as e:
        print(f"❌ 场景配置测试失败: {e}")
        return False


def test_basic_scenario():
    """测试基础场景"""
    print("\n=== 测试基础场景 ===")
    
    try:
        from scenarios import BasicAttackScenario, ScenarioConfig, AttackType
        
        config = ScenarioConfig(
            name="基础攻击测试",
            description="测试基础攻击场景",
            attack_type=AttackType.BASIC,
            target_url="http://httpbin.org/get",
            max_requests=3,
            concurrent_users=1,
            request_interval=0.5,
            timeout=5.0,
            use_anti_detection=False  # 简化测试
        )
        
        scenario = BasicAttackScenario(config)
        print(f"✅ 基础场景创建成功: {scenario.scenario_id[:8]}")
        
        # 执行场景
        print("   开始执行场景...")
        success = scenario.execute(blocking=True)
        
        if success:
            print(f"✅ 场景执行完成")
            print(f"   状态: {scenario.status.value}")
            print(f"   结果: {scenario.result.value if scenario.result else 'N/A'}")
            print(f"   总请求: {scenario.metrics.total_requests}")
            print(f"   成功请求: {scenario.metrics.successful_requests}")
            print(f"   成功率: {scenario.metrics.success_rate:.1%}")
            print(f"   耗时: {scenario.metrics.duration:.2f}s")
        else:
            print("❌ 场景执行失败")
            return False
        
        return True
    except Exception as e:
        print(f"❌ 基础场景测试失败: {e}")
        return False


def test_scenario_manager():
    """测试场景管理器"""
    print("\n=== 测试场景管理器 ===")
    
    try:
        from scenarios import create_scenario_manager
        
        # 创建管理器（使用默认配置）
        manager = create_scenario_manager()
        print("✅ 场景管理器创建成功")
        
        # 测试场景列表
        scenarios = manager.list_scenarios()
        print(f"   可用场景数: {len(scenarios)}")
        
        # 测试场景组列表
        groups = manager.list_scenario_groups()
        print(f"   可用场景组数: {len(groups)}")
        
        # 测试统计信息
        stats = manager.get_statistics()
        print(f"   统计信息: {stats}")
        
        manager.shutdown()
        print("✅ 场景管理器测试完成")
        
        return True
    except Exception as e:
        print(f"❌ 场景管理器测试失败: {e}")
        return False


def test_config_loading():
    """测试配置文件加载"""
    print("\n=== 测试配置文件加载 ===")
    
    try:
        config_file = "config/test_scenarios.json"
        config_path = Path(config_file)
        
        if not config_path.exists():
            print(f"⚠️  配置文件不存在: {config_file}")
            return True  # 不算失败，因为可能还没创建
        
        from scenarios import ScenarioManager
        
        manager = ScenarioManager(config_file)
        scenarios = manager.list_scenarios()
        
        print(f"✅ 配置文件加载成功: {config_file}")
        print(f"   加载场景数: {len(scenarios)}")
        
        # 显示前几个场景
        for i, scenario_name in enumerate(scenarios[:3]):
            config = manager.get_scenario_config(scenario_name)
            if config:
                print(f"   {i+1}. {scenario_name} ({config.get('attack_type', 'N/A')})")
        
        manager.shutdown()
        return True
    except Exception as e:
        print(f"❌ 配置文件加载测试失败: {e}")
        return False


def test_quick_interface():
    """测试快速接口"""
    print("\n=== 测试快速接口 ===")
    
    try:
        from scenarios import QuickTest, create_quick_scenario, AttackType
        
        # 测试快速场景创建
        scenario = create_quick_scenario(
            'basic',
            'http://httpbin.org/get',
            max_requests=2,
            request_interval=0.5
        )
        
        print(f"✅ 快速场景创建成功: {scenario.config.name}")
        
        # 测试快速基础攻击（使用httpbin作为安全的测试目标）
        print("   执行快速基础攻击测试...")
        success = QuickTest.basic_attack('http://httpbin.org/get', max_requests=2)
        
        if success:
            print("✅ 快速基础攻击测试成功")
        else:
            print("⚠️  快速基础攻击测试未完全成功（可能是网络问题）")
        
        return True
    except Exception as e:
        print(f"❌ 快速接口测试失败: {e}")
        return False


def test_scenario_executor():
    """测试场景执行器"""
    print("\n=== 测试场景执行器 ===")
    
    try:
        from scenarios import ScenarioExecutor
        
        executor = ScenarioExecutor(log_level="WARNING")  # 减少日志输出
        print("✅ 场景执行器创建成功")
        
        # 测试状态显示
        executor.show_status()
        print("✅ 状态显示测试完成")
        
        executor.shutdown()
        return True
    except Exception as e:
        print(f"❌ 场景执行器测试失败: {e}")
        return False


def test_error_handling():
    """测试错误处理"""
    print("\n=== 测试错误处理 ===")
    
    try:
        from scenarios import create_quick_scenario
        
        # 测试无效攻击类型
        try:
            scenario = create_quick_scenario('invalid_type', 'http://example.com')
            print("❌ 应该抛出错误但没有")
            return False
        except ValueError:
            print("✅ 无效攻击类型错误处理正确")
        
        # 测试无效URL（不会立即失败，但会在执行时处理）
        scenario = create_quick_scenario(
            'basic', 
            'http://invalid-url-that-does-not-exist.com',
            max_requests=1,
            timeout=1.0
        )
        print("✅ 无效URL场景创建成功（错误将在执行时处理）")
        
        return True
    except Exception as e:
        print(f"❌ 错误处理测试失败: {e}")
        return False


def run_all_tests():
    """运行所有测试"""
    print("开始测试场景框架...")
    print("=" * 50)
    
    tests = [
        ("模块导入", test_imports),
        ("场景配置", test_scenario_config),
        ("基础场景", test_basic_scenario),
        ("场景管理器", test_scenario_manager),
        ("配置文件加载", test_config_loading),
        ("快速接口", test_quick_interface),
        ("场景执行器", test_scenario_executor),
        ("错误处理", test_error_handling)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
            else:
                print(f"❌ {test_name} 测试失败")
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
    
    print("\n" + "=" * 50)
    print(f"测试完成: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！场景框架工作正常")
        return True
    else:
        print("⚠️  部分测试失败，请检查相关组件")
        return False


def main():
    """主函数"""
    try:
        success = run_all_tests()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n测试执行异常: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
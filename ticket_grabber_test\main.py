#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
抢票系统安全测试工具 - 主程序入口

这是一个用于测试票务系统防护手段的安全测试工具。
仅用于防御性安全研究和系统防护能力评估。

作者: Security Research Team
版本: 1.0.0
许可: 仅限安全研究使用
"""

import sys
import os
import argparse
import json
import time
from pathlib import Path
from typing import Dict, List, Optional, Any

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 导入核心模块
try:
    from core.logger import setup_logging, get_logger
    from core.config_manager import ConfigManager
    from scenarios.scenario_manager import ScenarioManager
    from scenarios.scenario_executor import ScenarioExecutor
    from utils import (
        get_available_features, 
        generate_comprehensive_report,
        REPORT_GENERATOR_AVAILABLE,
        MONITOR_AVAILABLE
    )
    
    # 导入CLI模块
    from cli import (
        InteractiveInterface,
        ConfigWizard,
        ProgressDisplay,
        CommandParser
    )
    
    # 尝试导入监控模块
    if MONITOR_AVAILABLE:
        from utils.monitor import create_monitor
    
except ImportError as e:
    print(f"❌ 模块导入失败: {e}")
    print("请确保所有依赖模块已正确安装")
    sys.exit(1)


class TicketGrabberTestTool:
    """
    抢票系统安全测试工具主类
    """
    
    def __init__(self):
        """初始化测试工具"""
        self.logger = None
        self.config_manager = None
        self.scenario_manager = None
        self.monitor = None
        self.version = "1.0.0"
        
        # 安全警告标志
        self.security_warning_shown = False
        
    def initialize(self, config_file: str = None, log_level: str = "INFO"):
        """
        初始化系统组件
        
        Args:
            config_file: 配置文件路径
            log_level: 日志级别
        """
        try:
            # 设置日志
            setup_logging(level=log_level)
            self.logger = get_logger("TicketGrabberTestTool")
            
            self.logger.info(f"初始化抢票系统安全测试工具 v{self.version}")
            
            # 显示安全警告
            self._show_security_warning()
            
            # 初始化配置管理器
            if config_file and Path(config_file).exists():
                self.config_manager = ConfigManager(config_file)
            else:
                self.config_manager = ConfigManager()
            
            self.logger.info("配置管理器初始化完成")
            
            # 初始化场景管理器
            self.scenario_manager = ScenarioManager()
            self.logger.info("场景管理器初始化完成")
            
            # 初始化监控器（如果可用）
            if MONITOR_AVAILABLE:
                try:
                    self.monitor = create_monitor()
                    self.logger.info("实时监控器初始化完成")
                except Exception as e:
                    self.logger.warning(f"监控器初始化失败: {e}")
            
            # 检查功能可用性
            self._check_feature_availability()
            
            self.logger.info("系统初始化完成")
            return True
            
        except Exception as e:
            if self.logger:
                self.logger.error(f"系统初始化失败: {e}")
            else:
                print(f"❌ 系统初始化失败: {e}")
            return False
    
    def _show_security_warning(self):
        """显示安全警告"""
        if self.security_warning_shown:
            return
            
        warning_text = """
╔══════════════════════════════════════════════════════════════════════════════╗
║                                 ⚠️  安全警告  ⚠️                                ║
╠══════════════════════════════════════════════════════════════════════════════╣
║                                                                              ║
║  本工具仅用于安全研究和防护测试目的！                                          ║
║                                                                              ║
║  ✅ 合法用途:                                                                 ║
║    • 测试自己系统的安全防护能力                                               ║
║    • 安全研究和漏洞发现                                                       ║
║    • 防护机制有效性评估                                                       ║
║                                                                              ║
║  ❌ 禁止用途:                                                                 ║
║    • 攻击他人系统或服务                                                       ║
║    • 非法获取票务或其他资源                                                   ║
║    • 任何违法违规行为                                                         ║
║                                                                              ║
║  使用本工具即表示您同意仅将其用于合法的安全研究目的，                          ║
║  并承担因不当使用而产生的所有法律责任。                                        ║
║                                                                              ║
╚══════════════════════════════════════════════════════════════════════════════╝
        """
        
        print(warning_text)
        
        # 要求用户确认
        while True:
            try:
                confirm = input("\n请输入 'I AGREE' 确认您已阅读并同意上述条款: ").strip()
                if confirm == "I AGREE":
                    break
                elif confirm.lower() in ['quit', 'exit', 'q']:
                    print("程序退出")
                    sys.exit(0)
                else:
                    print("请输入 'I AGREE' 确认同意，或输入 'quit' 退出程序")
            except KeyboardInterrupt:
                print("\n程序退出")
                sys.exit(0)
        
        self.security_warning_shown = True
        print("✅ 安全条款确认完成\n")
    
    def _check_feature_availability(self):
        """检查功能可用性"""
        features = get_available_features()
        
        self.logger.info(f"功能可用性检查:")
        self.logger.info(f"  可用功能: {features['available']}")
        
        if features['unavailable']:
            self.logger.warning(f"  不可用功能: {features['unavailable']}")
            self.logger.info("  安装建议:")
            
            if 'monitor' in features['unavailable']:
                self.logger.info("    系统监控: pip install psutil")
            if 'chart_generator' in features['unavailable']:
                self.logger.info("    图表生成: pip install matplotlib numpy")
            if 'pdf_generator' in features['unavailable']:
                self.logger.info("    PDF生成: pip install reportlab")
    
    def run_interactive_mode(self):
        """运行交互式模式"""
        self.logger.info("启动交互式模式")
        
        # 创建交互式界面
        interface = InteractiveInterface()
        
        # 注册回调函数
        self._register_interface_callbacks(interface)
        
        # 运行界面
        interface.run()
    
    def _interactive_single_scenario(self):
        """交互式运行单个场景"""
        print("\n📝 单个场景执行")
        print("-" * 40)
        
        # 获取可用场景
        scenarios = self.scenario_manager.get_available_scenarios()
        
        if not scenarios:
            print("❌ 没有可用的测试场景")
            return
        
        # 显示场景列表
        print("可用场景:")
        for i, scenario_id in enumerate(scenarios, 1):
            scenario_info = self.scenario_manager.get_scenario_info(scenario_id)
            print(f"{i}. {scenario_id} - {scenario_info.get('name', 'Unknown')}")
        
        try:
            choice = int(input(f"\n请选择场景 (1-{len(scenarios)}): ")) - 1
            if 0 <= choice < len(scenarios):
                scenario_id = scenarios[choice]
                print(f"\n🚀 执行场景: {scenario_id}")
                
                # 启动监控（如果可用）
                if self.monitor:
                    self.monitor.start_monitoring()
                
                # 执行场景
                result = self.scenario_manager.run_single_scenario(scenario_id)
                
                if result:
                    print("✅ 场景执行完成")
                    self._show_scenario_result(result)
                else:
                    print("❌ 场景执行失败")
                
                # 停止监控
                if self.monitor:
                    self.monitor.stop_monitoring()
            else:
                print("❌ 无效选择")
        except ValueError:
            print("❌ 请输入有效数字")
        except Exception as e:
            print(f"❌ 场景执行失败: {e}")
    
    def _interactive_scenario_group(self):
        """交互式运行场景组"""
        print("\n📦 场景组执行")
        print("-" * 40)
        
        # 获取可用场景组
        groups = self.scenario_manager.get_scenario_groups()
        
        if not groups:
            print("❌ 没有可用的场景组")
            return
        
        # 显示场景组列表
        print("可用场景组:")
        for i, group_name in enumerate(groups, 1):
            group_scenarios = self.scenario_manager.get_group_scenarios(group_name)
            print(f"{i}. {group_name} ({len(group_scenarios)} 个场景)")
        
        try:
            choice = int(input(f"\n请选择场景组 (1-{len(groups)}): ")) - 1
            if 0 <= choice < len(groups):
                group_name = groups[choice]
                print(f"\n🚀 执行场景组: {group_name}")
                
                # 启动监控
                if self.monitor:
                    self.monitor.start_monitoring()
                
                # 执行场景组
                results = self.scenario_manager.run_scenario_group(group_name)
                
                print(f"✅ 场景组执行完成，共 {len(results)} 个场景")
                
                # 显示汇总结果
                self._show_group_results(results)
                
                # 停止监控
                if self.monitor:
                    self.monitor.stop_monitoring()
            else:
                print("❌ 无效选择")
        except ValueError:
            print("❌ 请输入有效数字")
        except Exception as e:
            print(f"❌ 场景组执行失败: {e}")
    
    def _interactive_batch_run(self):
        """交互式批量运行"""
        print("\n🔄 批量执行所有场景")
        print("-" * 40)
        
        confirm = input("确认要执行所有测试场景吗？这可能需要较长时间 (y/N): ").strip().lower()
        
        if confirm in ['y', 'yes']:
            print("\n🚀 开始批量执行...")
            
            # 启动监控
            if self.monitor:
                self.monitor.start_monitoring()
            
            try:
                # 执行所有场景
                results = self.scenario_manager.run_all_scenarios()
                
                print(f"✅ 批量执行完成，共 {len(results)} 个场景")
                
                # 显示汇总结果
                self._show_batch_results(results)
                
                # 自动生成报告
                if REPORT_GENERATOR_AVAILABLE:
                    print("\n📊 自动生成测试报告...")
                    self._generate_comprehensive_report(results)
                
            except Exception as e:
                print(f"❌ 批量执行失败: {e}")
            finally:
                # 停止监控
                if self.monitor:
                    self.monitor.stop_monitoring()
        else:
            print("❌ 批量执行已取消")
    
    def _interactive_view_scenarios(self):
        """交互式查看场景配置"""
        print("\n📋 场景配置查看")
        print("-" * 40)
        
        scenarios = self.scenario_manager.get_available_scenarios()
        
        if not scenarios:
            print("❌ 没有可用的测试场景")
            return
        
        for scenario_id in scenarios:
            scenario_info = self.scenario_manager.get_scenario_info(scenario_id)
            print(f"\n🎯 场景: {scenario_id}")
            print(f"  名称: {scenario_info.get('name', 'Unknown')}")
            print(f"  类型: {scenario_info.get('attack_type', 'Unknown')}")
            print(f"  描述: {scenario_info.get('description', 'No description')}")
            
            config = scenario_info.get('config', {})
            if config:
                print(f"  目标: {config.get('target_url', 'Not specified')}")
                print(f"  并发数: {config.get('concurrent_users', 'Not specified')}")
                print(f"  请求数: {config.get('total_requests', 'Not specified')}")
    
    def _interactive_generate_report(self):
        """交互式生成报告"""
        print("\n📊 测试报告生成")
        print("-" * 40)
        
        if not REPORT_GENERATOR_AVAILABLE:
            print("❌ 报告生成功能不可用，请安装相关依赖")
            return
        
        # 检查是否有测试结果
        results_dir = Path("results")
        if not results_dir.exists() or not list(results_dir.glob("*.json")):
            print("❌ 没有找到测试结果文件")
            print("请先运行测试场景生成结果数据")
            return
        
        print("📁 找到以下测试结果:")
        result_files = list(results_dir.glob("*.json"))
        
        for i, file_path in enumerate(result_files, 1):
            print(f"{i}. {file_path.name}")
        
        try:
            choice = int(input(f"\n请选择结果文件 (1-{len(result_files)}): ")) - 1
            if 0 <= choice < len(result_files):
                result_file = result_files[choice]
                
                # 加载结果数据
                with open(result_file, 'r', encoding='utf-8') as f:
                    scenario_data = json.load(f)
                
                print(f"\n📊 生成报告: {result_file.name}")
                
                # 生成综合报告
                report_files = generate_comprehensive_report(
                    [scenario_data],
                    output_dir="reports",
                    include_charts=True,
                    include_pdf=True
                )
                
                print("✅ 报告生成完成:")
                for report_type, path in report_files.items():
                    print(f"  {report_type}: {path}")
                    
            else:
                print("❌ 无效选择")
        except ValueError:
            print("❌ 请输入有效数字")
        except Exception as e:
            print(f"❌ 报告生成失败: {e}")
    
    def _interactive_system_status(self):
        """交互式系统状态检查"""
        print("\n🔍 系统状态检查")
        print("-" * 40)
        
        # 检查功能可用性
        features = get_available_features()
        
        print("📋 功能模块状态:")
        for feature in features['available']:
            print(f"  ✅ {feature}: 可用")
        
        for feature in features['unavailable']:
            print(f"  ❌ {feature}: 不可用")
        
        # 检查配置文件
        print(f"\n⚙️ 配置管理:")
        print(f"  配置文件: {self.config_manager.config_file}")
        print(f"  配置有效: {'✅' if self.config_manager.is_valid() else '❌'}")
        
        # 检查场景配置
        scenarios = self.scenario_manager.get_available_scenarios()
        print(f"\n🎯 测试场景:")
        print(f"  可用场景数: {len(scenarios)}")
        
        groups = self.scenario_manager.get_scenario_groups()
        print(f"  场景组数: {len(groups)}")
        
        # 检查输出目录
        output_dirs = ["results", "reports", "logs"]
        print(f"\n📁 输出目录:")
        for dir_name in output_dirs:
            dir_path = Path(dir_name)
            exists = dir_path.exists()
            print(f"  {dir_name}: {'✅' if exists else '❌'}")
            if not exists:
                try:
                    dir_path.mkdir(parents=True, exist_ok=True)
                    print(f"    已创建目录: {dir_path}")
                except Exception as e:
                    print(f"    创建失败: {e}")
    
    def _interactive_config_management(self):
        """交互式配置管理"""
        print("\n⚙️ 配置管理")
        print("-" * 40)
        
        print("1. 查看当前配置")
        print("2. 修改配置")
        print("3. 重置配置")
        print("4. 导出配置")
        print("0. 返回主菜单")
        
        choice = input("\n请选择操作 (0-4): ").strip()
        
        if choice == "1":
            self._show_current_config()
        elif choice == "2":
            self._modify_config()
        elif choice == "3":
            self._reset_config()
        elif choice == "4":
            self._export_config()
        elif choice == "0":
            return
        else:
            print("❌ 无效选择")
    
    def _show_current_config(self):
        """显示当前配置"""
        print("\n📋 当前配置:")
        config = self.config_manager.get_all_config()
        
        for section, values in config.items():
            print(f"\n[{section}]")
            if isinstance(values, dict):
                for key, value in values.items():
                    print(f"  {key} = {value}")
            else:
                print(f"  {values}")
    
    def _show_scenario_result(self, result: Dict[str, Any]):
        """显示场景执行结果"""
        print(f"\n📊 场景执行结果:")
        print(f"  场景ID: {result.get('scenario_id', 'Unknown')}")
        print(f"  执行状态: {result.get('status', 'Unknown')}")
        print(f"  执行时间: {result.get('duration', 0):.2f} 秒")
        
        metrics = result.get('metrics', {})
        if metrics:
            print(f"  总请求数: {metrics.get('total_requests', 0)}")
            print(f"  成功请求: {metrics.get('successful_requests', 0)}")
            print(f"  失败请求: {metrics.get('failed_requests', 0)}")
            print(f"  成功率: {metrics.get('successful_requests', 0) / max(metrics.get('total_requests', 1), 1) * 100:.1f}%")
    
    def _show_group_results(self, results: List[Dict[str, Any]]):
        """显示场景组执行结果"""
        print(f"\n📊 场景组执行汇总:")
        
        total_scenarios = len(results)
        successful_scenarios = sum(1 for r in results if r.get('status') == 'completed')
        
        print(f"  总场景数: {total_scenarios}")
        print(f"  成功场景: {successful_scenarios}")
        print(f"  失败场景: {total_scenarios - successful_scenarios}")
        print(f"  成功率: {successful_scenarios / max(total_scenarios, 1) * 100:.1f}%")
        
        # 显示各场景简要信息
        for result in results:
            status_icon = "✅" if result.get('status') == 'completed' else "❌"
            print(f"  {status_icon} {result.get('scenario_id', 'Unknown')}: {result.get('duration', 0):.1f}s")
    
    def _show_batch_results(self, results: List[Dict[str, Any]]):
        """显示批量执行结果"""
        self._show_group_results(results)
        
        # 计算总体统计
        total_requests = sum(r.get('metrics', {}).get('total_requests', 0) for r in results)
        total_successful = sum(r.get('metrics', {}).get('successful_requests', 0) for r in results)
        total_duration = sum(r.get('duration', 0) for r in results)
        
        print(f"\n📈 总体统计:")
        print(f"  总请求数: {total_requests:,}")
        print(f"  成功请求: {total_successful:,}")
        print(f"  总体成功率: {total_successful / max(total_requests, 1) * 100:.1f}%")
        print(f"  总执行时间: {total_duration:.1f} 秒")
        print(f"  平均RPS: {total_requests / max(total_duration, 1):.1f}")
    
    def _register_interface_callbacks(self, interface: InteractiveInterface):
        """
        注册界面回调函数
        
        Args:
            interface: 交互式界面实例
        """
        # 场景执行相关
        interface.register_callback('run_single_scenario', self._callback_run_single_scenario)
        interface.register_callback('run_scenario_group', self._callback_run_scenario_group)
        interface.register_callback('run_batch_scenarios', self._callback_run_batch_scenarios)
        interface.register_callback('run_custom_scenario', self._callback_run_custom_scenario)
        
        # 场景查看相关
        interface.register_callback('list_all_scenarios', self._callback_list_all_scenarios)
        interface.register_callback('view_scenario_details', self._callback_view_scenario_details)
        interface.register_callback('view_scenario_groups', self._callback_view_scenario_groups)
        interface.register_callback('search_scenarios', self._callback_search_scenarios)
        
        # 报告生成相关
        interface.register_callback('generate_latest_report', self._callback_generate_latest_report)
        interface.register_callback('generate_custom_report', self._callback_generate_custom_report)
        interface.register_callback('generate_batch_reports', self._callback_generate_batch_reports)
        interface.register_callback('view_historical_reports', self._callback_view_historical_reports)
        
        # 系统管理相关
        interface.register_callback('check_system_status', self._callback_check_system_status)
        interface.register_callback('check_module_status', self._callback_check_module_status)
        interface.register_callback('cleanup_temp_files', self._callback_cleanup_temp_files)
        interface.register_callback('update_components', self._callback_update_components)
        interface.register_callback('diagnose_system', self._callback_diagnose_system)
        
        # 配置管理相关
        interface.register_callback('view_current_config', self._callback_view_current_config)
        interface.register_callback('modify_config_item', self._callback_modify_config_item)
        interface.register_callback('reset_config', self._callback_reset_config)
        interface.register_callback('import_config', self._callback_import_config)
        interface.register_callback('export_config', self._callback_export_config)
        interface.register_callback('config_wizard', self._callback_config_wizard)
    
    # 回调函数实现
    def _callback_run_single_scenario(self):
        """运行单个场景回调"""
        scenarios = self.scenario_manager.get_available_scenarios()
        
        if not scenarios:
            print("❌ 没有可用的测试场景")
            return
        
        print("可用场景:")
        for i, scenario_id in enumerate(scenarios, 1):
            scenario_info = self.scenario_manager.get_scenario_info(scenario_id)
            print(f"{i}. {scenario_id} - {scenario_info.get('name', 'Unknown')}")
        
        try:
            choice = int(input(f"\n请选择场景 (1-{len(scenarios)}): ")) - 1
            if 0 <= choice < len(scenarios):
                scenario_id = scenarios[choice]
                print(f"\n🚀 执行场景: {scenario_id}")
                
                # 启动监控
                if self.monitor:
                    self.monitor.start_monitoring()
                
                # 执行场景
                result = self.scenario_manager.run_single_scenario(scenario_id)
                
                if result:
                    print("✅ 场景执行完成")
                    self._show_scenario_result(result)
                else:
                    print("❌ 场景执行失败")
                
                # 停止监控
                if self.monitor:
                    self.monitor.stop_monitoring()
            else:
                print("❌ 无效选择")
        except ValueError:
            print("❌ 请输入有效数字")
        except Exception as e:
            print(f"❌ 场景执行失败: {e}")
    
    def _callback_run_scenario_group(self):
        """运行场景组回调"""
        groups = self.scenario_manager.get_scenario_groups()
        
        if not groups:
            print("❌ 没有可用的场景组")
            return
        
        print("可用场景组:")
        for i, group_name in enumerate(groups, 1):
            group_scenarios = self.scenario_manager.get_group_scenarios(group_name)
            print(f"{i}. {group_name} ({len(group_scenarios)} 个场景)")
        
        try:
            choice = int(input(f"\n请选择场景组 (1-{len(groups)}): ")) - 1
            if 0 <= choice < len(groups):
                group_name = groups[choice]
                print(f"\n🚀 执行场景组: {group_name}")
                
                # 启动监控
                if self.monitor:
                    self.monitor.start_monitoring()
                
                # 执行场景组
                results = self.scenario_manager.run_scenario_group(group_name)
                
                print(f"✅ 场景组执行完成，共 {len(results)} 个场景")
                self._show_group_results(results)
                
                # 停止监控
                if self.monitor:
                    self.monitor.stop_monitoring()
            else:
                print("❌ 无效选择")
        except ValueError:
            print("❌ 请输入有效数字")
        except Exception as e:
            print(f"❌ 场景组执行失败: {e}")
    
    def _callback_run_batch_scenarios(self):
        """批量运行场景回调"""
        print("\n🚀 开始批量执行...")
        
        # 启动监控
        if self.monitor:
            self.monitor.start_monitoring()
        
        try:
            # 执行所有场景
            results = self.scenario_manager.run_all_scenarios()
            
            print(f"✅ 批量执行完成，共 {len(results)} 个场景")
            self._show_batch_results(results)
            
            # 自动生成报告
            if REPORT_GENERATOR_AVAILABLE:
                print("\n📊 自动生成测试报告...")
                self._generate_comprehensive_report(results)
            
        except Exception as e:
            print(f"❌ 批量执行失败: {e}")
        finally:
            # 停止监控
            if self.monitor:
                self.monitor.stop_monitoring()
    
    def _callback_run_custom_scenario(self, custom_config: Dict[str, Any]):
        """自定义场景执行回调"""
        print(f"\n🚀 执行自定义场景，配置: {custom_config}")
        # TODO: 实现自定义场景执行逻辑
        print("⚠️  自定义场景执行功能开发中...")
    
    def _callback_list_all_scenarios(self):
        """列出所有场景回调"""
        scenarios = self.scenario_manager.get_available_scenarios()
        
        if not scenarios:
            print("❌ 没有可用的测试场景")
            return
        
        print(f"\n📋 可用场景 (共 {len(scenarios)} 个):")
        print("-" * 80)
        
        for scenario_id in scenarios:
            scenario_info = self.scenario_manager.get_scenario_info(scenario_id)
            print(f"🎯 {scenario_id}")
            print(f"  名称: {scenario_info.get('name', 'Unknown')}")
            print(f"  类型: {scenario_info.get('attack_type', 'Unknown')}")
            print(f"  描述: {scenario_info.get('description', 'No description')}")
            print()
    
    def _callback_view_scenario_details(self, scenario_id: str):
        """查看场景详情回调"""
        scenario_info = self.scenario_manager.get_scenario_info(scenario_id)
        
        if not scenario_info:
            print(f"❌ 场景不存在: {scenario_id}")
            return
        
        print(f"\n📋 场景详情: {scenario_id}")
        print("-" * 60)
        print(f"名称: {scenario_info.get('name', 'Unknown')}")
        print(f"类型: {scenario_info.get('attack_type', 'Unknown')}")
        print(f"描述: {scenario_info.get('description', 'No description')}")
        
        config = scenario_info.get('config', {})
        if config:
            print(f"\n⚙️ 配置参数:")
            for key, value in config.items():
                print(f"  {key}: {value}")
    
    def _callback_view_scenario_groups(self):
        """查看场景组回调"""
        groups = self.scenario_manager.get_scenario_groups()
        
        if not groups:
            print("❌ 没有可用的场景组")
            return
        
        print(f"\n📦 场景组 (共 {len(groups)} 个):")
        print("-" * 60)
        
        for group_name in groups:
            group_scenarios = self.scenario_manager.get_group_scenarios(group_name)
            print(f"📦 {group_name} ({len(group_scenarios)} 个场景)")
            for scenario_id in group_scenarios:
                print(f"  • {scenario_id}")
            print()
    
    def _callback_search_scenarios(self, keyword: str):
        """搜索场景回调"""
        scenarios = self.scenario_manager.get_available_scenarios()
        
        matching_scenarios = []
        for scenario_id in scenarios:
            scenario_info = self.scenario_manager.get_scenario_info(scenario_id)
            
            # 搜索场景ID、名称、描述
            search_text = f"{scenario_id} {scenario_info.get('name', '')} {scenario_info.get('description', '')}"
            if keyword.lower() in search_text.lower():
                matching_scenarios.append((scenario_id, scenario_info))
        
        if not matching_scenarios:
            print(f"❌ 没有找到包含 '{keyword}' 的场景")
            return
        
        print(f"\n🔍 搜索结果 (共 {len(matching_scenarios)} 个):")
        print("-" * 60)
        
        for scenario_id, scenario_info in matching_scenarios:
            print(f"🎯 {scenario_id}")
            print(f"  名称: {scenario_info.get('name', 'Unknown')}")
            print(f"  描述: {scenario_info.get('description', 'No description')}")
            print()
    
    def _callback_generate_latest_report(self):
        """生成最新报告回调"""
        results_dir = Path("results")
        if not results_dir.exists():
            print("❌ 结果目录不存在")
            return
        
        result_files = list(results_dir.glob("*.json"))
        if not result_files:
            print("❌ 没有找到结果文件")
            return
        
        # 找到最新的结果文件
        latest_file = max(result_files, key=lambda f: f.stat().st_mtime)
        
        print(f"📊 生成最新报告: {latest_file.name}")
        self._callback_generate_custom_report(str(latest_file))
    
    def _callback_generate_custom_report(self, file_path: str):
        """生成自定义报告回调"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                scenario_data = json.load(f)
            
            print(f"📊 生成报告: {Path(file_path).name}")
            
            # 生成综合报告
            report_files = generate_comprehensive_report(
                [scenario_data],
                output_dir="reports",
                include_charts=True,
                include_pdf=True
            )
            
            print("✅ 报告生成完成:")
            for report_type, path in report_files.items():
                print(f"  {report_type}: {path}")
                
        except Exception as e:
            print(f"❌ 报告生成失败: {e}")
    
    def _callback_generate_batch_reports(self):
        """批量生成报告回调"""
        results_dir = Path("results")
        if not results_dir.exists():
            print("❌ 结果目录不存在")
            return
        
        result_files = list(results_dir.glob("*.json"))
        if not result_files:
            print("❌ 没有找到结果文件")
            return
        
        print(f"📊 批量生成报告，共 {len(result_files)} 个文件")
        
        for result_file in result_files:
            try:
                print(f"处理: {result_file.name}")
                self._callback_generate_custom_report(str(result_file))
            except Exception as e:
                print(f"❌ 处理 {result_file.name} 失败: {e}")
        
        print("✅ 批量报告生成完成")
    
    def _callback_view_historical_reports(self):
        """查看历史报告回调"""
        reports_dir = Path("reports")
        if not reports_dir.exists():
            print("❌ 报告目录不存在")
            return
        
        report_files = list(reports_dir.glob("*.html")) + list(reports_dir.glob("*.pdf"))
        if not report_files:
            print("❌ 没有找到报告文件")
            return
        
        print(f"\n📊 历史报告 (共 {len(report_files)} 个):")
        print("-" * 60)
        
        for report_file in sorted(report_files, key=lambda f: f.stat().st_mtime, reverse=True):
            file_size = report_file.stat().st_size
            file_time = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime(report_file.stat().st_mtime))
            print(f"📄 {report_file.name}")
            print(f"  大小: {file_size:,} 字节")
            print(f"  时间: {file_time}")
            print()
    
    def _callback_check_system_status(self):
        """检查系统状态回调"""
        print("\n🔍 系统状态检查")
        print("-" * 60)
        
        # 检查功能可用性
        features = get_available_features()
        
        print("📋 功能模块状态:")
        for feature in features['available']:
            print(f"  ✅ {feature}: 可用")
        
        for feature in features['unavailable']:
            print(f"  ❌ {feature}: 不可用")
        
        # 检查配置文件
        print(f"\n⚙️ 配置管理:")
        print(f"  配置文件: {self.config_manager.config_file}")
        print(f"  配置有效: {'✅' if self.config_manager.is_valid() else '❌'}")
        
        # 检查场景配置
        scenarios = self.scenario_manager.get_available_scenarios()
        print(f"\n🎯 测试场景:")
        print(f"  可用场景数: {len(scenarios)}")
        
        groups = self.scenario_manager.get_scenario_groups()
        print(f"  场景组数: {len(groups)}")
        
        # 检查输出目录
        output_dirs = ["results", "reports", "logs"]
        print(f"\n📁 输出目录:")
        for dir_name in output_dirs:
            dir_path = Path(dir_name)
            exists = dir_path.exists()
            print(f"  {dir_name}: {'✅' if exists else '❌'}")
            if not exists:
                try:
                    dir_path.mkdir(parents=True, exist_ok=True)
                    print(f"    已创建目录: {dir_path}")
                except Exception as e:
                    print(f"    创建失败: {e}")
    
    def _callback_check_module_status(self):
        """检查模块状态回调"""
        print("\n🔧 模块状态详细检查")
        print("-" * 60)
        
        modules_to_check = [
            ('core.logger', '日志模块'),
            ('core.config_manager', '配置管理'),
            ('scenarios.scenario_manager', '场景管理'),
            ('utils.report_generator', '报告生成'),
            ('utils.monitor', '系统监控'),
            ('modules.http_client', 'HTTP客户端'),
            ('modules.captcha_solver', '验证码识别'),
            ('modules.anti_detection', '反检测'),
            ('modules.behavior_sim', '行为模拟')
        ]
        
        for module_name, display_name in modules_to_check:
            try:
                __import__(module_name)
                print(f"  ✅ {display_name}: 正常")
            except ImportError as e:
                print(f"  ❌ {display_name}: 导入失败 - {e}")
            except Exception as e:
                print(f"  ⚠️  {display_name}: 异常 - {e}")
    
    def _callback_cleanup_temp_files(self):
        """清理临时文件回调"""
        print("\n🧹 清理临时文件")
        print("-" * 40)
        
        temp_patterns = [
            "*.tmp",
            "*.log.*",
            "__pycache__",
            "*.pyc",
            ".pytest_cache"
        ]
        
        cleaned_count = 0
        cleaned_size = 0
        
        for pattern in temp_patterns:
            for temp_file in Path(".").rglob(pattern):
                try:
                    if temp_file.is_file():
                        file_size = temp_file.stat().st_size
                        temp_file.unlink()
                        cleaned_count += 1
                        cleaned_size += file_size
                        print(f"  删除文件: {temp_file}")
                    elif temp_file.is_dir():
                        import shutil
                        shutil.rmtree(temp_file)
                        cleaned_count += 1
                        print(f"  删除目录: {temp_file}")
                except Exception as e:
                    print(f"  ❌ 删除失败 {temp_file}: {e}")
        
        print(f"\n✅ 清理完成: 删除 {cleaned_count} 个项目，释放 {cleaned_size:,} 字节")
    
    def _callback_update_components(self):
        """更新系统组件回调"""
        print("\n🔄 更新系统组件")
        print("-" * 40)
        print("⚠️  组件更新功能开发中...")
        print("建议手动更新依赖包:")
        print("  pip install --upgrade -r requirements.txt")
    
    def _callback_diagnose_system(self):
        """诊断系统问题回调"""
        print("\n🔍 系统诊断")
        print("-" * 40)
        
        issues_found = []
        
        # 检查Python版本
        if sys.version_info < (3, 7):
            issues_found.append("Python版本过低，建议使用3.7+")
        
        # 检查必要目录
        required_dirs = ["config", "results", "reports", "logs"]
        for dir_name in required_dirs:
            if not Path(dir_name).exists():
                issues_found.append(f"缺少目录: {dir_name}")
        
        # 检查配置文件
        if not self.config_manager.is_valid():
            issues_found.append("配置文件无效或缺失")
        
        # 检查场景文件
        scenarios = self.scenario_manager.get_available_scenarios()
        if not scenarios:
            issues_found.append("没有可用的测试场景")
        
        if issues_found:
            print("❌ 发现以下问题:")
            for issue in issues_found:
                print(f"  • {issue}")
            
            print("\n🔧 建议解决方案:")
            print("  1. 检查安装是否完整")
            print("  2. 运行配置向导重新配置")
            print("  3. 查看日志文件获取详细错误信息")
        else:
            print("✅ 系统状态正常，未发现问题")
    
    def _callback_view_current_config(self):
        """查看当前配置回调"""
        print("\n📋 当前配置:")
        print("-" * 60)
        
        config = self.config_manager.get_all_config()
        
        for section, values in config.items():
            print(f"\n[{section}]")
            if isinstance(values, dict):
                for key, value in values.items():
                    print(f"  {key} = {value}")
            else:
                print(f"  {values}")
    
    def _callback_modify_config_item(self):
        """修改配置项回调"""
        print("\n🔧 修改配置项")
        print("-" * 40)
        print("⚠️  配置修改功能开发中...")
        print("建议使用配置向导进行配置")
    
    def _callback_reset_config(self):
        """重置配置回调"""
        print("\n🔄 重置配置")
        print("-" * 40)
        
        try:
            self.config_manager.reset_to_defaults()
            print("✅ 配置已重置为默认值")
        except Exception as e:
            print(f"❌ 配置重置失败: {e}")
    
    def _callback_import_config(self, file_path: str):
        """导入配置回调"""
        print(f"\n📁 导入配置: {file_path}")
        print("-" * 40)
        
        try:
            self.config_manager.load_from_file(file_path)
            print("✅ 配置导入成功")
        except Exception as e:
            print(f"❌ 配置导入失败: {e}")
    
    def _callback_export_config(self, file_path: str):
        """导出配置回调"""
        print(f"\n💾 导出配置: {file_path}")
        print("-" * 40)
        
        try:
            self.config_manager.save_to_file(file_path)
            print("✅ 配置导出成功")
        except Exception as e:
            print(f"❌ 配置导出失败: {e}")
    
    def _callback_config_wizard(self):
        """配置向导回调"""
        print("\n🧙‍♂️ 启动配置向导")
        print("-" * 40)
        
        wizard = ConfigWizard()
        config_data = wizard.run_wizard()
        
        if config_data:
            try:
                # 更新配置管理器
                for section, values in config_data.items():
                    if isinstance(values, dict):
                        for key, value in values.items():
                            self.config_manager.set(f"{section}.{key}", value)
                    else:
                        self.config_manager.set(section, values)
                
                print("✅ 配置向导完成，新配置已应用")
            except Exception as e:
                print(f"❌ 应用配置失败: {e}")
        else:
            print("❌ 配置向导已取消")
    
    def _generate_comprehensive_report(self, results: List[Dict[str, Any]]):
        """生成综合报告"""
        try:
            report_files = generate_comprehensive_report(
                results,
                output_dir="reports",
                include_charts=True,
                include_pdf=True
            )
            
            print("📊 综合报告生成完成:")
            for report_type, path in report_files.items():
                if isinstance(path, dict):
                    print(f"  {report_type}: {len(path)} 个文件")
                else:
                    print(f"  {report_type}: {path}")
                    
        except Exception as e:
            self.logger.error(f"综合报告生成失败: {e}")
            print(f"❌ 报告生成失败: {e}")
    
    def run_command_line_mode(self, args):
        """运行命令行模式"""
        self.logger.info(f"启动命令行模式: {args}")
        
        try:
            # 根据命令类型分发处理
            if args.command == 'run':
                self._handle_run_command(args)
            elif args.command == 'list':
                self._handle_list_command(args)
            elif args.command == 'report':
                self._handle_report_command(args)
            elif args.command == 'config':
                self._handle_config_command(args)
            elif args.command == 'system':
                self._handle_system_command(args)
            else:
                print("❌ 未指定有效的命令")
                print("使用 --help 查看可用选项")
                
        except Exception as e:
            self.logger.error(f"命令行模式执行失败: {e}")
            print(f"❌ 执行失败: {e}")
    
    def _handle_run_command(self, args):
        """处理运行命令"""
        if args.run_type == 'scenario':
            self._run_single_scenario_cli(args)
        elif args.run_type == 'group':
            self._run_scenario_group_cli(args)
        elif args.run_type == 'batch':
            self._run_batch_scenarios_cli(args)
        elif args.run_type == 'custom':
            self._run_custom_scenario_cli(args)
        else:
            print("❌ 未指定运行类型")
    
    def _handle_list_command(self, args):
        """处理列表命令"""
        if args.list_type == 'scenarios':
            self._list_scenarios_cli(args)
        elif args.list_type == 'groups':
            self._list_groups_cli(args)
        elif args.list_type == 'results':
            self._list_results_cli(args)
        else:
            print("❌ 未指定列表类型")
    
    def _handle_report_command(self, args):
        """处理报告命令"""
        if args.report_action == 'generate':
            self._generate_report_cli(args)
        elif args.report_action == 'batch-generate':
            self._batch_generate_reports_cli(args)
        elif args.report_action == 'view':
            self._view_report_cli(args)
        else:
            print("❌ 未指定报告操作")
    
    def _handle_config_command(self, args):
        """处理配置命令"""
        if args.config_action == 'show':
            self._show_config_cli(args)
        elif args.config_action == 'set':
            self._set_config_cli(args)
        elif args.config_action == 'reset':
            self._reset_config_cli(args)
        elif args.config_action == 'import':
            self._import_config_cli(args)
        elif args.config_action == 'export':
            self._export_config_cli(args)
        elif args.config_action == 'wizard':
            self._config_wizard_cli(args)
        else:
            print("❌ 未指定配置操作")
    
    def _handle_system_command(self, args):
        """处理系统命令"""
        if args.system_action == 'status':
            self._show_system_status_cli(args)
        elif args.system_action == 'cleanup':
            self._cleanup_system_cli(args)
        elif args.system_action == 'diagnose':
            self._diagnose_system_cli(args)
        elif args.system_action == 'update':
            self._update_system_cli(args)
        else:
            print("❌ 未指定系统操作")
    
    def _run_single_scenario_cli(self, scenario_id: str, args):
        """命令行模式运行单个场景"""
        print(f"🚀 执行场景: {scenario_id}")
        
        # 启动监控
        if self.monitor and not args.no_monitor:
            self.monitor.start_monitoring()
        
        try:
            result = self.scenario_manager.run_single_scenario(scenario_id)
            
            if result:
                print("✅ 场景执行完成")
                self._show_scenario_result(result)
                
                # 保存结果
                if args.output:
                    self._save_result(result, args.output)
            else:
                print("❌ 场景执行失败")
                
        finally:
            if self.monitor and not args.no_monitor:
                self.monitor.stop_monitoring()
    
    def _save_result(self, result: Dict[str, Any], output_path: str):
        """保存执行结果"""
        try:
            output_file = Path(output_path)
            output_file.parent.mkdir(parents=True, exist_ok=True)
            
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(result, f, ensure_ascii=False, indent=2)
            
            print(f"💾 结果已保存到: {output_file}")
            
        except Exception as e:
            self.logger.error(f"结果保存失败: {e}")
            print(f"❌ 结果保存失败: {e}")


def main():
    """主函数"""
    try:
        # 创建命令解析器
        command_parser = CommandParser(version="1.0.0")
        
        # 解析命令行参数
        args = command_parser.parse_args()
        
        # 创建测试工具实例
        tool = TicketGrabberTestTool()
        
        # 初始化系统
        config_file = getattr(args, 'config', None)
        log_level = getattr(args, 'log_level', 'INFO')
        
        if not tool.initialize(config_file=config_file, log_level=log_level):
            sys.exit(1)
        
        # 判断运行模式
        if not hasattr(args, 'command') or args.command is None:
            # 无命令，运行交互式模式
            tool.run_interactive_mode()
        else:
            # 有命令，运行命令行模式
            tool.run_command_line_mode(args)
            
    except KeyboardInterrupt:
        print("\n👋 程序被用户中断")
        sys.exit(0)
    except SystemExit:
        # argparse的正常退出
        pass
    except Exception as e:
        print(f"❌ 程序执行失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
报告生成模块简化测试脚本

测试报告生成、图表生成、实时监控等功能
"""

import sys
import time
import random
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_report_generator():
    """测试报告生成器"""
    print("=" * 60)
    print("测试报告生成器")
    print("=" * 60)
    
    try:
        from utils.report_generator import ReportGenerator, create_report_generator
        
        # 创建报告生成器
        generator = create_report_generator("test_reports")
        print("[PASS] 报告生成器创建成功")
        
        # 生成测试数据
        for i in range(2):
            scenario_data = {
                'scenario_id': f'test-scenario-{i+1}',
                'name': f'测试场景 {i+1}',
                'attack_type': ['basic', 'advanced'][i],
                'start_time': time.time() - 100 + i * 30,
                'end_time': time.time() - 70 + i * 30,
                'duration': 30.0,
                'status': 'completed',
                'result': ['success', 'partial'][i],
                'metrics': {
                    'total_requests': 100 + i * 50,
                    'successful_requests': 80 + i * 10,
                    'failed_requests': 15 - i * 5,
                    'blocked_requests': 5 + i * 2,
                    'timeout_requests': 0,
                    'response_times': [random.uniform(0.5, 3.0) for _ in range(10)],
                    'duration': 30.0
                },
                'performance': {
                    'concurrent_users': 10 + i * 5,
                    'peak_concurrent_requests': 20 + i * 10,
                    'cpu_usage_avg': 30.0 + i * 10,
                    'memory_usage_avg': 40.0 + i * 15
                },
                'defense': {
                    'captcha_triggered': i * 5,
                    'captcha_solved': i * 3,
                    'captcha_failed': i * 2,
                    'ip_blocks_detected': i * 2,
                    'rate_limits_hit': i * 3
                },
                'errors': [],
                'warnings': []
            }
            
            generator.add_scenario_data(scenario_data)
        
        print("[PASS] 测试数据添加成功")
        
        # 生成汇总统计
        summary = generator.generate_summary_statistics()
        print(f"[PASS] 汇总统计生成成功 (场景数: {summary['overview']['total_scenarios']})")
        
        # 生成JSON报告
        json_file = generator.generate_json_report()
        print(f"[PASS] JSON报告生成成功: {json_file}")
        
        # 生成HTML报告
        html_file = generator.generate_html_report()
        print(f"[PASS] HTML报告生成成功: {html_file}")
        
        return True
        
    except Exception as e:
        print(f"[FAIL] 报告生成器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_monitor():
    """测试实时监控器"""
    print("\n" + "=" * 60)
    print("测试实时监控器")
    print("=" * 60)
    
    try:
        from utils.monitor import RealTimeMonitor, create_monitor
        
        # 创建监控器
        monitor = create_monitor(update_interval=0.5)
        print("[PASS] 监控器创建成功")
        
        # 启动监控
        monitor.start_monitoring()
        print("[PASS] 监控启动成功")
        
        # 注册测试场景
        monitor.register_scenario('test-001', '测试场景1', 100)
        print("[PASS] 场景注册成功")
        
        # 模拟进度更新
        monitor.update_scenario_progress('test-001', 
                                       completed_requests=50,
                                       successful_requests=45,
                                       failed_requests=5,
                                       response_times=[1.0, 1.2, 0.8, 1.5])
        
        print("[PASS] 进度更新成功")
        
        # 获取场景进度
        progress = monitor.get_scenario_progress()
        print(f"[PASS] 场景进度获取成功 (活动场景: {len(progress)})")
        
        # 停止监控
        monitor.stop_monitoring()
        print("[PASS] 监控停止成功")
        
        return True
        
    except Exception as e:
        print(f"[FAIL] 监控器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_utils_module():
    """测试utils模块"""
    print("\n" + "=" * 60)
    print("测试utils模块")
    print("=" * 60)
    
    try:
        from utils import get_available_features, print_feature_status
        
        # 检查可用功能
        features = get_available_features()
        print(f"[PASS] 功能检查成功")
        print(f"  可用功能: {features['available']}")
        print(f"  不可用功能: {features['unavailable']}")
        
        # 打印功能状态
        print_feature_status()
        print("[PASS] 功能状态打印成功")
        
        return True
        
    except Exception as e:
        print(f"[FAIL] utils模块测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主测试函数"""
    print("开始报告生成模块测试")
    print("=" * 80)
    
    # 创建测试目录
    test_dirs = ["test_reports", "test_charts"]
    for test_dir in test_dirs:
        Path(test_dir).mkdir(exist_ok=True)
    
    # 执行测试
    tests = [
        ("报告生成器", test_report_generator),
        ("实时监控器", test_monitor),
        ("utils模块", test_utils_module)
    ]
    
    results = {}
    for test_name, test_func in tests:
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"[ERROR] {test_name} 测试异常: {e}")
            results[test_name] = False
    
    # 输出测试结果
    print("\n" + "=" * 80)
    print("测试结果汇总")
    print("=" * 80)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results.items():
        status = "PASS" if result else "FAIL"
        print(f"{test_name:<20} [{status}]")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("所有测试通过！报告生成模块工作正常。")
    else:
        print("部分测试失败，请检查相关依赖和配置。")
    
    return passed == total


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
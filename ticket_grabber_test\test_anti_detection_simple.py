"""
反检测技术模块简化测试脚本
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    """测试模块导入"""
    print("测试模块导入...")
    
    try:
        from utils.proxy_pool import ProxyPool
        print("[PASS] ProxyPool导入成功")
    except Exception as e:
        print(f"[FAIL] ProxyPool导入失败: {e}")
        return False
    
    try:
        from modules.anti_detection import UserAgentManager, FingerprintSpoofing
        print("[PASS] 反检测模块导入成功")
    except Exception as e:
        print(f"[FAIL] 反检测模块导入失败: {e}")
        return False
    
    return True

def test_user_agent_basic():
    """测试User-Agent基本功能"""
    print("\n测试User-Agent管理器...")
    
    try:
        from modules.anti_detection import UserAgentManager
        
        ua_manager = UserAgentManager()
        
        # 测试获取随机UA
        ua1 = ua_manager.get_random_ua()
        ua2 = ua_manager.get_random_ua()
        
        print(f"随机UA1: {ua1[:50]}...")
        print(f"随机UA2: {ua2[:50]}...")
        
        # 测试轮询UA
        ua3 = ua_manager.get_next_ua()
        print(f"轮询UA: {ua3[:50]}...")
        
        print("[PASS] User-Agent管理器测试通过")
        return True
        
    except Exception as e:
        print(f"[FAIL] User-Agent管理器测试失败: {e}")
        return False

def test_fingerprint_basic():
    """测试设备指纹基本功能"""
    print("\n测试设备指纹伪造...")
    
    try:
        from modules.anti_detection import FingerprintSpoofing
        
        spoofer = FingerprintSpoofing()
        
        # 生成指纹
        fingerprint = spoofer.generate_random_fingerprint()
        
        print(f"生成指纹:")
        print(f"  屏幕分辨率: {fingerprint.screen_resolution}")
        print(f"  时区: {fingerprint.timezone}")
        print(f"  语言: {fingerprint.language}")
        print(f"  平台: {fingerprint.platform}")
        
        print("[PASS] 设备指纹伪造测试通过")
        return True
        
    except Exception as e:
        print(f"[FAIL] 设备指纹伪造测试失败: {e}")
        return False

def test_proxy_basic():
    """测试代理池基本功能"""
    print("\n测试代理池管理器...")
    
    try:
        from utils.proxy_pool import ProxyPool
        
        # 创建代理池（不自动验证）
        proxy_pool = ProxyPool()
        proxy_pool.auto_validate = False
        proxy_pool.load_proxies()
        
        print(f"加载代理数量: {len(proxy_pool.proxy_list)}")
        
        if proxy_pool.proxy_list:
            # 解析第一个代理
            proxy_info = proxy_pool.parse_proxy_url(proxy_pool.proxy_list[0])
            if proxy_info:
                print(f"解析代理成功: {proxy_info.ip}:{proxy_info.port}")
            else:
                print("代理解析失败")
        
        print("[PASS] 代理池管理器测试通过")
        return True
        
    except Exception as e:
        print(f"[FAIL] 代理池管理器测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("反检测技术模块简化测试")
    print("=" * 40)
    
    tests = [
        ("模块导入", test_imports),
        ("User-Agent管理器", test_user_agent_basic),
        ("设备指纹伪造", test_fingerprint_basic),
        ("代理池管理器", test_proxy_basic)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"[ERROR] {test_name}测试异常: {e}")
    
    print(f"\n{'='*40}")
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("所有测试通过！")
        return True
    else:
        print("部分测试失败")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
"""
代理池管理器

基于研究报告中的ProxyPool类实现，提供代理验证、轮换和故障转移功能。
"""

import requests
import random
import time
import threading
import json
from typing import List, Dict, Optional, Any
from concurrent.futures import ThreadPoolExecutor, as_completed
from dataclasses import dataclass
from queue import Queue, Empty
import os

try:
    from ..core.logger import get_logger
    from ..config.settings import config_manager
except ImportError:
    import logging
    def get_logger(name):
        return logging.getLogger(name)
    
    class SimpleConfig:
        def get(self, key, default=None):
            config_data = {
                'proxy.enable': False,
                'proxy.rotation_interval': 300,
                'proxy.max_failures': 3,
                'proxy.validation_timeout': 5,
                'proxy.proxy_file': 'data/proxies.txt',
                'proxy.auto_validate': True
            }
            return config_data.get(key, default)
    
    config_manager = SimpleConfig()


@dataclass
class ProxyInfo:
    """代理信息数据类"""
    proxy_url: str
    protocol: str  # http, https, socks4, socks5
    ip: str
    port: int
    username: Optional[str] = None
    password: Optional[str] = None
    is_valid: bool = False
    last_validated: Optional[float] = None
    failure_count: int = 0
    response_time: Optional[float] = None
    location: Optional[str] = None
    anonymity_level: Optional[str] = None  # transparent, anonymous, elite


class ProxyPool:
    """
    代理池管理器
    基于研究报告中的ProxyPool类实现
    """
    
    def __init__(self, proxy_file: Optional[str] = None):
        """
        初始化代理池
        
        Args:
            proxy_file: 代理文件路径
        """
        self.logger = get_logger(__name__)
        self.proxy_file = proxy_file or config_manager.get('proxy.proxy_file', 'data/proxies.txt')
        
        # 代理列表
        self.proxy_list: List[str] = []
        self.valid_proxies: List[ProxyInfo] = []
        self.failed_proxies: List[ProxyInfo] = []
        
        # 配置参数
        self.rotation_interval = config_manager.get('proxy.rotation_interval', 300)
        self.max_failures = config_manager.get('proxy.max_failures', 3)
        self.validation_timeout = config_manager.get('proxy.validation_timeout', 5)
        self.auto_validate = config_manager.get('proxy.auto_validate', True)
        
        # 线程安全
        self.lock = threading.RLock()
        self.current_proxy_index = 0
        
        # 统计信息
        self.stats = {
            'total_proxies': 0,
            'valid_proxies': 0,
            'failed_proxies': 0,
            'validation_attempts': 0,
            'successful_validations': 0,
            'total_requests': 0,
            'successful_requests': 0
        }
        
        # 验证测试URL
        self.test_urls = [
            'http://httpbin.org/ip',
            'https://httpbin.org/ip',
            'http://icanhazip.com',
            'https://api.ipify.org?format=json'
        ]
        
        # 加载代理
        self.load_proxies()
        
        # 自动验证
        if self.auto_validate and self.proxy_list:
            self.validate_all_proxies()
        
        self.logger.info(f"代理池初始化完成，总代理数: {len(self.proxy_list)}, 有效代理数: {len(self.valid_proxies)}")
    
    def load_proxies(self, proxy_file: Optional[str] = None):
        """
        从文件加载代理列表
        
        Args:
            proxy_file: 代理文件路径
        """
        file_path = proxy_file or self.proxy_file
        
        if not os.path.exists(file_path):
            self.logger.warning(f"代理文件不存在: {file_path}")
            return
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            self.proxy_list = []
            for line in lines:
                line = line.strip()
                if line and not line.startswith('#'):
                    self.proxy_list.append(line)
            
            self.stats['total_proxies'] = len(self.proxy_list)
            self.logger.info(f"从文件加载了 {len(self.proxy_list)} 个代理")
            
        except Exception as e:
            self.logger.error(f"加载代理文件失败: {e}")
    
    def parse_proxy_url(self, proxy_url: str) -> Optional[ProxyInfo]:
        """
        解析代理URL
        
        Args:
            proxy_url: 代理URL字符串
            
        Returns:
            ProxyInfo: 解析后的代理信息
        """
        try:
            # 支持多种格式:
            # http://ip:port
            # *******************:port
            # socks5://ip:port
            # ip:port (默认http)
            
            if '://' not in proxy_url:
                proxy_url = f'http://{proxy_url}'
            
            from urllib.parse import urlparse
            parsed = urlparse(proxy_url)
            
            return ProxyInfo(
                proxy_url=proxy_url,
                protocol=parsed.scheme or 'http',
                ip=parsed.hostname,
                port=parsed.port or 8080,
                username=parsed.username,
                password=parsed.password
            )
            
        except Exception as e:
            self.logger.error(f"解析代理URL失败: {proxy_url} - {e}")
            return None
    
    def validate_proxy(self, proxy_url: str) -> Optional[ProxyInfo]:
        """
        验证单个代理
        
        Args:
            proxy_url: 代理URL
            
        Returns:
            ProxyInfo: 验证成功返回代理信息，失败返回None
        """
        proxy_info = self.parse_proxy_url(proxy_url)
        if not proxy_info:
            return None
        
        self.stats['validation_attempts'] += 1
        
        # 构建代理字典
        proxy_dict = {
            'http': proxy_url,
            'https': proxy_url
        }
        
        # 测试多个URL
        for test_url in self.test_urls:
            try:
                start_time = time.time()
                
                response = requests.get(
                    test_url,
                    proxies=proxy_dict,
                    timeout=self.validation_timeout,
                    headers={'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'}
                )
                
                response_time = time.time() - start_time
                
                if response.status_code == 200:
                    # 验证成功
                    proxy_info.is_valid = True
                    proxy_info.last_validated = time.time()
                    proxy_info.response_time = response_time
                    
                    # 尝试获取IP信息
                    try:
                        if 'json' in response.headers.get('content-type', ''):
                            data = response.json()
                            if 'origin' in data:
                                proxy_info.location = data.get('origin')
                        else:
                            proxy_info.location = response.text.strip()
                    except:
                        pass
                    
                    self.stats['successful_validations'] += 1
                    self.logger.debug(f"代理验证成功: {proxy_url} - 响应时间: {response_time:.2f}s")
                    return proxy_info
                    
            except requests.exceptions.Timeout:
                self.logger.debug(f"代理验证超时: {proxy_url}")
                continue
                
            except requests.exceptions.ConnectionError:
                self.logger.debug(f"代理连接失败: {proxy_url}")
                continue
                
            except Exception as e:
                self.logger.debug(f"代理验证异常: {proxy_url} - {e}")
                continue
        
        # 所有测试URL都失败
        proxy_info.is_valid = False
        proxy_info.failure_count += 1
        self.logger.debug(f"代理验证失败: {proxy_url}")
        return None
    
    def validate_all_proxies(self, max_workers: int = 50):
        """
        批量验证所有代理
        
        Args:
            max_workers: 最大并发数
        """
        if not self.proxy_list:
            self.logger.warning("没有代理需要验证")
            return
        
        self.logger.info(f"开始验证 {len(self.proxy_list)} 个代理...")
        
        with self.lock:
            self.valid_proxies.clear()
            self.failed_proxies.clear()
        
        start_time = time.time()
        
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # 提交所有验证任务
            future_to_proxy = {
                executor.submit(self.validate_proxy, proxy): proxy 
                for proxy in self.proxy_list
            }
            
            # 收集结果
            for future in as_completed(future_to_proxy):
                proxy_url = future_to_proxy[future]
                try:
                    result = future.result()
                    with self.lock:
                        if result:
                            self.valid_proxies.append(result)
                        else:
                            failed_info = self.parse_proxy_url(proxy_url)
                            if failed_info:
                                failed_info.is_valid = False
                                self.failed_proxies.append(failed_info)
                except Exception as e:
                    self.logger.error(f"验证代理时出现异常: {proxy_url} - {e}")
        
        validation_time = time.time() - start_time
        
        # 更新统计信息
        with self.lock:
            self.stats['valid_proxies'] = len(self.valid_proxies)
            self.stats['failed_proxies'] = len(self.failed_proxies)
        
        self.logger.info(f"代理验证完成，耗时: {validation_time:.2f}s")
        self.logger.info(f"有效代理: {len(self.valid_proxies)}/{len(self.proxy_list)}")
        
        # 按响应时间排序
        if self.valid_proxies:
            self.valid_proxies.sort(key=lambda x: x.response_time or float('inf'))
    
    def get_valid_proxy(self) -> Optional[ProxyInfo]:
        """
        获取一个有效代理（轮询方式）
        
        Returns:
            ProxyInfo: 代理信息，如果没有可用代理返回None
        """
        with self.lock:
            if not self.valid_proxies:
                self.logger.warning("没有可用的有效代理")
                return None
            
            # 轮询获取代理
            proxy_info = self.valid_proxies[self.current_proxy_index]
            self.current_proxy_index = (self.current_proxy_index + 1) % len(self.valid_proxies)
            
            return proxy_info
    
    def get_random_proxy(self) -> Optional[ProxyInfo]:
        """
        随机获取一个有效代理
        
        Returns:
            ProxyInfo: 代理信息，如果没有可用代理返回None
        """
        with self.lock:
            if not self.valid_proxies:
                return None
            
            return random.choice(self.valid_proxies)
    
    def get_fastest_proxy(self) -> Optional[ProxyInfo]:
        """
        获取响应最快的代理
        
        Returns:
            ProxyInfo: 代理信息，如果没有可用代理返回None
        """
        with self.lock:
            if not self.valid_proxies:
                return None
            
            # 返回响应时间最短的代理
            return min(self.valid_proxies, key=lambda x: x.response_time or float('inf'))
    
    def mark_proxy_failed(self, proxy_info: ProxyInfo):
        """
        标记代理失败
        
        Args:
            proxy_info: 代理信息
        """
        with self.lock:
            proxy_info.failure_count += 1
            
            # 如果失败次数超过阈值，移除代理
            if proxy_info.failure_count >= self.max_failures:
                if proxy_info in self.valid_proxies:
                    self.valid_proxies.remove(proxy_info)
                    self.failed_proxies.append(proxy_info)
                    self.logger.warning(f"代理失败次数过多，已移除: {proxy_info.proxy_url}")
    
    def get_proxy_dict(self, proxy_info: ProxyInfo) -> Dict[str, str]:
        """
        获取requests库使用的代理字典
        
        Args:
            proxy_info: 代理信息
            
        Returns:
            Dict[str, str]: 代理字典
        """
        return {
            'http': proxy_info.proxy_url,
            'https': proxy_info.proxy_url
        }
    
    def test_proxy_with_request(self, proxy_info: ProxyInfo, test_url: str = None) -> bool:
        """
        使用实际请求测试代理
        
        Args:
            proxy_info: 代理信息
            test_url: 测试URL
            
        Returns:
            bool: 测试是否成功
        """
        test_url = test_url or self.test_urls[0]
        proxy_dict = self.get_proxy_dict(proxy_info)
        
        try:
            self.stats['total_requests'] += 1
            
            response = requests.get(
                test_url,
                proxies=proxy_dict,
                timeout=self.validation_timeout,
                headers={'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'}
            )
            
            if response.status_code == 200:
                self.stats['successful_requests'] += 1
                return True
            else:
                self.mark_proxy_failed(proxy_info)
                return False
                
        except Exception as e:
            self.logger.debug(f"代理请求测试失败: {proxy_info.proxy_url} - {e}")
            self.mark_proxy_failed(proxy_info)
            return False
    
    def get_stats(self) -> Dict[str, Any]:
        """获取代理池统计信息"""
        with self.lock:
            stats = self.stats.copy()
            stats.update({
                'current_valid_proxies': len(self.valid_proxies),
                'current_failed_proxies': len(self.failed_proxies),
                'success_rate': self.stats['successful_requests'] / max(self.stats['total_requests'], 1),
                'validation_success_rate': self.stats['successful_validations'] / max(self.stats['validation_attempts'], 1)
            })
            
            if self.valid_proxies:
                response_times = [p.response_time for p in self.valid_proxies if p.response_time]
                if response_times:
                    stats['average_response_time'] = sum(response_times) / len(response_times)
                    stats['fastest_response_time'] = min(response_times)
                    stats['slowest_response_time'] = max(response_times)
            
            return stats
    
    def refresh_proxies(self):
        """刷新代理池"""
        self.logger.info("刷新代理池...")
        self.load_proxies()
        if self.auto_validate:
            self.validate_all_proxies()
    
    def save_valid_proxies(self, filename: str = None):
        """
        保存有效代理到文件
        
        Args:
            filename: 保存文件名
        """
        filename = filename or 'data/valid_proxies.txt'
        
        try:
            os.makedirs(os.path.dirname(filename), exist_ok=True)
            
            with open(filename, 'w', encoding='utf-8') as f:
                for proxy in self.valid_proxies:
                    f.write(f"{proxy.proxy_url}\n")
            
            self.logger.info(f"已保存 {len(self.valid_proxies)} 个有效代理到 {filename}")
            
        except Exception as e:
            self.logger.error(f"保存有效代理失败: {e}")


# 全局代理池实例
proxy_pool: Optional[ProxyPool] = None


def get_proxy_pool() -> ProxyPool:
    """获取全局代理池实例"""
    global proxy_pool
    if proxy_pool is None:
        proxy_pool = ProxyPool()
    return proxy_pool


def initialize_proxy_pool(proxy_file: str = None) -> ProxyPool:
    """
    初始化全局代理池
    
    Args:
        proxy_file: 代理文件路径
        
    Returns:
        ProxyPool: 代理池实例
    """
    global proxy_pool
    proxy_pool = ProxyPool(proxy_file)
    return proxy_pool
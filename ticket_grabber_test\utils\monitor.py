"""
实时监控模块

提供测试过程中的实时监控和进度显示功能
"""

import time
import threading
import queue
import psutil
from typing import Dict, List, Any, Optional, Callable
from datetime import datetime, timedelta
from dataclasses import dataclass, asdict
from collections import deque

try:
    from ..core.logger import get_logger
except ImportError:
    import logging
    def get_logger(name):
        return logging.getLogger(name)


@dataclass
class SystemMetrics:
    """系统指标"""
    timestamp: float
    cpu_percent: float
    memory_percent: float
    memory_used_mb: float
    memory_available_mb: float
    disk_io_read_mb: float
    disk_io_write_mb: float
    network_sent_mb: float
    network_recv_mb: float
    active_connections: int


@dataclass
class TestProgress:
    """测试进度"""
    scenario_id: str
    scenario_name: str
    total_requests: int
    completed_requests: int
    successful_requests: int
    failed_requests: int
    blocked_requests: int
    current_rps: float  # 当前每秒请求数
    avg_response_time: float
    start_time: float
    elapsed_time: float
    estimated_remaining: float
    progress_percent: float


class RealTimeMonitor:
    """
    实时监控器
    
    监控系统性能和测试进度
    """
    
    def __init__(self, update_interval: float = 1.0):
        """
        初始化监控器
        
        Args:
            update_interval: 更新间隔（秒）
        """
        self.logger = get_logger("RealTimeMonitor")
        self.update_interval = update_interval
        
        # 监控状态
        self.is_monitoring = False
        self.monitor_thread: Optional[threading.Thread] = None
        
        # 数据存储
        self.system_metrics_history = deque(maxlen=300)  # 保留5分钟数据
        self.test_progress_data: Dict[str, TestProgress] = {}
        
        # 事件队列
        self.event_queue = queue.Queue()
        
        # 回调函数
        self.on_metrics_update: Optional[Callable] = None
        self.on_progress_update: Optional[Callable] = None
        self.on_alert: Optional[Callable] = None
        
        # 警报阈值
        self.alert_thresholds = {
            'cpu_percent': 80.0,
            'memory_percent': 85.0,
            'response_time': 10.0,
            'error_rate': 50.0
        }
        
        # 基线指标（用于计算网络和磁盘IO增量）
        self.baseline_metrics = None
        
        self.logger.info("实时监控器初始化完成")
    
    def start_monitoring(self):
        """开始监控"""
        if self.is_monitoring:
            self.logger.warning("监控已在运行")
            return
        
        self.is_monitoring = True
        self.baseline_metrics = self._get_baseline_metrics()
        
        self.monitor_thread = threading.Thread(target=self._monitor_loop, daemon=True)
        self.monitor_thread.start()
        
        self.logger.info("实时监控已启动")
    
    def stop_monitoring(self):
        """停止监控"""
        if not self.is_monitoring:
            return
        
        self.is_monitoring = False
        
        if self.monitor_thread and self.monitor_thread.is_alive():
            self.monitor_thread.join(timeout=5)
        
        self.logger.info("实时监控已停止")
    
    def _get_baseline_metrics(self) -> Dict[str, float]:
        """获取基线指标"""
        try:
            net_io = psutil.net_io_counters()
            disk_io = psutil.disk_io_counters()
            
            return {
                'net_sent': net_io.bytes_sent / 1024 / 1024,  # MB
                'net_recv': net_io.bytes_recv / 1024 / 1024,  # MB
                'disk_read': disk_io.read_bytes / 1024 / 1024,  # MB
                'disk_write': disk_io.write_bytes / 1024 / 1024  # MB
            }
        except Exception as e:
            self.logger.error(f"获取基线指标失败: {e}")
            return {
                'net_sent': 0, 'net_recv': 0,
                'disk_read': 0, 'disk_write': 0
            }
    
    def _monitor_loop(self):
        """监控循环"""
        while self.is_monitoring:
            try:
                # 收集系统指标
                metrics = self._collect_system_metrics()
                if metrics:
                    self.system_metrics_history.append(metrics)
                    
                    # 检查警报
                    self._check_alerts(metrics)
                    
                    # 调用回调
                    if self.on_metrics_update:
                        self.on_metrics_update(metrics)
                
                # 更新测试进度
                self._update_test_progress()
                
                time.sleep(self.update_interval)
                
            except Exception as e:
                self.logger.error(f"监控循环错误: {e}")
                time.sleep(self.update_interval)
    
    def _collect_system_metrics(self) -> Optional[SystemMetrics]:
        """收集系统指标"""
        try:
            # CPU和内存
            cpu_percent = psutil.cpu_percent(interval=None)
            memory = psutil.virtual_memory()
            
            # 网络IO
            net_io = psutil.net_io_counters()
            net_sent_mb = net_io.bytes_sent / 1024 / 1024
            net_recv_mb = net_io.bytes_recv / 1024 / 1024
            
            # 磁盘IO
            disk_io = psutil.disk_io_counters()
            disk_read_mb = disk_io.read_bytes / 1024 / 1024
            disk_write_mb = disk_io.write_bytes / 1024 / 1024
            
            # 网络连接数
            try:
                connections = len(psutil.net_connections())
            except (psutil.AccessDenied, OSError):
                connections = 0
            
            # 计算增量（相对于基线）
            if self.baseline_metrics:
                net_sent_delta = net_sent_mb - self.baseline_metrics['net_sent']
                net_recv_delta = net_recv_mb - self.baseline_metrics['net_recv']
                disk_read_delta = disk_read_mb - self.baseline_metrics['disk_read']
                disk_write_delta = disk_write_mb - self.baseline_metrics['disk_write']
            else:
                net_sent_delta = net_recv_delta = disk_read_delta = disk_write_delta = 0
            
            return SystemMetrics(
                timestamp=time.time(),
                cpu_percent=cpu_percent,
                memory_percent=memory.percent,
                memory_used_mb=memory.used / 1024 / 1024,
                memory_available_mb=memory.available / 1024 / 1024,
                disk_io_read_mb=disk_read_delta,
                disk_io_write_mb=disk_write_delta,
                network_sent_mb=net_sent_delta,
                network_recv_mb=net_recv_delta,
                active_connections=connections
            )
            
        except Exception as e:
            self.logger.error(f"收集系统指标失败: {e}")
            return None
    
    def _update_test_progress(self):
        """更新测试进度"""
        for scenario_id, progress in self.test_progress_data.items():
            # 计算当前RPS
            current_time = time.time()
            elapsed = current_time - progress.start_time
            
            if elapsed > 0:
                progress.current_rps = progress.completed_requests / elapsed
                progress.elapsed_time = elapsed
                
                # 估算剩余时间
                if progress.completed_requests > 0 and progress.total_requests > progress.completed_requests:
                    remaining_requests = progress.total_requests - progress.completed_requests
                    estimated_remaining = remaining_requests / progress.current_rps if progress.current_rps > 0 else 0
                    progress.estimated_remaining = estimated_remaining
                else:
                    progress.estimated_remaining = 0
                
                # 计算进度百分比
                progress.progress_percent = (progress.completed_requests / progress.total_requests * 100) if progress.total_requests > 0 else 0
            
            # 调用进度更新回调
            if self.on_progress_update:
                self.on_progress_update(progress)
    
    def _check_alerts(self, metrics: SystemMetrics):
        """检查警报条件"""
        alerts = []
        
        # CPU警报
        if metrics.cpu_percent > self.alert_thresholds['cpu_percent']:
            alerts.append({
                'type': 'cpu_high',
                'message': f'CPU使用率过高: {metrics.cpu_percent:.1f}%',
                'severity': 'warning',
                'value': metrics.cpu_percent,
                'threshold': self.alert_thresholds['cpu_percent']
            })
        
        # 内存警报
        if metrics.memory_percent > self.alert_thresholds['memory_percent']:
            alerts.append({
                'type': 'memory_high',
                'message': f'内存使用率过高: {metrics.memory_percent:.1f}%',
                'severity': 'warning',
                'value': metrics.memory_percent,
                'threshold': self.alert_thresholds['memory_percent']
            })
        
        # 检查测试进度警报
        for scenario_id, progress in self.test_progress_data.items():
            # 响应时间警报
            if progress.avg_response_time > self.alert_thresholds['response_time']:
                alerts.append({
                    'type': 'response_time_high',
                    'message': f'场景 {progress.scenario_name} 响应时间过长: {progress.avg_response_time:.2f}s',
                    'severity': 'warning',
                    'scenario_id': scenario_id,
                    'value': progress.avg_response_time,
                    'threshold': self.alert_thresholds['response_time']
                })
            
            # 错误率警报
            if progress.completed_requests > 0:
                error_rate = (progress.failed_requests + progress.blocked_requests) / progress.completed_requests * 100
                if error_rate > self.alert_thresholds['error_rate']:
                    alerts.append({
                        'type': 'error_rate_high',
                        'message': f'场景 {progress.scenario_name} 错误率过高: {error_rate:.1f}%',
                        'severity': 'error',
                        'scenario_id': scenario_id,
                        'value': error_rate,
                        'threshold': self.alert_thresholds['error_rate']
                    })
        
        # 发送警报
        for alert in alerts:
            if self.on_alert:
                self.on_alert(alert)
    
    def register_scenario(self, scenario_id: str, scenario_name: str, total_requests: int):
        """注册测试场景"""
        self.test_progress_data[scenario_id] = TestProgress(
            scenario_id=scenario_id,
            scenario_name=scenario_name,
            total_requests=total_requests,
            completed_requests=0,
            successful_requests=0,
            failed_requests=0,
            blocked_requests=0,
            current_rps=0.0,
            avg_response_time=0.0,
            start_time=time.time(),
            elapsed_time=0.0,
            estimated_remaining=0.0,
            progress_percent=0.0
        )
        
        self.logger.info(f"注册测试场景: {scenario_name} ({scenario_id[:8]})")
    
    def update_scenario_progress(self, scenario_id: str, **kwargs):
        """更新场景进度"""
        if scenario_id not in self.test_progress_data:
            self.logger.warning(f"未知场景ID: {scenario_id}")
            return
        
        progress = self.test_progress_data[scenario_id]
        
        # 更新字段
        for key, value in kwargs.items():
            if hasattr(progress, key):
                setattr(progress, key, value)
        
        # 重新计算平均响应时间（如果提供了响应时间列表）
        if 'response_times' in kwargs:
            response_times = kwargs['response_times']
            if response_times:
                progress.avg_response_time = sum(response_times) / len(response_times)
    
    def unregister_scenario(self, scenario_id: str):
        """注销测试场景"""
        if scenario_id in self.test_progress_data:
            scenario_name = self.test_progress_data[scenario_id].scenario_name
            del self.test_progress_data[scenario_id]
            self.logger.info(f"注销测试场景: {scenario_name} ({scenario_id[:8]})")
    
    def get_current_metrics(self) -> Optional[SystemMetrics]:
        """获取当前系统指标"""
        if self.system_metrics_history:
            return self.system_metrics_history[-1]
        return None
    
    def get_metrics_history(self, duration_minutes: int = 5) -> List[SystemMetrics]:
        """获取指定时间段的指标历史"""
        cutoff_time = time.time() - (duration_minutes * 60)
        return [m for m in self.system_metrics_history if m.timestamp >= cutoff_time]
    
    def get_scenario_progress(self, scenario_id: str = None) -> Dict[str, Any]:
        """获取场景进度"""
        if scenario_id:
            if scenario_id in self.test_progress_data:
                return asdict(self.test_progress_data[scenario_id])
            else:
                return {}
        else:
            return {sid: asdict(progress) for sid, progress in self.test_progress_data.items()}
    
    def get_summary_stats(self) -> Dict[str, Any]:
        """获取汇总统计"""
        current_metrics = self.get_current_metrics()
        
        # 系统统计
        system_stats = {}
        if current_metrics:
            system_stats = {
                'cpu_percent': current_metrics.cpu_percent,
                'memory_percent': current_metrics.memory_percent,
                'memory_used_mb': current_metrics.memory_used_mb,
                'active_connections': current_metrics.active_connections
            }
        
        # 测试统计
        test_stats = {
            'active_scenarios': len(self.test_progress_data),
            'total_requests': sum(p.total_requests for p in self.test_progress_data.values()),
            'completed_requests': sum(p.completed_requests for p in self.test_progress_data.values()),
            'successful_requests': sum(p.successful_requests for p in self.test_progress_data.values()),
            'failed_requests': sum(p.failed_requests for p in self.test_progress_data.values()),
            'blocked_requests': sum(p.blocked_requests for p in self.test_progress_data.values())
        }
        
        # 计算总体成功率
        if test_stats['completed_requests'] > 0:
            test_stats['success_rate'] = test_stats['successful_requests'] / test_stats['completed_requests']
            test_stats['error_rate'] = (test_stats['failed_requests'] + test_stats['blocked_requests']) / test_stats['completed_requests']
        else:
            test_stats['success_rate'] = 0.0
            test_stats['error_rate'] = 0.0
        
        # 计算平均RPS
        total_rps = sum(p.current_rps for p in self.test_progress_data.values())
        test_stats['total_rps'] = total_rps
        
        return {
            'timestamp': time.time(),
            'system': system_stats,
            'test': test_stats
        }
    
    def export_metrics(self, filepath: str):
        """导出指标数据"""
        try:
            import json
            
            data = {
                'export_time': datetime.now().isoformat(),
                'system_metrics': [asdict(m) for m in self.system_metrics_history],
                'test_progress': {sid: asdict(p) for sid, p in self.test_progress_data.items()},
                'summary': self.get_summary_stats()
            }
            
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
            
            self.logger.info(f"指标数据导出成功: {filepath}")
            
        except Exception as e:
            self.logger.error(f"指标数据导出失败: {e}")
    
    def set_alert_threshold(self, metric: str, threshold: float):
        """设置警报阈值"""
        if metric in self.alert_thresholds:
            self.alert_thresholds[metric] = threshold
            self.logger.info(f"警报阈值已更新: {metric} = {threshold}")
        else:
            self.logger.warning(f"未知指标: {metric}")
    
    def clear_history(self):
        """清空历史数据"""
        self.system_metrics_history.clear()
        self.test_progress_data.clear()
        self.logger.info("历史数据已清空")


class ConsoleDisplay:
    """
    控制台显示器
    
    在控制台显示实时监控信息
    """
    
    def __init__(self, monitor: RealTimeMonitor, update_interval: float = 2.0):
        """
        初始化控制台显示器
        
        Args:
            monitor: 监控器实例
            update_interval: 显示更新间隔
        """
        self.monitor = monitor
        self.update_interval = update_interval
        self.is_displaying = False
        self.display_thread: Optional[threading.Thread] = None
        
        # 设置监控器回调
        self.monitor.on_alert = self._handle_alert
    
    def start_display(self):
        """开始显示"""
        if self.is_displaying:
            return
        
        self.is_displaying = True
        self.display_thread = threading.Thread(target=self._display_loop, daemon=True)
        self.display_thread.start()
    
    def stop_display(self):
        """停止显示"""
        self.is_displaying = False
        if self.display_thread and self.display_thread.is_alive():
            self.display_thread.join(timeout=3)
    
    def _display_loop(self):
        """显示循环"""
        while self.is_displaying:
            try:
                self._update_display()
                time.sleep(self.update_interval)
            except Exception as e:
                print(f"显示错误: {e}")
                time.sleep(self.update_interval)
    
    def _update_display(self):
        """更新显示"""
        # 清屏（简单方式）
        import os
        os.system('cls' if os.name == 'nt' else 'clear')
        
        print("=" * 80)
        print("实时监控仪表板")
        print("=" * 80)
        
        # 显示系统指标
        current_metrics = self.monitor.get_current_metrics()
        if current_metrics:
            print(f"\n系统指标 ({datetime.fromtimestamp(current_metrics.timestamp).strftime('%H:%M:%S')}):")
            print(f"  CPU使用率: {current_metrics.cpu_percent:6.1f}%")
            print(f"  内存使用率: {current_metrics.memory_percent:6.1f}%")
            print(f"  内存使用量: {current_metrics.memory_used_mb:8.1f} MB")
            print(f"  网络发送: {current_metrics.network_sent_mb:8.1f} MB")
            print(f"  网络接收: {current_metrics.network_recv_mb:8.1f} MB")
            print(f"  活动连接: {current_metrics.active_connections:6d}")
        
        # 显示测试进度
        progress_data = self.monitor.get_scenario_progress()
        if progress_data:
            print(f"\n测试进度:")
            print(f"{'场景名称':<20} {'进度':<8} {'RPS':<8} {'成功率':<8} {'响应时间':<10}")
            print("-" * 70)
            
            for scenario_id, progress in progress_data.items():
                success_rate = (progress['successful_requests'] / progress['completed_requests'] * 100) if progress['completed_requests'] > 0 else 0
                
                print(f"{progress['scenario_name'][:19]:<20} "
                      f"{progress['progress_percent']:6.1f}% "
                      f"{progress['current_rps']:6.1f} "
                      f"{success_rate:6.1f}% "
                      f"{progress['avg_response_time']:8.2f}s")
        
        # 显示汇总统计
        summary = self.monitor.get_summary_stats()
        test_stats = summary.get('test', {})
        
        print(f"\n汇总统计:")
        print(f"  活动场景: {test_stats.get('active_scenarios', 0)}")
        print(f"  总请求数: {test_stats.get('total_requests', 0):,}")
        print(f"  已完成: {test_stats.get('completed_requests', 0):,}")
        print(f"  成功率: {test_stats.get('success_rate', 0):.1%}")
        print(f"  总RPS: {test_stats.get('total_rps', 0):.1f}")
        
        print(f"\n按 Ctrl+C 停止监控")
    
    def _handle_alert(self, alert: Dict[str, Any]):
        """处理警报"""
        severity_symbols = {
            'info': 'ℹ',
            'warning': '⚠',
            'error': '❌',
            'critical': '🚨'
        }
        
        symbol = severity_symbols.get(alert.get('severity', 'info'), 'ℹ')
        timestamp = datetime.now().strftime('%H:%M:%S')
        
        print(f"\n{symbol} [{timestamp}] {alert['message']}")


# 便捷函数
def create_monitor(update_interval: float = 1.0) -> RealTimeMonitor:
    """创建监控器"""
    return RealTimeMonitor(update_interval)


def start_console_monitoring(monitor: RealTimeMonitor = None, update_interval: float = 2.0):
    """启动控制台监控"""
    if monitor is None:
        monitor = create_monitor()
    
    display = ConsoleDisplay(monitor, update_interval)
    
    try:
        monitor.start_monitoring()
        display.start_display()
        
        # 保持运行直到用户中断
        while True:
            time.sleep(1)
            
    except KeyboardInterrupt:
        print("\n\n停止监控...")
        display.stop_display()
        monitor.stop_monitoring()
        print("监控已停止")
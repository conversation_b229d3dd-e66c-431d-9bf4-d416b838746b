"""
命令解析器模块

提供命令行参数解析、验证和处理功能
"""

import argparse
import sys
from typing import Dict, Any, List, Optional, Tuple
from pathlib import Path

try:
    from ..core.logger import get_logger
except ImportError:
    import logging
    def get_logger(name):
        return logging.getLogger(name)


class CommandParser:
    """
    命令解析器类
    
    提供完整的命令行参数解析和验证功能
    """
    
    def __init__(self, version: str = "1.0.0"):
        """
        初始化命令解析器
        
        Args:
            version: 程序版本
        """
        self.logger = get_logger("CommandParser")
        self.version = version
        self.parser = None
        self.subparsers = None
        
        # 创建解析器
        self._create_parser()
    
    def _create_parser(self):
        """创建参数解析器"""
        self.parser = argparse.ArgumentParser(
            prog="ticket_grabber_test",
            description="抢票系统安全测试工具 - 用于测试票务系统防护手段",
            formatter_class=argparse.RawDescriptionHelpFormatter,
            epilog=self._get_epilog_text()
        )
        
        # 添加基本参数
        self._add_basic_arguments()
        
        # 添加子命令
        self._add_subcommands()
    
    def _get_epilog_text(self) -> str:
        """获取帮助文本尾部"""
        return """
使用示例:
  # 交互式模式
  python main.py
  
  # 运行单个场景
  python main.py run scenario -s basic-attack
  
  # 运行场景组
  python main.py run group -g basic-scenarios
  
  # 批量运行所有场景
  python main.py run batch
  
  # 列出所有场景
  python main.py list scenarios
  
  # 生成报告
  python main.py report generate -f results/test.json
  
  # 系统状态检查
  python main.py system status
  
  # 配置向导
  python main.py config wizard

注意事项:
  • 本工具仅用于安全研究和防护测试目的
  • 请确保在授权环境中使用
  • 遵守相关法律法规和道德准则

更多信息请访问: https://github.com/your-repo/ticket-grabber-test
        """
    
    def _add_basic_arguments(self):
        """添加基本参数"""
        # 版本信息
        self.parser.add_argument(
            '-v', '--version',
            action='version',
            version=f'%(prog)s {self.version}'
        )
        
        # 配置文件
        self.parser.add_argument(
            '--config',
            type=str,
            metavar='FILE',
            help='指定配置文件路径'
        )
        
        # 日志级别
        self.parser.add_argument(
            '--log-level',
            choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'],
            default='INFO',
            help='设置日志级别 (默认: INFO)'
        )
        
        # 输出目录
        self.parser.add_argument(
            '--output-dir',
            type=str,
            metavar='DIR',
            default='results',
            help='设置输出目录 (默认: results)'
        )
        
        # 静默模式
        self.parser.add_argument(
            '--quiet', '-q',
            action='store_true',
            help='静默模式，减少输出信息'
        )
        
        # 详细模式
        self.parser.add_argument(
            '--verbose',
            action='store_true',
            help='详细模式，显示更多信息'
        )
        
        # 禁用颜色
        self.parser.add_argument(
            '--no-color',
            action='store_true',
            help='禁用彩色输出'
        )
    
    def _add_subcommands(self):
        """添加子命令"""
        self.subparsers = self.parser.add_subparsers(
            dest='command',
            help='可用命令',
            metavar='COMMAND'
        )
        
        # 运行命令
        self._add_run_command()
        
        # 列表命令
        self._add_list_command()
        
        # 报告命令
        self._add_report_command()
        
        # 配置命令
        self._add_config_command()
        
        # 系统命令
        self._add_system_command()
    
    def _add_run_command(self):
        """添加运行命令"""
        run_parser = self.subparsers.add_parser(
            'run',
            help='运行测试场景',
            description='运行各种类型的测试场景'
        )
        
        run_subparsers = run_parser.add_subparsers(
            dest='run_type',
            help='运行类型',
            metavar='TYPE'
        )
        
        # 运行单个场景
        scenario_parser = run_subparsers.add_parser(
            'scenario',
            help='运行单个场景'
        )
        scenario_parser.add_argument(
            '-s', '--scenario',
            type=str,
            required=True,
            help='场景ID'
        )
        scenario_parser.add_argument(
            '--target',
            type=str,
            help='覆盖目标URL'
        )
        scenario_parser.add_argument(
            '--concurrent',
            type=int,
            help='覆盖并发用户数'
        )
        scenario_parser.add_argument(
            '--requests',
            type=int,
            help='覆盖总请求数'
        )
        scenario_parser.add_argument(
            '--timeout',
            type=int,
            help='覆盖请求超时时间(秒)'
        )
        scenario_parser.add_argument(
            '--no-monitor',
            action='store_true',
            help='禁用实时监控'
        )
        
        # 运行场景组
        group_parser = run_subparsers.add_parser(
            'group',
            help='运行场景组'
        )
        group_parser.add_argument(
            '-g', '--group',
            type=str,
            required=True,
            help='场景组名称'
        )
        group_parser.add_argument(
            '--parallel',
            action='store_true',
            help='并行执行场景'
        )
        group_parser.add_argument(
            '--max-workers',
            type=int,
            default=4,
            help='最大并行工作线程数 (默认: 4)'
        )
        
        # 批量运行
        batch_parser = run_subparsers.add_parser(
            'batch',
            help='批量运行所有场景'
        )
        batch_parser.add_argument(
            '--filter',
            type=str,
            help='场景过滤条件'
        )
        batch_parser.add_argument(
            '--exclude',
            type=str,
            nargs='*',
            help='排除的场景ID'
        )
        batch_parser.add_argument(
            '--dry-run',
            action='store_true',
            help='试运行，不实际执行'
        )
        
        # 自定义运行
        custom_parser = run_subparsers.add_parser(
            'custom',
            help='自定义场景运行'
        )
        custom_parser.add_argument(
            '--target',
            type=str,
            required=True,
            help='目标URL'
        )
        custom_parser.add_argument(
            '--concurrent',
            type=int,
            default=10,
            help='并发用户数 (默认: 10)'
        )
        custom_parser.add_argument(
            '--requests',
            type=int,
            default=100,
            help='总请求数 (默认: 100)'
        )
        custom_parser.add_argument(
            '--attack-type',
            choices=['basic', 'advanced', 'distributed', 'adversarial'],
            default='basic',
            help='攻击类型 (默认: basic)'
        )
    
    def _add_list_command(self):
        """添加列表命令"""
        list_parser = self.subparsers.add_parser(
            'list',
            help='列出信息',
            description='列出各种系统信息'
        )
        
        list_subparsers = list_parser.add_subparsers(
            dest='list_type',
            help='列表类型',
            metavar='TYPE'
        )
        
        # 列出场景
        scenarios_parser = list_subparsers.add_parser(
            'scenarios',
            help='列出所有场景'
        )
        scenarios_parser.add_argument(
            '--group',
            type=str,
            help='按组过滤'
        )
        scenarios_parser.add_argument(
            '--type',
            type=str,
            help='按类型过滤'
        )
        scenarios_parser.add_argument(
            '--format',
            choices=['table', 'json', 'yaml'],
            default='table',
            help='输出格式 (默认: table)'
        )
        
        # 列出场景组
        groups_parser = list_subparsers.add_parser(
            'groups',
            help='列出所有场景组'
        )
        groups_parser.add_argument(
            '--details',
            action='store_true',
            help='显示详细信息'
        )
        
        # 列出结果文件
        results_parser = list_subparsers.add_parser(
            'results',
            help='列出结果文件'
        )
        results_parser.add_argument(
            '--recent',
            type=int,
            help='显示最近N个结果'
        )
        results_parser.add_argument(
            '--pattern',
            type=str,
            help='文件名模式匹配'
        )
    
    def _add_report_command(self):
        """添加报告命令"""
        report_parser = self.subparsers.add_parser(
            'report',
            help='报告管理',
            description='生成和管理测试报告'
        )
        
        report_subparsers = report_parser.add_subparsers(
            dest='report_action',
            help='报告操作',
            metavar='ACTION'
        )
        
        # 生成报告
        generate_parser = report_subparsers.add_parser(
            'generate',
            help='生成报告'
        )
        generate_parser.add_argument(
            '-f', '--file',
            type=str,
            help='结果文件路径'
        )
        generate_parser.add_argument(
            '--format',
            choices=['json', 'html', 'pdf', 'all'],
            default='all',
            help='报告格式 (默认: all)'
        )
        generate_parser.add_argument(
            '--template',
            type=str,
            help='报告模板'
        )
        generate_parser.add_argument(
            '--output',
            type=str,
            help='输出文件路径'
        )
        generate_parser.add_argument(
            '--include-charts',
            action='store_true',
            help='包含图表'
        )
        
        # 批量生成报告
        batch_generate_parser = report_subparsers.add_parser(
            'batch-generate',
            help='批量生成报告'
        )
        batch_generate_parser.add_argument(
            '--input-dir',
            type=str,
            default='results',
            help='输入目录 (默认: results)'
        )
        batch_generate_parser.add_argument(
            '--pattern',
            type=str,
            default='*.json',
            help='文件模式 (默认: *.json)'
        )
        
        # 查看报告
        view_parser = report_subparsers.add_parser(
            'view',
            help='查看报告'
        )
        view_parser.add_argument(
            'report_file',
            type=str,
            help='报告文件路径'
        )
        view_parser.add_argument(
            '--browser',
            action='store_true',
            help='在浏览器中打开'
        )
    
    def _add_config_command(self):
        """添加配置命令"""
        config_parser = self.subparsers.add_parser(
            'config',
            help='配置管理',
            description='管理系统配置'
        )
        
        config_subparsers = config_parser.add_subparsers(
            dest='config_action',
            help='配置操作',
            metavar='ACTION'
        )
        
        # 显示配置
        show_parser = config_subparsers.add_parser(
            'show',
            help='显示当前配置'
        )
        show_parser.add_argument(
            '--section',
            type=str,
            help='显示特定配置段'
        )
        show_parser.add_argument(
            '--format',
            choices=['json', 'yaml', 'table'],
            default='table',
            help='输出格式 (默认: table)'
        )
        
        # 设置配置
        set_parser = config_subparsers.add_parser(
            'set',
            help='设置配置项'
        )
        set_parser.add_argument(
            'key',
            type=str,
            help='配置键'
        )
        set_parser.add_argument(
            'value',
            type=str,
            help='配置值'
        )
        
        # 重置配置
        reset_parser = config_subparsers.add_parser(
            'reset',
            help='重置配置'
        )
        reset_parser.add_argument(
            '--confirm',
            action='store_true',
            help='确认重置'
        )
        
        # 导入配置
        import_parser = config_subparsers.add_parser(
            'import',
            help='导入配置'
        )
        import_parser.add_argument(
            'file',
            type=str,
            help='配置文件路径'
        )
        import_parser.add_argument(
            '--merge',
            action='store_true',
            help='合并配置而非替换'
        )
        
        # 导出配置
        export_parser = config_subparsers.add_parser(
            'export',
            help='导出配置'
        )
        export_parser.add_argument(
            'file',
            type=str,
            help='输出文件路径'
        )
        export_parser.add_argument(
            '--format',
            choices=['json', 'yaml'],
            default='json',
            help='输出格式 (默认: json)'
        )
        
        # 配置向导
        wizard_parser = config_subparsers.add_parser(
            'wizard',
            help='配置向导'
        )
        wizard_parser.add_argument(
            '--template',
            choices=['basic', 'advanced', 'performance'],
            help='使用预设模板'
        )
    
    def _add_system_command(self):
        """添加系统命令"""
        system_parser = self.subparsers.add_parser(
            'system',
            help='系统管理',
            description='系统状态和维护'
        )
        
        system_subparsers = system_parser.add_subparsers(
            dest='system_action',
            help='系统操作',
            metavar='ACTION'
        )
        
        # 系统状态
        status_parser = system_subparsers.add_parser(
            'status',
            help='检查系统状态'
        )
        status_parser.add_argument(
            '--detailed',
            action='store_true',
            help='显示详细状态'
        )
        
        # 清理系统
        cleanup_parser = system_subparsers.add_parser(
            'cleanup',
            help='清理临时文件'
        )
        cleanup_parser.add_argument(
            '--dry-run',
            action='store_true',
            help='试运行，不实际删除'
        )
        cleanup_parser.add_argument(
            '--force',
            action='store_true',
            help='强制清理'
        )
        
        # 诊断系统
        diagnose_parser = system_subparsers.add_parser(
            'diagnose',
            help='诊断系统问题'
        )
        diagnose_parser.add_argument(
            '--fix',
            action='store_true',
            help='尝试自动修复问题'
        )
        
        # 更新系统
        update_parser = system_subparsers.add_parser(
            'update',
            help='更新系统组件'
        )
        update_parser.add_argument(
            '--check-only',
            action='store_true',
            help='仅检查更新'
        )
    
    def parse_args(self, args: List[str] = None) -> argparse.Namespace:
        """
        解析命令行参数
        
        Args:
            args: 参数列表，None表示使用sys.argv
            
        Returns:
            argparse.Namespace: 解析结果
        """
        try:
            parsed_args = self.parser.parse_args(args)
            
            # 验证参数
            self._validate_args(parsed_args)
            
            return parsed_args
            
        except SystemExit as e:
            # argparse会在错误时调用sys.exit()
            if e.code != 0:
                self.logger.error("命令行参数解析失败")
            raise
        except Exception as e:
            self.logger.error(f"参数解析错误: {e}")
            raise
    
    def _validate_args(self, args: argparse.Namespace):
        """
        验证参数
        
        Args:
            args: 解析的参数
        """
        # 验证配置文件
        if hasattr(args, 'config') and args.config:
            config_path = Path(args.config)
            if not config_path.exists():
                raise ValueError(f"配置文件不存在: {config_path}")
        
        # 验证输出目录
        if hasattr(args, 'output_dir') and args.output_dir:
            output_path = Path(args.output_dir)
            try:
                output_path.mkdir(parents=True, exist_ok=True)
            except Exception as e:
                raise ValueError(f"无法创建输出目录 {output_path}: {e}")
        
        # 验证文件参数
        file_args = ['file', 'report_file']
        for file_arg in file_args:
            if hasattr(args, file_arg) and getattr(args, file_arg):
                file_path = Path(getattr(args, file_arg))
                if not file_path.exists():
                    raise ValueError(f"文件不存在: {file_path}")
        
        # 验证数值参数
        numeric_args = {
            'concurrent': (1, 1000),
            'requests': (1, 100000),
            'timeout': (1, 3600),
            'max_workers': (1, 32)
        }
        
        for arg_name, (min_val, max_val) in numeric_args.items():
            if hasattr(args, arg_name) and getattr(args, arg_name) is not None:
                value = getattr(args, arg_name)
                if not (min_val <= value <= max_val):
                    raise ValueError(f"{arg_name} 必须在 {min_val} 到 {max_val} 之间")
    
    def print_help(self):
        """打印帮助信息"""
        self.parser.print_help()
    
    def get_command_help(self, command: str) -> str:
        """
        获取特定命令的帮助信息
        
        Args:
            command: 命令名称
            
        Returns:
            str: 帮助信息
        """
        if command in self.subparsers.choices:
            subparser = self.subparsers.choices[command]
            return subparser.format_help()
        else:
            return f"未知命令: {command}"
    
    def suggest_command(self, partial_command: str) -> List[str]:
        """
        建议命令
        
        Args:
            partial_command: 部分命令
            
        Returns:
            List[str]: 建议的命令列表
        """
        if not self.subparsers:
            return []
        
        commands = list(self.subparsers.choices.keys())
        suggestions = []
        
        for command in commands:
            if command.startswith(partial_command):
                suggestions.append(command)
        
        return suggestions


# 便捷函数
def create_command_parser(version: str = "1.0.0") -> CommandParser:
    """
    创建命令解析器实例
    
    Args:
        version: 程序版本
        
    Returns:
        CommandParser: 解析器实例
    """
    return CommandParser(version)


def parse_command_line(args: List[str] = None, version: str = "1.0.0") -> argparse.Namespace:
    """
    解析命令行参数
    
    Args:
        args: 参数列表
        version: 程序版本
        
    Returns:
        argparse.Namespace: 解析结果
    """
    parser = create_command_parser(version)
    return parser.parse_args(args)


def show_command_help(command: str = None, version: str = "1.0.0"):
    """
    显示命令帮助
    
    Args:
        command: 特定命令，None表示显示总体帮助
        version: 程序版本
    """
    parser = create_command_parser(version)
    
    if command:
        help_text = parser.get_command_help(command)
        print(help_text)
    else:
        parser.print_help()
"""
主引擎类

负责系统初始化、任务调度和生命周期管理的核心引擎。
"""

import asyncio
import signal
import sys
import threading
import time
from typing import Dict, Any, Optional, List, Callable
from pathlib import Path
from concurrent.futures import ThreadPoolExecutor, Future
from dataclasses import dataclass
from enum import Enum

try:
    from ..config.settings import config_manager
    from .logger import initialize_logger, get_logger, log_operation, logger_manager
except ImportError:
    # 如果相对导入失败，尝试绝对导入
    try:
        from ticket_grabber_test.config.settings import config_manager
        from ticket_grabber_test.core.logger import initialize_logger, get_logger, log_operation, logger_manager
    except ImportError:
        # 如果都失败，创建简单的替代品
        class SimpleConfig:
            def get(self, key, default=None):
                config_data = {
                    'system.name': 'Ticket Grabber Test System',
                    'system.version': '1.0.0',
                    'system.max_workers': 10,
                    'system.debug': False,
                    'logging.level': 'INFO',
                    'security.warning_message': '本系统仅供防御性安全测试使用'
                }
                return config_data.get(key, default)

            def validate_config(self):
                return True

        config_manager = SimpleConfig()

        import logging
        def initialize_logger(config):
            logging.basicConfig(level=logging.INFO)

        def get_logger(name):
            return logging.getLogger(name)

        def log_operation(operation, details, user="system", success=True):
            logger = logging.getLogger('operations')
            logger.info(f"Operation: {operation}, Success: {success}")

        logger_manager = None


class SystemState(Enum):
    """系统状态枚举"""
    INITIALIZING = "initializing"
    RUNNING = "running"
    PAUSED = "paused"
    STOPPING = "stopping"
    STOPPED = "stopped"
    ERROR = "error"


@dataclass
class TaskInfo:
    """任务信息"""
    task_id: str
    name: str
    description: str
    status: str
    created_at: float
    started_at: Optional[float] = None
    completed_at: Optional[float] = None
    error_message: Optional[str] = None


class TicketGrabberEngine:
    """抢票测试系统主引擎"""
    
    def __init__(self, config_dir: str = None):
        """
        初始化主引擎
        
        Args:
            config_dir: 配置文件目录
        """
        self.config_dir = config_dir
        self.state = SystemState.INITIALIZING
        self.logger = None
        self.executor = None
        self.tasks = {}
        self.task_counter = 0
        self.shutdown_event = threading.Event()
        self.startup_time = None
        
        # 性能监控
        self.performance_metrics = {
            'total_tasks': 0,
            'completed_tasks': 0,
            'failed_tasks': 0,
            'average_task_duration': 0.0,
            'system_uptime': 0.0
        }
        
        # 注册信号处理器
        self._register_signal_handlers()
    
    def _register_signal_handlers(self):
        """注册系统信号处理器"""
        def signal_handler(signum, frame):
            self.logger.info(f"接收到信号 {signum}，开始优雅关闭...")
            self.shutdown()
        
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)
    
    def initialize(self) -> bool:
        """
        初始化系统
        
        Returns:
            初始化是否成功
        """
        try:
            self.state = SystemState.INITIALIZING
            
            # 验证配置
            if not config_manager.validate_config():
                raise RuntimeError("配置验证失败")
            
            # 初始化日志系统
            log_config = config_manager.get('logging', {})
            initialize_logger(log_config)
            self.logger = get_logger('engine')
            
            # 显示安全警告
            warning_msg = config_manager.get('security.warning_message')
            if warning_msg:
                self.logger.warning("=== 安全警告 ===")
                self.logger.warning(warning_msg)
                self.logger.warning("===============")
            
            # 初始化线程池
            max_workers = config_manager.get('system.max_workers', 10)
            self.executor = ThreadPoolExecutor(max_workers=max_workers)
            
            # 创建必要的目录
            self._create_directories()
            
            # 记录系统信息
            self._log_system_info()
            
            self.startup_time = time.time()
            self.state = SystemState.RUNNING
            
            self.logger.info("系统初始化完成")
            log_operation("system_initialize", {"status": "success"}, success=True)
            
            return True
            
        except Exception as e:
            self.state = SystemState.ERROR
            if self.logger:
                self.logger.error(f"系统初始化失败: {e}")
                log_operation("system_initialize", {"error": str(e)}, success=False)
            return False
    
    def _create_directories(self):
        """创建必要的目录"""
        directories = [
            'logs',
            'reports',
            'data',
            'temp'
        ]
        
        for directory in directories:
            Path(directory).mkdir(parents=True, exist_ok=True)
            self.logger.debug(f"创建目录: {directory}")
    
    def _log_system_info(self):
        """记录系统信息"""
        system_info = {
            'name': config_manager.get('system.name'),
            'version': config_manager.get('system.version'),
            'python_version': sys.version,
            'max_workers': config_manager.get('system.max_workers'),
            'debug_mode': config_manager.get('system.debug', False)
        }
        
        self.logger.info("系统信息:")
        for key, value in system_info.items():
            self.logger.info(f"  {key}: {value}")
    
    def submit_task(self, task_func: Callable, *args, **kwargs) -> str:
        """
        提交任务到执行队列
        
        Args:
            task_func: 任务函数
            *args: 位置参数
            **kwargs: 关键字参数
            
        Returns:
            任务ID
        """
        if self.state != SystemState.RUNNING:
            raise RuntimeError(f"系统状态不允许提交任务: {self.state}")
        
        self.task_counter += 1
        task_id = f"task_{self.task_counter:06d}"
        
        # 创建任务信息
        task_info = TaskInfo(
            task_id=task_id,
            name=task_func.__name__,
            description=kwargs.get('description', ''),
            status='pending',
            created_at=time.time()
        )
        
        # 包装任务函数以添加监控
        def wrapped_task():
            try:
                task_info.status = 'running'
                task_info.started_at = time.time()
                
                self.logger.info(f"开始执行任务: {task_id} - {task_info.name}")
                result = task_func(*args, **kwargs)
                
                task_info.status = 'completed'
                task_info.completed_at = time.time()
                
                # 更新性能指标
                self.performance_metrics['completed_tasks'] += 1
                duration = task_info.completed_at - task_info.started_at
                self._update_average_duration(duration)
                
                self.logger.info(f"任务完成: {task_id} - 耗时: {duration:.2f}秒")
                log_operation("task_complete", {
                    "task_id": task_id,
                    "duration": duration
                }, success=True)
                
                return result
                
            except Exception as e:
                task_info.status = 'failed'
                task_info.error_message = str(e)
                task_info.completed_at = time.time()
                
                self.performance_metrics['failed_tasks'] += 1
                
                self.logger.error(f"任务失败: {task_id} - 错误: {e}")
                log_operation("task_failed", {
                    "task_id": task_id,
                    "error": str(e)
                }, success=False)
                
                raise
        
        # 提交任务
        future = self.executor.submit(wrapped_task)
        self.tasks[task_id] = {
            'info': task_info,
            'future': future
        }
        
        self.performance_metrics['total_tasks'] += 1
        
        self.logger.debug(f"任务已提交: {task_id}")
        return task_id
    
    def _update_average_duration(self, duration: float):
        """更新平均任务持续时间"""
        completed = self.performance_metrics['completed_tasks']
        current_avg = self.performance_metrics['average_task_duration']
        
        # 计算新的平均值
        new_avg = ((current_avg * (completed - 1)) + duration) / completed
        self.performance_metrics['average_task_duration'] = new_avg
    
    def get_task_status(self, task_id: str) -> Optional[Dict[str, Any]]:
        """
        获取任务状态
        
        Args:
            task_id: 任务ID
            
        Returns:
            任务状态信息
        """
        if task_id not in self.tasks:
            return None
        
        task_data = self.tasks[task_id]
        task_info = task_data['info']
        future = task_data['future']
        
        return {
            'task_id': task_info.task_id,
            'name': task_info.name,
            'description': task_info.description,
            'status': task_info.status,
            'created_at': task_info.created_at,
            'started_at': task_info.started_at,
            'completed_at': task_info.completed_at,
            'error_message': task_info.error_message,
            'is_done': future.done(),
            'is_cancelled': future.cancelled()
        }
    
    def get_all_tasks(self) -> List[Dict[str, Any]]:
        """获取所有任务状态"""
        return [self.get_task_status(task_id) for task_id in self.tasks.keys()]
    
    def get_performance_metrics(self) -> Dict[str, Any]:
        """获取性能指标"""
        if self.startup_time:
            self.performance_metrics['system_uptime'] = time.time() - self.startup_time
        
        return self.performance_metrics.copy()
    
    def pause(self):
        """暂停系统"""
        if self.state == SystemState.RUNNING:
            self.state = SystemState.PAUSED
            self.logger.info("系统已暂停")
            log_operation("system_pause", {}, success=True)
    
    def resume(self):
        """恢复系统"""
        if self.state == SystemState.PAUSED:
            self.state = SystemState.RUNNING
            self.logger.info("系统已恢复")
            log_operation("system_resume", {}, success=True)
    
    def shutdown(self):
        """关闭系统"""
        if self.state in [SystemState.STOPPING, SystemState.STOPPED]:
            return
        
        self.state = SystemState.STOPPING
        self.logger.info("开始关闭系统...")
        
        # 设置关闭事件
        self.shutdown_event.set()
        
        # 等待所有任务完成
        if self.executor:
            self.logger.info("等待任务完成...")
            self.executor.shutdown(wait=True)
        
        # 记录系统关闭
        if logger_manager:
            logger_manager.log_system_shutdown()
        
        self.state = SystemState.STOPPED
        self.logger.info("系统已关闭")
    
    def is_running(self) -> bool:
        """检查系统是否正在运行"""
        return self.state == SystemState.RUNNING
    
    def wait_for_shutdown(self):
        """等待系统关闭信号"""
        self.shutdown_event.wait()


# 全局引擎实例
engine: Optional[TicketGrabberEngine] = None


def get_engine() -> TicketGrabberEngine:
    """获取全局引擎实例"""
    global engine
    if engine is None:
        engine = TicketGrabberEngine()
    return engine


def initialize_engine(config_dir: str = None) -> bool:
    """
    初始化全局引擎
    
    Args:
        config_dir: 配置目录
        
    Returns:
        初始化是否成功
    """
    global engine
    engine = TicketGrabberEngine(config_dir)
    return engine.initialize()
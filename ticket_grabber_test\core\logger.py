"""
日志系统

支持多级别日志、文件输出、审计日志的完整日志管理系统。
"""

import logging
import logging.handlers
import os
import sys
from pathlib import Path
from typing import Optional, Dict, Any
from datetime import datetime
import json


class SecurityAuditLogger:
    """安全审计日志记录器"""
    
    def __init__(self, audit_file: str):
        """
        初始化审计日志记录器
        
        Args:
            audit_file: 审计日志文件路径
        """
        self.audit_file = Path(audit_file)
        self.audit_file.parent.mkdir(parents=True, exist_ok=True)
        
        # 创建审计日志记录器
        self.audit_logger = logging.getLogger('security_audit')
        self.audit_logger.setLevel(logging.INFO)
        
        # 创建文件处理器
        handler = logging.handlers.RotatingFileHandler(
            self.audit_file,
            maxBytes=10*1024*1024,  # 10MB
            backupCount=10,
            encoding='utf-8'
        )
        
        # 设置审计日志格式
        formatter = logging.Formatter(
            '%(asctime)s - AUDIT - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        handler.setFormatter(formatter)
        
        self.audit_logger.addHandler(handler)
        self.audit_logger.propagate = False
    
    def log_operation(self, operation: str, details: Dict[str, Any], 
                     user: str = "system", success: bool = True):
        """
        记录操作审计日志
        
        Args:
            operation: 操作类型
            details: 操作详情
            user: 操作用户
            success: 操作是否成功
        """
        audit_entry = {
            "timestamp": datetime.now().isoformat(),
            "user": user,
            "operation": operation,
            "success": success,
            "details": details
        }
        
        self.audit_logger.info(json.dumps(audit_entry, ensure_ascii=False))
    
    def log_security_event(self, event_type: str, severity: str, 
                          description: str, source_ip: str = None):
        """
        记录安全事件
        
        Args:
            event_type: 事件类型
            severity: 严重程度 (LOW, MEDIUM, HIGH, CRITICAL)
            description: 事件描述
            source_ip: 源IP地址
        """
        security_event = {
            "timestamp": datetime.now().isoformat(),
            "event_type": event_type,
            "severity": severity,
            "description": description,
            "source_ip": source_ip
        }
        
        self.audit_logger.warning(json.dumps(security_event, ensure_ascii=False))


class LoggerManager:
    """日志管理器"""
    
    def __init__(self, config: Dict[str, Any] = None):
        """
        初始化日志管理器
        
        Args:
            config: 日志配置
        """
        self.config = config or {}
        self.loggers = {}
        self.audit_logger = None
        
        # 创建日志目录
        log_dir = Path(self.config.get('file_path', 'logs/system.log')).parent
        log_dir.mkdir(parents=True, exist_ok=True)
        
        # 初始化主日志记录器
        self._setup_main_logger()
        
        # 初始化审计日志记录器
        if self.config.get('audit_log', True):
            audit_file = self.config.get('audit_file', 'logs/audit.log')
            self.audit_logger = SecurityAuditLogger(audit_file)
    
    def _setup_main_logger(self):
        """设置主日志记录器"""
        # 获取根日志记录器
        root_logger = logging.getLogger()
        root_logger.setLevel(getattr(logging, self.config.get('level', 'INFO')))
        
        # 清除现有处理器
        root_logger.handlers.clear()
        
        # 创建格式化器
        formatter = logging.Formatter(
            self.config.get('format', 
                          '%(asctime)s - %(name)s - %(levelname)s - %(message)s'),
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        
        # 添加控制台处理器
        if self.config.get('console_output', True):
            console_handler = logging.StreamHandler(sys.stdout)
            console_handler.setFormatter(formatter)
            root_logger.addHandler(console_handler)
        
        # 添加文件处理器
        file_path = self.config.get('file_path', 'logs/system.log')
        max_size = self._parse_size(self.config.get('max_file_size', '10MB'))
        backup_count = self.config.get('backup_count', 5)
        
        file_handler = logging.handlers.RotatingFileHandler(
            file_path,
            maxBytes=max_size,
            backupCount=backup_count,
            encoding='utf-8'
        )
        file_handler.setFormatter(formatter)
        root_logger.addHandler(file_handler)
    
    def _parse_size(self, size_str: str) -> int:
        """解析文件大小字符串"""
        size_str = size_str.upper()
        if size_str.endswith('KB'):
            return int(size_str[:-2]) * 1024
        elif size_str.endswith('MB'):
            return int(size_str[:-2]) * 1024 * 1024
        elif size_str.endswith('GB'):
            return int(size_str[:-2]) * 1024 * 1024 * 1024
        else:
            return int(size_str)
    
    def get_logger(self, name: str) -> logging.Logger:
        """
        获取指定名称的日志记录器
        
        Args:
            name: 日志记录器名称
            
        Returns:
            日志记录器实例
        """
        if name not in self.loggers:
            logger = logging.getLogger(name)
            self.loggers[name] = logger
        
        return self.loggers[name]
    
    def log_system_start(self):
        """记录系统启动"""
        logger = self.get_logger('system')
        logger.info("=== 抢票测试系统启动 ===")
        logger.info("系统版本: 1.0.0")
        logger.info("启动时间: %s", datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
        
        if self.audit_logger:
            self.audit_logger.log_operation(
                "system_start",
                {"version": "1.0.0", "startup_time": datetime.now().isoformat()},
                success=True
            )
    
    def log_system_shutdown(self):
        """记录系统关闭"""
        logger = self.get_logger('system')
        logger.info("=== 抢票测试系统关闭 ===")
        logger.info("关闭时间: %s", datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
        
        if self.audit_logger:
            self.audit_logger.log_operation(
                "system_shutdown",
                {"shutdown_time": datetime.now().isoformat()},
                success=True
            )
    
    def log_operation(self, operation: str, details: Dict[str, Any], 
                     user: str = "system", success: bool = True):
        """记录操作日志"""
        logger = self.get_logger('operations')
        
        if success:
            logger.info("操作成功: %s - %s", operation, details)
        else:
            logger.error("操作失败: %s - %s", operation, details)
        
        if self.audit_logger:
            self.audit_logger.log_operation(operation, details, user, success)
    
    def log_security_event(self, event_type: str, severity: str, 
                          description: str, source_ip: str = None):
        """记录安全事件"""
        logger = self.get_logger('security')
        
        log_method = {
            'LOW': logger.info,
            'MEDIUM': logger.warning,
            'HIGH': logger.error,
            'CRITICAL': logger.critical
        }.get(severity, logger.info)
        
        log_method("安全事件 [%s]: %s", event_type, description)
        
        if self.audit_logger:
            self.audit_logger.log_security_event(
                event_type, severity, description, source_ip
            )
    
    def log_performance_metrics(self, metrics: Dict[str, Any]):
        """记录性能指标"""
        logger = self.get_logger('performance')
        logger.info("性能指标: %s", json.dumps(metrics, ensure_ascii=False))
    
    def set_log_level(self, level: str):
        """设置日志级别"""
        log_level = getattr(logging, level.upper(), logging.INFO)
        logging.getLogger().setLevel(log_level)
        
        logger = self.get_logger('system')
        logger.info("日志级别已设置为: %s", level)


# 全局日志管理器实例
logger_manager: Optional[LoggerManager] = None


def initialize_logger(config: Dict[str, Any]):
    """
    初始化全局日志管理器
    
    Args:
        config: 日志配置
    """
    global logger_manager
    logger_manager = LoggerManager(config)
    logger_manager.log_system_start()


def get_logger(name: str = __name__) -> logging.Logger:
    """
    获取日志记录器
    
    Args:
        name: 日志记录器名称
        
    Returns:
        日志记录器实例
    """
    if logger_manager is None:
        # 如果日志管理器未初始化，使用默认配置
        initialize_logger({})
    
    return logger_manager.get_logger(name)


def log_operation(operation: str, details: Dict[str, Any], 
                 user: str = "system", success: bool = True):
    """记录操作日志的便捷函数"""
    if logger_manager:
        logger_manager.log_operation(operation, details, user, success)


def log_security_event(event_type: str, severity: str, 
                      description: str, source_ip: str = None):
    """记录安全事件的便捷函数"""
    if logger_manager:
        logger_manager.log_security_event(event_type, severity, description, source_ip)
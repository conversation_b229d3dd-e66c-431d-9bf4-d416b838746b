"""
场景执行器

提供命令行接口和批量执行功能
"""

import argparse
import sys
import time
import json
from typing import List, Dict, Any, Optional
from pathlib import Path

from .scenario_manager import ScenarioManager, create_scenario_manager
from .base_scenario import ScenarioStatus, ScenarioResult

try:
    from ..core.logger import get_logger, setup_logging
except ImportError:
    import logging
    def get_logger(name):
        return logging.getLogger(name)
    
    def setup_logging(level="INFO"):
        logging.basicConfig(level=getattr(logging, level))


class ScenarioExecutor:
    """
    场景执行器
    
    提供命令行接口和批量执行功能
    """
    
    def __init__(self, config_file: str = None, log_level: str = "INFO"):
        """
        初始化执行器
        
        Args:
            config_file: 配置文件路径
            log_level: 日志级别
        """
        setup_logging(log_level)
        self.logger = get_logger("ScenarioExecutor")
        
        self.manager = ScenarioManager(config_file)
        self.results: List[Dict[str, Any]] = []
        
        # 设置回调
        self.manager.on_scenario_complete = self._on_scenario_complete
        self.manager.on_scenario_error = self._on_scenario_error
        
        self.logger.info("场景执行器初始化完成")
    
    def list_scenarios(self):
        """列出所有可用场景"""
        print("\n=== 可用场景 ===")
        scenarios = self.manager.list_scenarios()
        
        if not scenarios:
            print("没有可用的场景")
            return
        
        for i, scenario_name in enumerate(scenarios, 1):
            config = self.manager.get_scenario_config(scenario_name)
            if config:
                print(f"{i:2d}. {scenario_name}")
                print(f"     描述: {config.get('description', 'N/A')}")
                print(f"     类型: {config.get('attack_type', 'N/A')}")
                print(f"     目标: {config.get('target_url', 'N/A')}")
                print()
    
    def list_scenario_groups(self):
        """列出所有场景组"""
        print("\n=== 场景组 ===")
        groups = self.manager.list_scenario_groups()
        
        if not groups:
            print("没有可用的场景组")
            return
        
        for i, group_name in enumerate(groups, 1):
            scenarios = self.manager.get_scenario_group(group_name)
            print(f"{i:2d}. {group_name}")
            print(f"     包含场景: {', '.join(scenarios) if scenarios else 'N/A'}")
            print()
    
    def execute_scenario(self, scenario_name: str, custom_config: Dict[str, Any] = None, wait: bool = True) -> bool:
        """
        执行单个场景
        
        Args:
            scenario_name: 场景名称
            custom_config: 自定义配置
            wait: 是否等待完成
            
        Returns:
            bool: 是否成功
        """
        print(f"\n=== 执行场景: {scenario_name} ===")
        
        scenario = self.manager.execute_scenario_by_name(scenario_name, custom_config, blocking=False)
        if not scenario:
            print(f"❌ 场景启动失败: {scenario_name}")
            return False
        
        print(f"✅ 场景启动成功: {scenario_name}")
        print(f"   场景ID: {scenario.scenario_id}")
        print(f"   目标URL: {scenario.config.target_url}")
        print(f"   最大请求数: {scenario.config.max_requests}")
        print(f"   并发用户: {scenario.config.concurrent_users}")
        
        if wait:
            return self._wait_and_monitor_scenario(scenario)
        
        return True
    
    def execute_scenario_group(self, group_name: str, sequential: bool = False, wait: bool = True) -> bool:
        """
        执行场景组
        
        Args:
            group_name: 场景组名称
            sequential: 是否顺序执行
            wait: 是否等待完成
            
        Returns:
            bool: 是否成功
        """
        print(f"\n=== 执行场景组: {group_name} ===")
        
        scenarios = self.manager.execute_scenario_group(group_name, sequential=sequential)
        if not scenarios:
            print(f"❌ 场景组启动失败: {group_name}")
            return False
        
        print(f"✅ 场景组启动成功: {group_name}")
        print(f"   启动场景数: {len(scenarios)}")
        
        for scenario in scenarios:
            print(f"   - {scenario.config.name} ({scenario.scenario_id[:8]})")
        
        if wait:
            return self._wait_and_monitor_scenarios(scenarios)
        
        return True
    
    def execute_batch(self, batch_config: List[Dict[str, Any]], sequential: bool = False, wait: bool = True) -> bool:
        """
        批量执行场景
        
        Args:
            batch_config: 批量配置
            sequential: 是否顺序执行
            wait: 是否等待完成
            
        Returns:
            bool: 是否成功
        """
        print(f"\n=== 批量执行场景 ===")
        print(f"   场景数量: {len(batch_config)}")
        print(f"   执行模式: {'顺序' if sequential else '并发'}")
        
        scenarios = self.manager.execute_batch(batch_config, sequential=sequential)
        if not scenarios:
            print("❌ 批量执行失败")
            return False
        
        print(f"✅ 批量执行启动成功")
        print(f"   启动场景数: {len(scenarios)}")
        
        if wait:
            return self._wait_and_monitor_scenarios(scenarios)
        
        return True
    
    def _wait_and_monitor_scenario(self, scenario) -> bool:
        """等待并监控单个场景"""
        print(f"\n--- 监控场景执行: {scenario.config.name} ---")
        
        last_progress = 0
        start_time = time.time()
        
        while scenario.status == ScenarioStatus.RUNNING:
            time.sleep(2)  # 每2秒检查一次
            
            progress = scenario.get_progress()
            current_progress = progress['progress_percent']
            
            # 显示进度（每10%显示一次）
            if current_progress - last_progress >= 10:
                elapsed = time.time() - start_time
                print(f"   进度: {current_progress:.1f}% | "
                      f"请求: {progress['metrics']['total_requests']} | "
                      f"成功: {progress['metrics']['successful_requests']} | "
                      f"耗时: {elapsed:.1f}s")
                last_progress = current_progress
        
        # 显示最终结果
        self._show_scenario_result(scenario)
        return scenario.result == ScenarioResult.SUCCESS
    
    def _wait_and_monitor_scenarios(self, scenarios) -> bool:
        """等待并监控多个场景"""
        print(f"\n--- 监控场景执行 ---")
        
        scenario_ids = [s.scenario_id for s in scenarios]
        
        while True:
            time.sleep(3)  # 每3秒检查一次
            
            active_count = 0
            total_requests = 0
            total_success = 0
            
            for scenario in scenarios:
                if scenario.status == ScenarioStatus.RUNNING:
                    active_count += 1
                
                progress = scenario.get_progress()
                total_requests += progress['metrics']['total_requests']
                total_success += progress['metrics']['successful_requests']
            
            if active_count == 0:
                break
            
            success_rate = (total_success / total_requests * 100) if total_requests > 0 else 0
            print(f"   活动场景: {active_count}/{len(scenarios)} | "
                  f"总请求: {total_requests} | "
                  f"成功率: {success_rate:.1f}%")
        
        # 显示所有场景结果
        print(f"\n--- 执行结果 ---")
        success_count = 0
        
        for scenario in scenarios:
            self._show_scenario_result(scenario)
            if scenario.result == ScenarioResult.SUCCESS:
                success_count += 1
        
        print(f"\n总体结果: {success_count}/{len(scenarios)} 个场景成功")
        return success_count == len(scenarios)
    
    def _show_scenario_result(self, scenario):
        """显示场景结果"""
        result_icon = {
            ScenarioResult.SUCCESS: "✅",
            ScenarioResult.PARTIAL: "⚠️",
            ScenarioResult.BLOCKED: "🚫",
            ScenarioResult.ERROR: "❌",
            ScenarioResult.TIMEOUT: "⏰"
        }
        
        icon = result_icon.get(scenario.result, "❓")
        
        print(f"{icon} {scenario.config.name}")
        print(f"   结果: {scenario.result.value if scenario.result else 'N/A'}")
        print(f"   耗时: {scenario.metrics.duration:.2f}s")
        print(f"   请求: {scenario.metrics.total_requests}")
        print(f"   成功: {scenario.metrics.successful_requests}")
        print(f"   成功率: {scenario.metrics.success_rate:.1%}")
        
        if scenario.error_message:
            print(f"   错误: {scenario.error_message}")
    
    def _on_scenario_complete(self, scenario):
        """场景完成回调"""
        result = {
            'scenario_id': scenario.scenario_id,
            'name': scenario.config.name,
            'result': scenario.result.value if scenario.result else None,
            'summary': scenario.get_summary()
        }
        self.results.append(result)
    
    def _on_scenario_error(self, scenario, error_message):
        """场景错误回调"""
        self.logger.error(f"场景执行错误: {scenario.config.name} - {error_message}")
    
    def show_status(self):
        """显示当前状态"""
        print("\n=== 当前状态 ===")
        
        active_scenarios = self.manager.list_active_scenarios()
        completed_scenarios = self.manager.list_completed_scenarios()
        
        print(f"活动场景: {len(active_scenarios)}")
        for scenario in active_scenarios:
            print(f"  - {scenario['name']} ({scenario['status']})")
        
        print(f"已完成场景: {len(completed_scenarios)}")
        for scenario in completed_scenarios[-5:]:  # 显示最近5个
            print(f"  - {scenario['config']['name']} ({scenario['result']})")
        
        # 显示统计信息
        stats = self.manager.get_statistics()
        print(f"\n统计信息:")
        print(f"  总场景数: {stats['total_scenarios']}")
        print(f"  成功率: {stats['success_rate']:.1%}")
        print(f"  平均耗时: {stats['average_duration']:.2f}s")
    
    def export_results(self, output_dir: str = "results"):
        """导出结果"""
        print(f"\n=== 导出结果到: {output_dir} ===")
        self.manager.export_results(output_dir)
        print("✅ 结果导出完成")
    
    def interactive_mode(self):
        """交互模式"""
        print("\n=== 交互模式 ===")
        print("输入 'help' 查看可用命令")
        
        while True:
            try:
                command = input("\n> ").strip().lower()
                
                if command == 'help':
                    self._show_help()
                elif command == 'list':
                    self.list_scenarios()
                elif command == 'groups':
                    self.list_scenario_groups()
                elif command == 'status':
                    self.show_status()
                elif command == 'export':
                    self.export_results()
                elif command in ['quit', 'exit', 'q']:
                    break
                elif command.startswith('run '):
                    scenario_name = command[4:].strip()
                    self.execute_scenario(scenario_name)
                elif command.startswith('group '):
                    group_name = command[6:].strip()
                    self.execute_scenario_group(group_name)
                else:
                    print("未知命令，输入 'help' 查看帮助")
                    
            except KeyboardInterrupt:
                print("\n\n中断执行")
                break
            except Exception as e:
                print(f"错误: {e}")
        
        print("退出交互模式")
    
    def _show_help(self):
        """显示帮助信息"""
        print("""
可用命令:
  help          - 显示此帮助信息
  list          - 列出所有可用场景
  groups        - 列出所有场景组
  status        - 显示当前状态
  export        - 导出结果
  run <name>    - 执行指定场景
  group <name>  - 执行指定场景组
  quit/exit/q   - 退出
        """)
    
    def shutdown(self):
        """关闭执行器"""
        self.logger.info("正在关闭场景执行器...")
        self.manager.shutdown()
        self.logger.info("场景执行器已关闭")


def create_argument_parser():
    """创建命令行参数解析器"""
    parser = argparse.ArgumentParser(
        description="抢票测试场景执行器",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例:
  # 列出所有场景
  python -m scenarios.scenario_executor --list
  
  # 执行单个场景
  python -m scenarios.scenario_executor --scenario basic_high_frequency
  
  # 执行场景组
  python -m scenarios.scenario_executor --group basic_attacks
  
  # 批量执行（从文件）
  python -m scenarios.scenario_executor --batch batch_config.json
  
  # 交互模式
  python -m scenarios.scenario_executor --interactive
        """
    )
    
    # 基本选项
    parser.add_argument('--config', '-c', 
                       help='配置文件路径 (默认: config/test_scenarios.json)')
    parser.add_argument('--log-level', choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'],
                       default='INFO', help='日志级别')
    parser.add_argument('--output', '-o', default='results',
                       help='结果输出目录')
    
    # 执行选项
    group = parser.add_mutually_exclusive_group()
    group.add_argument('--list', '-l', action='store_true',
                      help='列出所有可用场景')
    group.add_argument('--list-groups', action='store_true',
                      help='列出所有场景组')
    group.add_argument('--scenario', '-s',
                      help='执行指定场景')
    group.add_argument('--group', '-g',
                      help='执行指定场景组')
    group.add_argument('--batch', '-b',
                      help='批量执行（JSON配置文件）')
    group.add_argument('--interactive', '-i', action='store_true',
                      help='进入交互模式')
    
    # 执行控制
    parser.add_argument('--sequential', action='store_true',
                       help='顺序执行（仅适用于组和批量）')
    parser.add_argument('--no-wait', action='store_true',
                       help='不等待执行完成')
    parser.add_argument('--target-url',
                       help='覆盖目标URL')
    parser.add_argument('--max-requests', type=int,
                       help='覆盖最大请求数')
    
    return parser


def main():
    """主函数"""
    parser = create_argument_parser()
    args = parser.parse_args()
    
    # 创建执行器
    executor = ScenarioExecutor(args.config, args.log_level)
    
    try:
        # 准备自定义配置
        custom_config = {}
        if args.target_url:
            custom_config['target_url'] = args.target_url
        if args.max_requests:
            custom_config['max_requests'] = args.max_requests
        
        wait = not args.no_wait
        
        # 执行相应操作
        if args.list:
            executor.list_scenarios()
        
        elif args.list_groups:
            executor.list_scenario_groups()
        
        elif args.scenario:
            success = executor.execute_scenario(args.scenario, custom_config, wait)
            sys.exit(0 if success else 1)
        
        elif args.group:
            success = executor.execute_scenario_group(args.group, args.sequential, wait)
            sys.exit(0 if success else 1)
        
        elif args.batch:
            try:
                with open(args.batch, 'r', encoding='utf-8') as f:
                    batch_config = json.load(f)
                
                success = executor.execute_batch(batch_config, args.sequential, wait)
                sys.exit(0 if success else 1)
                
            except Exception as e:
                print(f"❌ 批量配置文件加载失败: {e}")
                sys.exit(1)
        
        elif args.interactive:
            executor.interactive_mode()
        
        else:
            parser.print_help()
        
        # 导出结果
        if args.output:
            executor.export_results(args.output)
    
    except KeyboardInterrupt:
        print("\n\n用户中断执行")
        sys.exit(1)
    
    except Exception as e:
        print(f"❌ 执行失败: {e}")
        sys.exit(1)
    
    finally:
        executor.shutdown()


if __name__ == '__main__':
    main()
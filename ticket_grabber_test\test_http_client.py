#!/usr/bin/env python3
"""
HTTP客户端和会话管理模块测试脚本
"""

import asyncio
import time
import json
from typing import List, Dict, Any

# 导入模块
try:
    from modules.http_client import TicketGrabber, AsyncTicketGrabber, RetryStrategy, RequestResult
    from core.session_manager import SessionManager, get_session_manager
    from core.logger import get_logger
except ImportError as e:
    print(f"导入错误: {e}")
    print("请确保在项目根目录运行此脚本")
    exit(1)


def test_sync_http_client():
    """测试同步HTTP客户端"""
    print("\n=== 测试同步HTTP客户端 ===")
    
    client = TicketGrabber()
    
    # 测试GET请求
    print("1. 测试GET请求...")
    result = client.get('https://httpbin.org/get')
    print(f"   状态: {result.status}")
    print(f"   状态码: {result.status_code}")
    print(f"   响应时间: {result.response_time:.2f}s")
    
    # 测试POST请求
    print("2. 测试POST请求...")
    data = {'test': 'data', 'timestamp': time.time()}
    result = client.post('https://httpbin.org/post', json=data)
    print(f"   状态: {result.status}")
    print(f"   状态码: {result.status_code}")
    print(f"   响应时间: {result.response_time:.2f}s")
    
    # 测试统计信息
    print("3. 统计信息:")
    stats = client.get_stats()
    for key, value in stats.items():
        print(f"   {key}: {value}")
    
    client.close()
    print("   同步客户端测试完成 ✓")


async def test_async_http_client():
    """测试异步HTTP客户端"""
    print("\n=== 测试异步HTTP客户端 ===")
    
    client = AsyncTicketGrabber(max_concurrent=10)
    
    # 测试单个异步请求
    print("1. 测试异步GET请求...")
    result = await client.get('https://httpbin.org/get')
    print(f"   状态: {result.status}")
    print(f"   状态码: {result.status_code}")
    print(f"   响应时间: {result.response_time:.2f}s")
    
    # 测试异步POST请求
    print("2. 测试异步POST请求...")
    data = {'async_test': 'data', 'timestamp': time.time()}
    result = await client.post('https://httpbin.org/post', data=data)
    print(f"   状态: {result.status}")
    print(f"   状态码: {result.status_code}")
    print(f"   响应时间: {result.response_time:.2f}s")
    
    # 测试批量异步请求
    print("3. 测试批量异步请求...")
    requests_data = [
        {'method': 'GET', 'url': 'https://httpbin.org/delay/1'},
        {'method': 'GET', 'url': 'https://httpbin.org/delay/1'},
        {'method': 'GET', 'url': 'https://httpbin.org/delay/1'},
        {'method': 'POST', 'url': 'https://httpbin.org/post', 'data': {'batch': 'test'}},
        {'method': 'GET', 'url': 'https://httpbin.org/status/200'}
    ]
    
    start_time = time.time()
    results = await client.batch_requests(requests_data)
    batch_time = time.time() - start_time
    
    print(f"   批量请求完成: {len(results)} 个请求")
    print(f"   总耗时: {batch_time:.2f}s")
    
    success_count = sum(1 for r in results if r.status == 'success')
    print(f"   成功: {success_count}/{len(results)}")
    
    # 测试统计信息
    print("4. 异步客户端统计信息:")
    stats = client.get_stats()
    for key, value in stats.items():
        print(f"   {key}: {value}")
    
    print("   异步客户端测试完成 ✓")


async def test_retry_strategy():
    """测试重试策略"""
    print("\n=== 测试重试策略 ===")
    
    retry_strategy = RetryStrategy(max_retries=3, base_delay=0.5, backoff_factor=2)
    
    # 测试同步重试
    print("1. 测试同步重试装饰器...")
    
    @retry_strategy.retry_decorator(exceptions=(Exception,))
    def failing_function(attempt_count=[0]):
        attempt_count[0] += 1
        print(f"   尝试 {attempt_count[0]}")
        if attempt_count[0] < 3:
            raise Exception(f"模拟失败 {attempt_count[0]}")
        return f"成功在第 {attempt_count[0]} 次尝试"
    
    try:
        result = failing_function()
        print(f"   结果: {result}")
    except Exception as e:
        print(f"   最终失败: {e}")
    
    # 测试异步重试
    print("2. 测试异步重试装饰器...")
    
    @retry_strategy.retry_decorator(exceptions=(Exception,))
    async def async_failing_function(attempt_count=[0]):
        attempt_count[0] += 1
        print(f"   异步尝试 {attempt_count[0]}")
        if attempt_count[0] < 2:
            raise Exception(f"异步模拟失败 {attempt_count[0]}")
        return f"异步成功在第 {attempt_count[0]} 次尝试"
    
    try:
        result = await async_failing_function()
        print(f"   异步结果: {result}")
    except Exception as e:
        print(f"   异步最终失败: {e}")
    
    print("   重试策略测试完成 ✓")


def test_session_manager():
    """测试会话管理器"""
    print("\n=== 测试会话管理器 ===")
    
    # 创建会话管理器
    manager = SessionManager(sync_pool_size=5, async_pool_size=5)
    
    # 测试同步会话
    print("1. 测试同步会话管理...")
    result = manager.make_sync_request('GET', 'https://httpbin.org/get')
    print(f"   同步请求状态: {result.status}")
    print(f"   状态码: {result.status_code}")
    
    # 测试多个同步请求
    print("2. 测试多个同步请求...")
    for i in range(3):
        result = manager.make_sync_request('GET', f'https://httpbin.org/delay/1')
        print(f"   请求 {i+1} 状态: {result.status}")
    
    # 测试统计信息
    print("3. 会话管理器统计信息:")
    stats = manager.get_manager_stats()
    print(f"   同步池: {stats['sync_pool']}")
    print(f"   异步池: {stats['async_pool']}")
    
    manager.close()
    print("   会话管理器测试完成 ✓")


async def test_async_session_manager():
    """测试异步会话管理器"""
    print("\n=== 测试异步会话管理器 ===")
    
    manager = SessionManager(sync_pool_size=3, async_pool_size=5)
    
    # 测试异步会话
    print("1. 测试异步会话管理...")
    result = await manager.make_async_request('GET', 'https://httpbin.org/get')
    print(f"   异步请求状态: {result.status}")
    print(f"   状态码: {result.status_code}")
    
    # 测试批量异步请求
    print("2. 测试批量异步请求...")
    requests_data = [
        {'method': 'GET', 'url': 'https://httpbin.org/delay/1'},
        {'method': 'GET', 'url': 'https://httpbin.org/delay/1'},
        {'method': 'POST', 'url': 'https://httpbin.org/post', 'data': {'test': 'batch'}}
    ]
    
    results = await manager.batch_async_requests(requests_data)
    success_count = sum(1 for r in results if r.status == 'success')
    print(f"   批量请求结果: {success_count}/{len(results)} 成功")
    
    # 最终统计
    print("3. 最终统计信息:")
    stats = manager.get_manager_stats()
    print(f"   总会话数: {stats['total_sessions']}")
    print(f"   总请求数: {stats['total_requests']}")
    print(f"   总错误数: {stats['total_errors']}")
    
    manager.close()
    print("   异步会话管理器测试完成 ✓")


def test_performance():
    """性能测试"""
    print("\n=== 性能测试 ===")
    
    # 同步性能测试
    print("1. 同步客户端性能测试...")
    client = TicketGrabber()
    
    start_time = time.time()
    for i in range(5):
        result = client.get('https://httpbin.org/get')
    sync_time = time.time() - start_time
    
    print(f"   同步5个请求耗时: {sync_time:.2f}s")
    client.close()
    
    # 异步性能测试
    async def async_performance_test():
        print("2. 异步客户端性能测试...")
        client = AsyncTicketGrabber(max_concurrent=10)
        
        requests_data = [
            {'method': 'GET', 'url': 'https://httpbin.org/get'}
            for _ in range(5)
        ]
        
        start_time = time.time()
        results = await client.batch_requests(requests_data)
        async_time = time.time() - start_time
        
        print(f"   异步5个并发请求耗时: {async_time:.2f}s")
        print(f"   性能提升: {sync_time/async_time:.1f}x")
        
        return results
    
    asyncio.run(async_performance_test())
    print("   性能测试完成 ✓")


async def main():
    """主测试函数"""
    print("开始HTTP客户端和会话管理模块测试...")
    print("=" * 50)
    
    try:
        # 基础功能测试
        test_sync_http_client()
        await test_async_http_client()
        await test_retry_strategy()
        
        # 会话管理测试
        test_session_manager()
        await test_async_session_manager()
        
        # 性能测试
        test_performance()
        
        print("\n" + "=" * 50)
        print("所有测试完成！ ✅")
        
    except Exception as e:
        print(f"\n测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True


if __name__ == "__main__":
    # 运行测试
    success = asyncio.run(main())
    exit(0 if success else 1)